const mongoose = require('mongoose');
const Slug = require('slug');
const { omitBy, isNil } = require('lodash');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const { rolesVar } = require('../../../../config/vars');

const statusOpts = ['public', 'private'];
const typeOpts = ['post', 'document', 'schedule', 'report'];
const originalOpts = ['Biểu mẫu', 'Văn bản', 'Tài liệu', '<PERSON>ông văn đến', 'Công văn đi', ''];
const ArticleFolder = require('./article.folder.model');


/**
 * Article Schema
 * @private
 */
const articleSchema = new mongoose.Schema(
  {
    avatar: {
      type: String,
      trim: true,
      default: '',
    },
    title: { // Trích yếu
      type: String,
      trim: true,
      default: '',
    },
    slug: {
      type: String,
      trim: true,
      default: '',
    },
    number: {// Số hiệu
      type: String,
      trim: true,
      default: '',
    },
    folder: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ArticleFolder',
    },
    document_type: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ArticleType',
    },
    field: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ArticleField',
    },
    department: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ArticleDepartment',
    },
    sign: {// Người ký
      type: String,
      trim: true,
      default: '',
    },
    type: {
      type: String,
      enum: typeOpts,
      default: 'post',
    },
    categories: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'ArticleCategory',
      },
    ],
    tags: [
      {
        type: String,
      },
    ],
    info: {
      type: String,
      default: '',
    },
    content: {
      type: String,
      default: '',
    },
    order: {
      type: Number,
      default: 0,
    },
    status: {
      type: String,
      enum: statusOpts,
      default: 'public',
    },
    original: {
      type: String,
      enum: originalOpts,
      default: '',
    },
    is_active: {
      type: Boolean,
      default: true,
    },
    public_date: {// Ban hành
      type: Date,
    },
    release_date: {
      type: Date,
    },
    attachment: [{
      type: String,
      trim: true,
    }],
    allow_users: [
      {
        type: Number,
      },
    ],
    created_by: {
      type: String,
      default: '',
    },
    updated_by: {
      type: String,
      default: '',
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  },
);

/**
 * Methods
 */
articleSchema.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'avatar',
      'title',
      'info',
      'slug',
      'type',
      'original',
      'content',
      'categories',
      'tags',
      'order',
      'status',
      'is_active',
      'created_at',
      'public_date',
      'release_date',
      'number',
      'folder',
      'document_type',
      'department',
      'field',
      'sign',
      'attachment',
      'allow_users',
      'created_by',
    ];

    fields.forEach((field) => {
      if (field === 'categories' && this.categories.length > 0) {
        transformed.categories = this.categories;
        for (let i = 0; i < transformed.categories.length; i += 1) {
          transformed.categories[i].key =
            transformed.categories[i]._id.toString();
        }
      } else {
        transformed[field] = this[field];
      }
    });

    return transformed;
  },
});

/**
 * Add your
 * - pre-save hooks
 * - validations
 * - virtuals
 */
articleSchema.pre('save', async function save(next) {
  try {
    const doc = this;

    /*
    slug
    */
    if (this.isModified('title')) {
      doc.slug = Slug(doc.title).toLowerCase();
    }
    next();
  } catch (error) {
    next(error);
  }
});

/**
 * Statics
 */
articleSchema.statics = {
  statusOpts,
  typeOpts,
  originalOpts,
  /**
   * Get Article
   *
   * @param {ObjectId} id - The objectId of Article.
   * @returns {Promise<Article, APIError>}
   */
  async get(id) {
    try {
      const Article = await this.findById(id)
        .populate('categories', '_id title slug key')
        .populate('folder', '_id title')
        .populate('document_type', '_id title')
        .populate('field', '_id title')
        .populate('department', '_id title')
        .exec();

      if (Article) {
        return Article;
      }

      throw new APIError({
        message: 'Article does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * List articles in descending order of 'created_at' timestamp.
   * @param {number} skip - Number of articles to be skipped.
   * @param {number} limit - Limit number of articles to be returned.
   * @returns {Promise<Article[]>}
   */
  async list({
    page = 1,
    perPage = 30,
    sort,
    title,
    categories,
    status,
    is_active,
    slug,
    tags,
    public_date,
    number,
    document_type,
    folder,
    field,
    year,
    sign,
    type,
    release_date,
    original,
    department,
    parent_folder,
    user,
    role,
  }) {
    try {
      if (year) {
        public_date = { $gte: `${year}-01-01`, $lte: `${year}-12-31` };
      } else if (public_date) {
        public_date = {
          $gte: `${public_date}T00:00:00.000Z`,
          $lte: `${public_date}T23:59:00.000Z`,
        };
      } else {
        public_date = { $ne: null };
      }
      if (release_date) {
        release_date = {
          $gte: `${release_date}T00:00:00.000Z`,
          $lte: `${release_date}T23:59:00.000Z`,
        };
      }
      perPage = parseInt(perPage, 10);
      page = parseInt(page, 10);
      const options = omitBy(
        {
          title: new RegExp(title, 'i'),
          categories,
          status,
          is_active,
          tags,
          slug,
          public_date,
          release_date,
          number,
          type,
          folder,
          document_type,
          field,
          department,
          original,
        },
        isNil,
      );
      // if (department) {
      //   options.department = new RegExp(department, 'i');
      // }
      if (sign) {
        options.sign = new RegExp(sign, 'i');
      }
      if (parent_folder) {
        const children = (await ArticleFolder.findById(parent_folder)).children;
        options.folder = { $in: children };
      }
      const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
      let query = {};
      if (!rolesVar.admin.includes(role)) {
        query = {
          $and: [
            options,
            {
              $or: [
                { status: 'public' },
                { status: 'private', allow_users: user },
              ],
            },
          ],
        };
      } else {
        query = options;
      }
      const result = this.find(query)
        .populate('categories', '_id title slug key')
        .populate('folder', '_id title')
        .populate('document_type', '_id title')
        .populate('field', '_id title')
        .populate('department', '_id title')
        .sort(sortOpts);
      if (perPage > -1) {
        result.skip(perPage * (page - 1)).limit(perPage);
      }
      return result.exec();
    } catch (error) {
      throw error;
    }
  },

  /**
   * Count articles.
   * @returns {Promise<Number>}
   */
  async count({
    title,
    categories,
    status,
    is_active,
    slug,
    tags,
    public_date,
    number,
    document_type,
    folder,
    field,
    sign,
    type,
    year,
    release_date,
    original,
    department,
    parent_folder,
    user,
    role,
  }) {
    if (year) {
      public_date = { $gte: `${year}-01-01`, $lte: `${year}-12-31` };
    } else if (public_date) {
      public_date = {
        $gte: `${public_date}T00:00:00.000Z`,
        $lte: `${public_date}T23:59:00.000Z`,
      };
    } else {
      public_date = { $ne: null };
    }
    if (release_date) {
      release_date = {
        $gte: `${release_date}T00:00:00.000Z`,
        $lte: `${release_date}T23:59:00.000Z`,
      };
    }
    const options = omitBy(
      {
        title: new RegExp(title, 'i'),
        categories,
        status,
        is_active,
        slug,
        tags,
        public_date,
        number,
        type,
        release_date,
        folder,
        document_type,
        field,
        original,
        department,
      },
      isNil,
    );
    if (sign) {
      options.sign = new RegExp(sign, 'i');
    }
    if (parent_folder) {
      const parent = await ArticleFolder.findById(parent_folder);
      if (parent) {
        const children = parent.children;
        options.folder = { $in: children };
      } else {
        throw new APIError({
          message: 'Parent folder does not exist',
          status: httpStatus.NOT_FOUND,
        });
      }
    }
    let query = {};
    if (!rolesVar.admin.includes(role)) {
      query = {
        $and: [
          options,
          {
            $or: [
              { status: 'public' },
              { status: 'private', allow_users: user },
            ],
          },
        ],
      };
    } else {
      query = options;
    }
    return this.find(query).count();
  },
};

/**
 * @typedef Article
 */
module.exports = mongoose.model('Article', articleSchema);
