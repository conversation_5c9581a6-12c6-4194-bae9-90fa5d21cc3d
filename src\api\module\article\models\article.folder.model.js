// 23/11/2021
const mongoose = require('mongoose');
const Slug = require('slug');
const { omitBy, isNil } = require('lodash');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

/**
 * ArticleFolder Schema
 * @private
 */
const articleFolderSchema = new mongoose.Schema({
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ArticleFolder',
    default: null,
  },
  children: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ArticleFolder',
  }],
  title: {
    type: String,
    trim: true,
    default: '',
  },
  drive_id: {
    type: String,
    trim: true,
    default: '',
  },
  slug: {
    type: String,
    trim: true,
    default: '',
  },
  order: {
    type: Number,
    default: 0,
  },
  is_active: {
    type: Boolean,
    default: true,
  },
  created_by: {
    type: String,
    default: '',
  },
  updated_by: {
    type: String,
    default: '',
  },
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
});

/**
 * Methods
 */
articleFolderSchema.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'title',
      'slug',
      'drive_id',
      'parent',
      'children',
      'order',
      'is_active',
      'created_at',
      'created_by',
    ];

    fields.forEach((field) => {
      transformed[field] = this[field];
    });

    return transformed;
  },
});

/**
 * Add your
 * - pre-save hooks
 * - validations
 * - virtuals
 */
articleFolderSchema.pre('save', async function save(next) {
  try {
    const doc = this;

    /*
    slug
    */
    if (this.isModified('title')) {
      doc.slug = Slug(doc.title).toLowerCase();
    }
    next();
  } catch (error) {
    next(error);
  }
});

/**
 * Add your
 * - post-save hooks
 */
articleFolderSchema.post('save', async (doc, next) => {
  try {
    const ArticleFolder = mongoose.model('ArticleFolder', articleFolderSchema);
    if (doc.parent) {
      const parentFolder = await ArticleFolder.findById(doc.parent);
      if (parentFolder && !parentFolder.children.includes(doc._id)) {
        await ArticleFolder.findOneAndUpdate({ _id: doc.parent }, { $push: { children: doc._id } });
      }
    }

    return next();
  } catch (error) {
    return next(error);
  }
});

/**
 * Statics
 */
articleFolderSchema.statics = {
  /**
   * Get ArticleFolder
   *
   * @param {ObjectId} id - The objectId of ArticleFolder.
   * @returns {Promise<ArticleFolder, APIError>}
   */
  async get(id) {
    try {
      const ArticleFolder = await this.findById(id)
        .populate('parent', '_id title slug drive_id')
        // .populate('children', '_id title slug drive_id')
        .populate({
          path: 'children',
          select: '_id title slug drive_id children',
          populate: {
            path: 'children',
            select: '_id title slug drive_id',
          },
        })
        .exec();

      if (ArticleFolder) {
        return ArticleFolder;
      }

      throw new APIError({
        message: 'ArticleFolder does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * List articles in descending order of 'created_at' timestamp.
   * @param {number} skip - Number of articles to be skipped.
   * @param {number} limit - Limit number of articles to be returned.
   * @returns {Promise<ArticleFolder[]>}
   */
  async list({
    page = 1, perPage = 30,
    sort,
    title,
    parent,
    is_active,
    slug,
    is_parent,
  }) {
    try {
      const options = omitBy({
        title: new RegExp(title, 'i'),
        parent,
        is_active,
        slug,
      }, isNil);
      const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
      if (is_parent) {
        options.parent = null;
      }
      const result = this.find(options)
        .populate('parent', '_id title slug drive_id')
        // .populate('children', '_id title slug drive_id')
        .populate({
          path: 'children',
          select: '_id title slug drive_id children',
          populate: {
            path: 'children',
            select: '_id title slug drive_id',
          },
        })
        .sort(sortOpts);
      if (perPage > -1) {
        result.skip(perPage * (page - 1)).limit(perPage);
      }
      return result.exec();
    } catch (error) {
      throw error;
    }
  },

  /**
   * Count articles.
   * @returns {Promise<Number>}
   */
  async count({
    title,
    parent,
    is_active,
    slug,
    is_parent,
  }) {
    const options = omitBy({
      title: new RegExp(title, 'i'),
      parent,
      is_active,
      slug,
    }, isNil);
    if (is_parent) {
      options.parent = null;
    }
    return this.find(options).count();
  },
};

/**
 * @typedef ArticleFolder
 */
module.exports = mongoose.model('ArticleFolder', articleFolderSchema);
