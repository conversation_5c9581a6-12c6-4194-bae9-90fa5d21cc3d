const mongoose = require('mongoose');
const httpStatus = require('http-status');
const Slug = require('slug');
const { omitBy, isNil } = require('lodash');
const APIError = require('../../../utils/APIError');

// const language = ['vn', 'en'];

/**
 * ArticleCategory Schema
 * @private
 */
const articleCategory = new mongoose.Schema({
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ArticleCategory',
  },
  key: {
    type: String,
  },
  children: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ArticleCategory',
  }],
  title: {
    type: String,
    maxlength: 128,
    trim: true,
  },
  slug: {
    type: String,
    trim: true,
  },
  avatar: {
    type: String,
    trim: true,
  },
  // language: {
  //   type: String,
  //   enum: language,
  //   default: 'vn',
  // },
  info: {
    type: String,
  },
  order: {
    type: Number,
    default: 0,
  },
  is_active: {
    type: Boolean,
    default: true,
  },
  // created_by: {
  //   type: mongoose.Schema.Types.ObjectId,
  //   ref: 'User',
  // },
  // updated_by: {
  //   type: mongoose.Schema.Types.ObjectId,
  //   ref: 'User',
  // },
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
});

/**
 * Add your
 * - pre-save hooks
 * - validations
 * - virtuals
 */
articleCategory.pre('save', async function save(next) {
  try {
    /*
    slug
    */
    if (this.isModified('title')) {
      this.slug = Slug(this.title).toLowerCase();
    }

    if (this.isModified('parent')) {
      const ArticleCategory = mongoose.model('ArticleCategory', articleCategory);
      await ArticleCategory.updateOne({ _id: this.parent }, { $pull: { children: this._id } });
      await ArticleCategory.updateOne({ _id: this.parent }, { $push: { children: this._id } });
    }

    next();
  } catch (error) {
    next(error);
  }
});

/**
 * Methods
 */
articleCategory.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'title',
      'slug',
      'avatar',
      'parent',
      'children',
      'info',
      'order',
      'is_active',
      'created_at',
      'key',
    ];

    fields.forEach((field) => {
      if (field === 'children' && this.children.length > 0) {
        transformed[field] = this[field];
      } else if (field !== 'children') {
        transformed[field] = this[field];
      }
    });

    return transformed;
  },
});

/**
 * Statics
 */
articleCategory.statics = {
  /**
   * Get ArticleCategory
   *
   * @param {ObjectId} id - The objectId of ArticleCategory.
   * @returns {Promise<Post, APIError>}
   */
  async get(id) {
    try {
      let ArticleCategory;

      if (mongoose.Types.ObjectId.isValid(id)) {
        ArticleCategory = await this.findById(id)
          .populate('parent', '_id title slug avatar info')
          .populate('children', '_id title slug avatar info key')
          .exec();
      }
      if (ArticleCategory) {
        return ArticleCategory;
      }

      throw new APIError({
        message: 'ArticleCategory does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * List categories in descending order of 'createdAt' timestamp.
   *
   * @param {number} skip - Number of categories to be skipped.
   * @param {number} limit - Limit number of categories to be returned.
   * @returns {Promise<User[]>}
   */
  list({
    page = 1, perPage = 30, sort, title, slug, is_parent = 'true',
  }) {
    perPage = parseInt(perPage, 10);
    page = parseInt(page, 10);
    let options = omitBy({ title, slug }, isNil);
    if (is_parent === 'true') {
      options = Object.assign(options, { parent: null });
    }
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options)
      .populate('parent', '_id title slug avatar info')
      .populate('children', '_id title slug avatar info')
      .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  },

  /**
   * Count radios.
   * @returns {Promise<Number>}
   */
  async count({
    title, slug, is_parent,
  }) {
    let options = omitBy({ title, slug }, isNil);
    if (is_parent === 'true') {
      options = Object.assign(options, { parent: null });
    }
    return this.find(options).count();
  },
};

/**
 * @typedef ArticleCategory
 */
module.exports = mongoose.model('ArticleCategory', articleCategory);
