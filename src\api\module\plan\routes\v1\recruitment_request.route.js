const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/recruitment_request.model.controller');
const { authorize, ADMIN } = require('../../../../middlewares/auth');
// const { addUser, acl_middleware } = require('../../../../middlewares/auth_acl');
const {
  listRecruitmentRequests,
  createRecruitmentRequest,
  updateRecruitmentRequest,
  deleteRecruitmentRequest,
} = require('../../validations/recruitment_request.validation');
// const { route } = require('../../../../../config/vars');

const router = express.Router();
// const resource = `/v1${route.plan}`;

/**
 * Load plan when API with id route parameter is hit
 */
router.param('id', controller.load);

router
  .route('/')
  /**
   * @api {get} v1/plan/plan List Posts
   * @apiDescription Get list
   * @apiVersion 1.0.0
   * @apiName ListPosts
   * @apiGroup Post
   * @apiPermission admin
   *
   * @apiHeader {String} Authorization  User's access token
   *
   * @apiParam  {Number{1-}}         [page=1]     List page
   * @apiParam  {Number{1-100}}      [perPage=1]  Per page
   * @apiParam  {String}             [title]      Title
   * @apiParam  {String}             [category]   category
   * @apiParam  {String}             [status]     status
   * @apiParam  {String}             [sort]       sort ex:{"id": -1}
   *
   * @apiSuccess {Number} total Total posts.
   * @apiSuccess {Object[]} docs List of posts.
   */
  .get(authorize(), validate(listRecruitmentRequests), controller.list)
  /**
   * @api {plan} v1/plan/plan Create Post
   * @apiDescription Create a new plan
   * @apiVersion 1.0.0
   * @apiName CreatePost
   * @apiGroup Post
   * @apiPermission admin
   *
   * @apiHeader {String} Authorization  User's access token
   *
   * @apiParam  {String}                  avatar         avatar
   * @apiParam  {String}                  title          title
   * @apiParam  {String}                  category       category
   * @apiParam  {Array}                   related        related
   * @apiParam  {Array}                   tag            tag
   * @apiParam  {String}                  info           info
   * @apiParam  {String}                  content        content
   * @apiParam  {String=public, approve, new, draft}     [status]   status
   * @apiParam  {Number}                  order          order
   *
   * @apiSuccess (Created 201) {String}  id           id
   * @apiSuccess (Created 201) {String}  avatar       avatar
   * @apiSuccess (Created 201) {String}  title        title
   * @apiSuccess (Created 201) {String}  category     category
   * @apiSuccess (Created 201) {Array}   related      related
   * @apiSuccess (Created 201) {Array}   info         info
   * @apiSuccess (Created 201) {String}  content      content
   * @apiSuccess (Created 201) {Number}  order        order
   * @apiSuccess (Created 201) {Date}    createdAt    Timestamp
   *
   * @apiError (Bad Request 400)   ValidationError  Some parameters may contain invalid values
   * @apiError (Unauthorized 401)  Unauthorized     Only authenticated users can create the data
   * @apiError (Forbidden 403)     Forbidden        Only admins can create the data
   */
  .post(authorize(ADMIN), validate(createRecruitmentRequest), controller.create);

router
  .route('/:id')
  /**
   * @api {get} v1/plan/plan/:id Get Post
   * @apiDescription Get plan information
   * @apiVersion 1.0.0
   * @apiName GetPost
   * @apiGroup Post
   * @apiPermission plan
   *
   * @apiSuccess {String}  id           id
   * @apiSuccess {String}  avatar       avatar
   * @apiSuccess {String}  title        title
   * @apiSuccess {String}  category     category
   * @apiSuccess {Array}   related      related
   * @apiSuccess {Array}   info         info
   * @apiSuccess {String}  content      content
   * @apiSuccess {Number}  order        order
   * @apiSuccess {Date}    createdAt    Timestamp
   *
   * @apiError (Not Found 404)    NotFound     Post does not exist
   */
  .get(authorize(), controller.get)
  /**
   * @api {patch} v1/plan/plan/:id Update Post
   * @apiDescription Update some fields of a plan document
   * @apiVersion 1.0.0
   * @apiName UpdatePost
   * @apiGroup Post
   * @apiPermission plan
   *
   * @apiHeader {String} Authorization  User's access token
   *
   * @apiParam  {String}                  avatar         avatar
   * @apiParam  {String}                  title          title
   * @apiParam  {String}                  category       category
   * @apiParam  {Array}                   related        related
   * @apiParam  {Array}                   tag            tag
   * @apiParam  {String}                  info           info
   * @apiParam  {String}                  content        content
   * @apiParam  {String=public, approve, new, draft}   [status]   status
   * @apiParam  {Number}                  order          order
   *
   * @apiSuccess {String}  id           id
   * @apiSuccess {String}  avatar       avatar
   * @apiSuccess {String}  title        title
   * @apiSuccess {String}  category     category
   * @apiSuccess {Array}   related      related
   * @apiSuccess {Array}   info         info
   * @apiSuccess {String}  content      content
   * @apiSuccess {Number}  order        order
   * @apiSuccess {Date}    createdAt    Timestamp
   *
   * @apiError (Bad Request 400)  ValidationError  Some parameters may contain invalid values
   * @apiError (Unauthorized 401) Unauthorized Only authenticated users can modify the data
   * @apiError (Forbidden 403)    Forbidden    Only admins can modify the data
   * @apiError (Not Found 404)    NotFound     Post does not exist
   */
  .patch(authorize(ADMIN), validate(updateRecruitmentRequest), controller.update)
  /**
   * @api {delete} v1/plan/plan/:id Delete Post
   * @apiDescription Delete a plan
   * @apiVersion 1.0.0
   * @apiName DeletePost
   * @apiGroup Post
   * @apiPermission plan
   *
   * @apiHeader {String} Authorization  User's access token
   *
   * @apiSuccess (No Content 204)  Successfully deleted
   *
   * @apiError (Unauthorized 401) Unauthorized  Only authenticated users can delete the data
   * @apiError (Forbidden 403)    Forbidden     Only user with same id or admins can delete the data
   * @apiError (Not Found 404)    NotFound      Post does not exist
   */
  .delete(authorize(ADMIN), validate(deleteRecruitmentRequest), controller.remove);

module.exports = router;
