const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/work.report.controller');
const {
  authorize,
  // ADMIN,
  HR_DEPT,
} = require('../../../../middlewares/auth');
const {
  listWorkReports,
  createWorkReport,
  updateWorkReport,
  deleteWorkReport,
} = require('../../validations/work.report.validation');

const router = express.Router();
router.param('id', controller.load);

router
  .route('/')
  .get(authorize(), validate(listWorkReports), controller.list)
  .post(authorize(), validate(createWorkReport), controller.create);

router
  .route('/manage')
  .get(authorize(), validate(listWorkReports), controller.manage);

router
  .route('/:id')
  .get(authorize(), controller.get)
  .patch(authorize(HR_DEPT), validate(updateWorkReport), controller.update)
  .delete(authorize(HR_DEPT), validate(deleteWorkReport), controller.remove);

module.exports = router;
