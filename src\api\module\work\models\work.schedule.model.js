// 23/9/2021
const _ = require('lodash');
const moment = require('moment');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

const isApprovedOpts = [[0, 1]];// (0: Disapprove, 1: Approve)
const isDisplayOpts = [[0, 1]];// 0: Private, 1: Public
const fields = {
  table: 't500',
  _id: 'pt500',
  year_id: 'fh050', // ID Năm học
  is_approved: 'tn501', // Trạng thái xét duyệt
  approved_at: 'td501', // Ngày xét duyệt
  is_display: 'tn501p', // Trạng thái hiển thị (giao diện sinh viên)
  public_date: 'td501p', // Ngày công bố
  work: 'tv502', // Công việc
  participant: 'tv503', // Thành phần tham dự
  chairman: 'tv504', // Chủ trì
  content_department: 'tv505', // Đơn vị / Phòng ban chuẩn bị nội dung
  place: 'tv506', // Địa điểm họp
  start_date: 'td507', // Ngày diễn ra
  start_time: 'tv508', // Thời gian bắt đầu
  end_time: 'tv509', // Thời gian kết thúc
  description: 'tl538', // Mô tả
  note: 'tl539', // Ghi chú

  deleted_by: 'tl544', // Email người xóa
  deleted_at: 'tl545', // Thời gian xóa
  created_by: 'tl547', // Email người tạo
  updated_by: 'tl549', // Email người cập nhật
  updated_at: 'tl548',
  created_at: 'tl546',
};

const schema = (sequelize, DataTypes) => {
  const workScheduleSchema = sequelize.define('WorkSchedule', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.is_approved]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: isApprovedOpts,
      },
    },
    [fields.is_display]: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      validate: {
        isIn: isDisplayOpts,
      },
    },
    [fields.approved_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.public_date]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.work]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.participant]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.chairman]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.content_department]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.place]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.start_date]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.start_time]: {
      type: DataTypes.STRING(5),
      defaultValue: null,
    },
    [fields.end_time]: {
      type: DataTypes.STRING(5),
      defaultValue: null,
    },
    [fields.description]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.note]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const WorkSchedule = workScheduleSchema;

  workScheduleSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await WorkSchedule.countItem({
      [fields.deleted_at]: null,
    });
    const schedule = await WorkSchedule.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.is_approved, 'is_approved'],
        [fields.approved_at, 'approved_at'],
        [fields.is_display, 'is_display'],
        [fields.public_date, 'public_date'],
        [fields.work, 'work'],
        [fields.participant, 'participant'],
        [fields.chairman, 'chairman'],
        [fields.content_department, 'content_department'],
        [fields.place, 'place'],
        [fields.description, 'description'],
        [fields.note, 'note'],
        [fields.start_date, 'start_date'],
        [fields.start_time, 'start_time'],
        [fields.end_time, 'end_time'],
        [fields.created_by, 'created_by'],
        [fields.created_at, 'created_at'],
      ],
      where: {
        [fields.deleted_at]: null,
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: schedule };
  };

  workScheduleSchema.countItem = async (query) => {
    const count = await WorkSchedule.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  workScheduleSchema.get = async (id) => {
    try {
      const schedule = await WorkSchedule.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.is_approved, 'is_approved'],
          [fields.approved_at, 'approved_at'],
          [fields.is_display, 'is_display'],
          [fields.public_date, 'public_date'],
          [fields.work, 'work'],
          [fields.participant, 'participant'],
          [fields.chairman, 'chairman'],
          [fields.content_department, 'content_department'],
          [fields.place, 'place'],
          [fields.start_date, 'start_date'],
          [fields.start_time, 'start_time'],
          [fields.end_time, 'end_time'],
          [fields.description, 'description'],
          [fields.note, 'note'],
          [fields.deleted_by, 'deleted_by'],
          [fields.deleted_at, 'deleted_at'],
          [fields.created_at, 'created_at'],
          [fields.created_by, 'created_by'],
          [fields.updated_at, 'updated_at'],
          [fields.updated_by, 'updated_by'],
        ],
        where: {
          [fields._id]: id,
        },
      });
      if (schedule) {
        return schedule;
      }
      throw new APIError({
        message: 'Schedule does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  workScheduleSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await WorkSchedule.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  workScheduleSchema.remove = async ({ id, userId }) => {
    try {
      const result = await WorkSchedule.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: userId,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  return workScheduleSchema;
};

module.exports = {
  schema,
  fields,
  opts: {
    isApprovedOpts,
    isDisplayOpts,
  },
};
