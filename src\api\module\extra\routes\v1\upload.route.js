const express = require('express');
const controller = require('../../controllers/upload.controller');
const { authorize } = require('../../../../middlewares/auth');


const router = express.Router();

router.route('/avatar')
  .post(authorize(), controller.avatar);

router.route('/document')
  .post(authorize(), controller.uploadDocument);

router.route('/external-image')
  .post(controller.externalImage);


module.exports = router;
