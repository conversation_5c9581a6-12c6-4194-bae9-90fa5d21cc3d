// 28/11/2023
const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/employee.file.controller');
const {
  authorize,
  // ADMIN,
  // HR_DEPT,
} = require('../../../../middlewares/auth');
const {
  listEmployeeFiles,
  updateEmployeeFile,
  deleteEmployeeFile,
} = require('../../validations/employee.file.validation');

const router = express.Router();

router.param('id', controller.load);

router
  .route('/')
  .get(authorize(), validate(listEmployeeFiles), controller.list)
  .post(authorize(), controller.create);

router
  .route('/:id')
  .get(authorize(), controller.get)
  .patch(authorize(), validate(updateEmployeeFile), controller.update)
  .delete(authorize(), validate(deleteEmployeeFile), controller.remove);

module.exports = router;
