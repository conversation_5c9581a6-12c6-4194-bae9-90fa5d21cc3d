// 24/11/2021
/* eslint-disable consistent-return */
const fs = require('fs');
const readline = require('readline');
const { google } = require('googleapis');
const { googleService } = require('./vars');
const httpStatus = require('http-status');
const APIError = require('../api/utils/APIError');

// If modifying these scopes, delete token.json.
const SCOPES = googleService.domain;
const TOKEN_PATH = googleService.token_path;
const CREDENTIALS_PATH = googleService.credentials_path;
const DRIVE_ID = googleService.drive_id;
const DRIVE_DOCUMENT_ID = googleService.drive_document_id;
let auth;

// lấy file credentials.json
fs.readFile(CREDENTIALS_PATH, (err, content) => {
  if (err) { return console.log('Error loading client secret file:', err); }
  // Authorize a client with credentials, then call the Google Drive API.
  authorize(JSON.parse(content));
});

/**
 * Create an OAuth2 client with the given credentials, and then execute the
 * given callback function.
 * @param {Object} credentials The authorization client credentials.
 * @param {function} callback The callback to call with the authorized client.
 */
const authorize = (credentials) => {
  const { client_secret, client_id, redirect_uris } = credentials.installed;
  const oAuth2Client = new google.auth.OAuth2(
    client_id,
    client_secret,
    redirect_uris[0],
  );

  // Check if we have previously stored a token.
  fs.readFile(TOKEN_PATH, (err, token) => {
    if (err) { return getAccessToken(oAuth2Client); }
    oAuth2Client.setCredentials(JSON.parse(token));
    auth = oAuth2Client;
  });
};

/**
 * Get and store new token after prompting for user authorization, and then
 * execute the given callback with the authorized OAuth2 client.
 * @param {google.auth.OAuth2} oAuth2Client The OAuth2 client to get token for.
 * @param {getEventsCallback} callback The callback for the authorized client.
 */
const getAccessToken = (oAuth2Client) => {
  const authUrl = oAuth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: SCOPES,
  });
  console.log('Authorize this app by visiting this url:', authUrl);
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });
  rl.question('Enter the code from that page here: ', (code) => {
    rl.close();
    oAuth2Client.getToken(code, (err, token) => {
      if (err) return console.error('Error retrieving access token', err);
      oAuth2Client.setCredentials(token);
      // Store the token to disk for later program executions
      fs.writeFile(TOKEN_PATH, JSON.stringify(token), (error) => {
        if (error) return console.error(error);
        console.log('Token stored to', TOKEN_PATH);
      });
      auth = oAuth2Client;
    });
  });
};

const getFile = async (fileId = 'xxx') => {
  try {
    const drive = google.drive({ version: 'v3', auth });
    const file = await drive.files.get({
      fileId,
      fields: 'id, name, webViewLink, mimeType, webContentLink',
      includeItemsFromAllDrives: true,
      supportsAllDrives: true,
      driveId: DRIVE_ID,
      corpora: 'drive',
    });
    return file.data;
  } catch (error) {
    return null;
  }
};

const uploadFile = async ({ name, parent = DRIVE_DOCUMENT_ID, filePath }) => {
  try {
    const drive = google.drive({ version: 'v3', auth });
    const fileMetadata = {
      name,
      parents: [parent],
    };
    const media = {
      // mimeType: 'text/pdf',
      body: fs.createReadStream(filePath), // Reading the file from our server
    };
    const uploading = await drive.files.create({
      resource: fileMetadata,
      media,
      supportsAllDrives: true,
    });
    return uploading.data;
  } catch (error) {
    return error;
  }
};

const createFolder = async ({ name, parent = DRIVE_ID }) => {
  try {
    const drive = google.drive({ version: 'v3', auth });
    const fileMetadata = {
      name,
      mimeType: 'application/vnd.google-apps.folder',
      parents: [parent],
    };

    const folder = await drive.files.create({
      resource: fileMetadata,
      fields: 'id, name',
      supportsAllDrives: true,
    });
    return folder.data;
  } catch (error) {
    throw new APIError({
      message: error.message,
      status: httpStatus.BAD_REQUEST,
    });
  }
};

const trashFolder = async ({ fileId }) => {
  try {
    const drive = google.drive({ version: 'v3', auth });
    const file = await drive.files.update({
      fileId,
      includeItemsFromAllDrives: true,
      supportsAllDrives: true,
      requestBody: {
        trashed: true,
      },
    });
    return file.data;
  } catch (error) {
    return error.message;
  }
};

const publicFile = async (fileId) => {
  try {
    const drive = google.drive({ version: 'v3', auth });

    const result = await drive.permissions.create({
      fileId,
      supportsAllDrives: true,
      requestBody: {
        role: 'reader',
        type: 'anyone',
      },
    });
    return result.data;
  } catch (error) {
    throw new APIError({
      message: error.message,
      status: httpStatus.BAD_REQUEST,
    });
  }
};

// const listFiles = async () => {
//   const drive = google.drive({ version: 'v3', auth });
//   drive.files.list({
//     pageSize: 10,
//     fields: 'nextPageToken, files(id, name)',
//     supportsAllDrives: true,
//     includeItemsFromAllDrives: true,
//     corpora: 'drive',
//     driveId: DRIVE_ID,
//     q: 'trashed = false',
//   }, (err, res) => {
//     if (err) return console.log(`The API returned an error: ${err}`);
//     const files = res.data.files;
//     if (files.length) {
//       console.log('Files:');
//       files.map((file) => {
//         console.log(`${file.name} (${file.id})`);
//       });
//     } else {
//       console.log('No files found.');
//     }
//   });
// };

module.exports = {
  getFile,
  uploadFile,
  createFolder,
  publicFile,
  // listFiles,
  trashFolder,
};
