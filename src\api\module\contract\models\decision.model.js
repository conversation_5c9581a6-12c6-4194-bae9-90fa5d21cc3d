module.exports = (sequelize, DataTypes) => {
  const decisionSchema = sequelize.define('Decision', {
    pm200: {
      // ID Chi tiết
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    fm100: {
      // ID Nhân viên / giảng viên liên quan
      type: DataTypes.INTEGER,
      defaultValue: null,
    },
    fn400: {
      // ID Khoa / phòng ban (bảng N450)
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    fn450: {
      // ID Chức vụ (bảng N400)
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    mv202: {
      // Số quyết định
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    mv203: {
      // Ngày quyết định
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    mn204: {
      // Loại quyết định
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    md205: {
      // Ngày có hiệu lực
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    mv206: {
      // Tên phòng ban, khoa, cá nhân đề nghị ra quyết định
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    mn207: {
      // Hưởng lương %
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    md208: {
      // Ngày nâng lương
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    md209: {
      // Ngày chấm dứt hợp đồng
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    md210: {
      // Ngày tạo quyết định
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    md211: {
      // Ngày ký quyết định
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    mv241: {
      // File đính kèm
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    ml241: {
      // Nội dung quyết định (dạng text cho chỉnh sửa)
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    ml247: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    ml244: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    ml245: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    ml249: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: 'm200',
    createdAt: 'ml246',
    updatedAt: 'ml248',
  });

  return decisionSchema;
};
