
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const Workforce = require('../models/workforce.model');

/**
 * Load workforce and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const workforce = await Workforce.get(id);
    req.locals = { workforce };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get workforce
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.workforce.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Create new workforce
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user._id;
    const workforce = new Workforce(req.body);
    const saved = await workforce.save();

    jsonSuccess(saved.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing workforce
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const workforce = Object.assign(req.locals.workforce, req.body);

  workforce.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get workforce list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await Workforce.count(req.query);
    const workforces = await Workforce.list(req.query);
    const transformed = workforces.map(workforce => workforce.transform());
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete workforce
 * @public
 */
exports.remove = (req, res, next) => {
  const { workforce } = req.locals;

  workforce.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};

