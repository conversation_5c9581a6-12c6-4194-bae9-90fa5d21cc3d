const Joi = require('joi');

module.exports = {

  // GET /v1/department/department
  listDepartments: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      name: Joi.string(),
      order_way: Joi.string().valid(...['asc', 'desc']),
      order_by: Joi.string(),
      type: Joi.number().valid(...[0, 1]),
    }),
  },

  // POST /v1/department/department
  createDepartment: {
    body: Joi.object({
      name_vn: Joi.string().required(),
      name: Joi.string(),
      code: Joi.string(),
      type: Joi.number(),
      // is_active: Joi.bool(),
    }),
  },

  // PATCH /v1/department/department/:id
  updateDepartment: {
    body: Joi.object({
      name_vn: Joi.string(),
      name: Joi.string(),
      code: Joi.string(),
      type: Joi.number(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/department/department/:id
  deleteDepartment: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
