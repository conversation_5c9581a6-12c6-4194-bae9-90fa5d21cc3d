/* eslint-disable no-await-in-loop */
const _ = require('lodash');
const moment = require('moment');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const db = require('../../../../config/mysql');
const { fields } = require('../models/leave.model');
const { success: jsonSuccess } = require('../../../middlewares/success');

const Leave = db.leave;
const Employee = db.employee;
const Department = db.department;
const UserPermission = db.userPermission;
const Notification = db.notification;

const leaveCounter = (start_at, end_at) => ((end_at - start_at) / 86400000) + 1;

exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const { user } = req;
    data.employee = user._id;
    data.department = user.department;
    data.created_by = user.email;
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });
    const leave = Leave.build({
      ...dbData,
    });
    const savedLeave = await leave.save();
    const headers = await UserPermission.listDepartmentHead(user.department);

    headers.forEach(async (element) => {
      const header = element.dataValues.employee;
      const start_at = moment(new Date(data.start_at)).format('DD/MM/YYYY');
      const notificationData = {
        employee: header,
        content: `${user.last_name} ${user.first_name} đã xin nghỉ phép vào ngày ${start_at}`,
        type: 'leave',
        created_by: user.email,
      };
      await Notification.create({ ...notificationData });
    });

    jsonSuccess(savedLeave, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    const { user } = req;

    query.employee = user._id;

    const leave = await Leave.list({
      ...query,
      employeeModel: Employee,
      departmentModel: Department,
    });

    jsonSuccess(leave, req, res);
  } catch (error) {
    next(error);
  }
};

// quản lý chỉ dành cho quản trị viên
exports.manage = async (req, res, next) => {
  try {
    const { query } = req;
    const { user } = req;
    const permissions = await UserPermission.checkDepartmentHead(user._id);// list quyền
    const departments = [];// list phòng ban có quyền quản lý
    if (permissions.length > 0) {
      for (let i = 0; i < permissions.length; i += 1) {
        const item = permissions[i].dataValues;
        const department = item.permission_ui_function_code.slice(17);
        departments.push(parseInt(department, 10));
      }
    }
    if (user.role !== 2 && user.role !== 1) {
      if (departments.length > 0) {
        query.department = departments;
      } else {
        query.employee = user._id;
      }
    }

    const leave = await Leave.list({
      ...query,
      employeeModel: Employee,
      departmentModel: Department,
    });

    jsonSuccess(leave, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const { user } = req;
    const { body } = req;
    const permissions = await UserPermission.checkDepartmentHead(user._id);
    if (permissions.length < 1) {
      throw new APIError({
        message: 'User doesn\'t have permission',
        status: httpStatus.FORBIDDEN,
      });
    } else {
      const departments = [];
      for (let i = 0; i < permissions.length; i += 1) {
        const item = permissions[i].dataValues;
        const department = item.permission_ui_function_code.slice(17);
        departments.push(parseInt(department, 10));
      }
      const leave = await Leave.get(req.params.id);
      if (departments.includes(leave.dataValues.department)) {
        await Leave.patch({
          id: req.params.id,
          data: body,
        });
        if (
          body.is_approved
          && body.is_approved === 1
          && body.is_approved !== leave.dataValues.is_approved
        ) {
          // tạo notification khi nghỉ phép đc duyệt
          const start_at = moment(new Date(leave.dataValues.start_at)).format('DD/MM/YYYY');
          const notificationData = {
            employee: leave.dataValues.employee,
            content: `Đơn xin nghỉ phép của bạn vào ngày ${start_at} đã được ${user.last_name} ${user.first_name} phê duyệt`,
            type: 'leave',
            created_by: user.email,
          };
          await Notification.create({ ...notificationData });

          // trừ ngày nghỉ phép
          const employee = await Employee.get({
            departmentModel: Department,
            id: leave.dataValues.employee,
          });
          await Employee.patch({
            id: leave.dataValues.employee,
            data: {
              leave_balance_days: employee.dataValues.leave_balance_days
                - leaveCounter(leave.dataValues.start_at, leave.dataValues.end_at),
            },
          });
        }
      } else {
        throw new APIError({
          message: 'User doesn\'t have permission',
          status: httpStatus.FORBIDDEN,
        });
      }
    }

    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await Leave.remove({
      id: req.params.id,
      employee: req.user._id,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.manageCreate = async (req, res, next) => {
  try {
    const data = req.body;
    const { user } = req;
    const leaveDay = leaveCounter(new Date(data.start_at), new Date(data.end_at));
    data.created_by = user.email;
    data.department = 23;
    if (data.is_all) {
      data.employees = [];
      const employees = await Employee.list({
        perPage: -1,
        is_lecturer: 0,
        work_status: 0,
        departmentModel: Department,
      });
      for (let i = 0; i < employees.data.length; i += 1) {
        const item = employees.data[i].dataValues;
        data.employees.push(item._id);
      }
    }
    for (let i = 0; i < data.employees.length; i += 1) {
      const item = data.employees[i];
      const dbData = { [fields.employee]: item, [fields.is_approved]: 2 };
      _.forEach(data, (value, key) => {
        if (key !== 'employees' || key !== 'is_all') {
          dbData[fields[key]] = value;
        }
      });
      const leave = Leave.build({
        ...dbData,
      });
      await leave.save();
      // // trừ ngày nghỉ phép
      const employee = await Employee.get({
        departmentModel: Department,
        id: item,
      });
      await Employee.patch({
        id: item,
        data: {
          leave_balance_days: employee.dataValues.leave_balance_days - leaveDay,
        },
      });
    }

    jsonSuccess({ data: data.employees.length }, req, res);
  } catch (error) {
    next(error);
  }
};
