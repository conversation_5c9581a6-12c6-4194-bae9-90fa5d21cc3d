const Joi = require('joi');

module.exports = {

  // GET /v1/student/student-grades
  listStudentGrades: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string().valid('asc', 'desc'),
      student: Joi.number(),
      year: Joi.number(),
      semester: Joi.number(),
      course: Joi.number(),
      _class: Joi.number(),
      department: Joi.number(),
      major: Joi.number(),
    }),
  },

  // POST /v1/student/student-grades
  createStudentGrade: {
    body: Joi.object({
      year: Joi.number().integer().min(2000).max(2100)
        .required()
        .messages({
          'number.base': 'Năm học phải là số',
          'number.min': 'Năm học phải từ 2000 trở lên',
          'number.max': '<PERSON><PERSON><PERSON> học không được quá 2100',
          'any.required': '<PERSON>ăm học là bắt buộc',
        }),
      semester: Joi.number().integer().min(1).max(3)
        .required()
        .messages({
          'number.base': '<PERSON>ọc kỳ phải là số',
          'number.min': 'Học kỳ phải từ 1 trở lên',
          'number.max': 'Học kỳ không được quá 3',
          'any.required': 'Học kỳ là bắt buộc',
        }),
      department: Joi.number().integer().required()
        .messages({
          'number.base': 'ID khoa phải là số',
          'any.required': 'Khoa là bắt buộc',
        }),
      major: Joi.number().integer().required()
        .messages({
          'number.base': 'ID ngành phải là số',
          'any.required': 'Ngành là bắt buộc',
        }),
      class: Joi.number().integer().required()
        .messages({
          'number.base': 'ID lớp phải là số',
          'any.required': 'Lớp là bắt buộc',
        }),
      student: Joi.number().integer().required()
        .messages({
          'number.base': 'ID sinh viên phải là số',
          'any.required': 'Sinh viên là bắt buộc',
        }),
      course: Joi.number().integer().required()
        .messages({
          'number.base': 'ID môn học phải là số',
          'any.required': 'Môn học là bắt buộc',
        }),
      grade_100: Joi.number().min(0).max(10).precision(2)
        .messages({
          'number.base': 'Điểm hệ 10 phải là số',
          'number.min': 'Điểm hệ 10 phải từ 0 trở lên',
          'number.max': 'Điểm hệ 10 không được quá 10',
        }),
      grade_4: Joi.number().min(0).max(4).precision(2)
        .messages({
          'number.base': 'Điểm hệ 4 phải là số',
          'number.min': 'Điểm hệ 4 phải từ 0 trở lên',
          'number.max': 'Điểm hệ 4 không được quá 4',
        }),
      grade_letter: Joi.string().valid('A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D', 'F')
        .messages({
          'string.base': 'Điểm chữ phải là chuỗi',
          'any.only': 'Điểm chữ phải là một trong các giá trị: A+, A, B+, B, C+, C, D+, D, F',
        }),
      grade_10: Joi.string().max(10)
        .messages({
          'string.base': 'Điểm hệ 10 (chuỗi) phải là chuỗi',
          'string.max': 'Điểm hệ 10 (chuỗi) không được quá 10 ký tự',
        }),
    }),
  },

  // PATCH /v1/student/student-grades/:id
  updateStudentGrade: {
    body: Joi.object({
      year: Joi.number().integer().min(2000).max(2100)
        .messages({
          'number.base': 'Năm học phải là số',
          'number.min': 'Năm học phải từ 2000 trở lên',
          'number.max': 'Năm học không được quá 2100',
        }),
      semester: Joi.number().integer().min(1).max(3)
        .messages({
          'number.base': 'Học kỳ phải là số',
          'number.min': 'Học kỳ phải từ 1 trở lên',
          'number.max': 'Học kỳ không được quá 3',
        }),
      department: Joi.number().integer()
        .messages({
          'number.base': 'ID khoa phải là số',
        }),
      major: Joi.number().integer()
        .messages({
          'number.base': 'ID ngành phải là số',
        }),
      class: Joi.number().integer()
        .messages({
          'number.base': 'ID lớp phải là số',
        }),
      student: Joi.number().integer()
        .messages({
          'number.base': 'ID sinh viên phải là số',
        }),
      course: Joi.number().integer()
        .messages({
          'number.base': 'ID môn học phải là số',
        }),
      grade_100: Joi.number().min(0).max(10).precision(2)
        .messages({
          'number.base': 'Điểm hệ 10 phải là số',
          'number.min': 'Điểm hệ 10 phải từ 0 trở lên',
          'number.max': 'Điểm hệ 10 không được quá 10',
        }),
      grade_4: Joi.number().min(0).max(4).precision(2)
        .messages({
          'number.base': 'Điểm hệ 4 phải là số',
          'number.min': 'Điểm hệ 4 phải từ 0 trở lên',
          'number.max': 'Điểm hệ 4 không được quá 4',
        }),
      grade_letter: Joi.string().valid('A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D', 'F')
        .messages({
          'string.base': 'Điểm chữ phải là chuỗi',
          'any.only': 'Điểm chữ phải là một trong các giá trị: A+, A, B+, B, C+, C, D+, D, F',
        }),
      grade_10: Joi.string().max(10)
        .messages({
          'string.base': 'Điểm hệ 10 (chuỗi) phải là chuỗi',
          'string.max': 'Điểm hệ 10 (chuỗi) không được quá 10 ký tự',
        }),
    }),
    params: Joi.object({
      id: Joi.number().integer().required()
        .messages({
          'number.base': 'ID phải là số',
          'any.required': 'ID là bắt buộc',
        }),
    }),
  },

  // DELETE /v1/student/student-grades/:id
  deleteStudentGrade: {
    params: Joi.object({
      id: Joi.number().integer().required()
        .messages({
          'number.base': 'ID phải là số',
          'any.required': 'ID là bắt buộc',
        }),
    }),
  },

  // GET /v1/student/student-grades/:id
  getStudentGrade: {
    params: Joi.object({
      id: Joi.number().integer().required()
        .messages({
          'number.base': 'ID phải là số',
          'any.required': 'ID là bắt buộc',
        }),
    }),
  },

  // GET /v1/student/student-grades/student/:studentId
  getStudentGradesByStudent: {
    params: Joi.object({
      studentId: Joi.number().integer().required()
        .messages({
          'number.base': 'ID sinh viên phải là số',
          'any.required': 'ID sinh viên là bắt buộc',
        }),
    }),
    query: Joi.object({
      year: Joi.number().integer().min(2000).max(2100)
        .messages({
          'number.base': 'Năm học phải là số',
          'number.min': 'Năm học phải từ 2000 trở lên',
          'number.max': 'Năm học không được quá 2100',
        }),
      semester: Joi.number().integer().min(1).max(3)
        .messages({
          'number.base': 'Học kỳ phải là số',
          'number.min': 'Học kỳ phải từ 1 trở lên',
          'number.max': 'Học kỳ không được quá 3',
        }),
    }),
  },

  // GET /v1/student/student-grades/class/:classId
  getStudentGradesByClass: {
    params: Joi.object({
      classId: Joi.number().integer().required()
        .messages({
          'number.base': 'ID lớp phải là số',
          'any.required': 'ID lớp là bắt buộc',
        }),
    }),
    query: Joi.object({
      year: Joi.number().integer().min(2000).max(2100)
        .messages({
          'number.base': 'Năm học phải là số',
          'number.min': 'Năm học phải từ 2000 trở lên',
          'number.max': 'Năm học không được quá 2100',
        }),
      semester: Joi.number().integer().min(1).max(3)
        .messages({
          'number.base': 'Học kỳ phải là số',
          'number.min': 'Học kỳ phải từ 1 trở lên',
          'number.max': 'Học kỳ không được quá 3',
        }),
      course: Joi.number().integer()
        .messages({
          'number.base': 'ID môn học phải là số',
        }),
    }),
  },
};
