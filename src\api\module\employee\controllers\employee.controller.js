/* eslint-disable no-continue */
/* eslint-disable no-await-in-loop */
const _ = require('lodash');
const md5 = require('md5');
const { Op } = require('sequelize');
const moment = require('moment');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const db = require('../../../../config/mysql');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');
const XLSX = require('xlsx');
const path = require('path');

const Employee = db.employee;
const Department = db.department;
const User = db.user;
const Position = db.position;
const EmployeePosition = db.employeePosition;
const { fields } = require('../models/employee.model');
const userFields = require('../../user/models/user.model').fields;
const employeePositionFields = require('../models/employee.position.model').fields;
const { rolesVar } = require('../../../../config/vars');
const { generator } = require('../../../services/genPassword');

/**
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const employee = await Employee.get({ id, departmentModel: Department });
    req.locals = { employee };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get employee
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.employee, req, res);
  } catch (error) {
    next(error);
  }
};

// Create and Save a new Employee
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const { user } = req;
    const { email } = data;
    const existEmail = await Employee.countItem({ [fields.email]: email });
    if (existEmail > 0) {
      throw new APIError({
        message: 'Duplicate Email',
        status: httpStatus.BAD_REQUEST,
      });
    }
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });
    const employee = Employee.build({
      ...dbData,
      [fields.created_by]: user.email,
    });
    const savedEmployee = await employee.save();
    const employee_id = savedEmployee.dataValues.pm100;
    const employee_email = savedEmployee.dataValues.mv107;
    const password = `${generator(8)}${generator(4)}`;
    // tạo user mới
    const newUser = User.build({
      [userFields.employee]: employee_id,
      [userFields.email]: employee_email,
      [userFields.password_plain]: password,
      [userFields.password]: md5(password),
      [userFields.created_by]: user.email,
      [userFields.updated_by]: user.email,
      [userFields.is_active]: 1,
      [userFields.user_type]: 'M100',
    });
    const savedUser = await newUser.save();
    jsonSuccess({ savedEmployee, savedUser }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query, user } = req;
    const employees = await Employee.list({ ...query, departmentModel: Department });
    for (let i = 0; i < employees.data.length; i += 1) {
      const item = employees.data[i].dataValues;
      const positions = await EmployeePosition.listByEmployee({
        perPage: -1,
        employee: item._id,
        departmentModel: Department,
        positionModel: Position,
      });
      item.position = positions;
      if (!rolesVar.hr_dept.includes(user.role)) {
        delete item.dob;
        delete item.leave_balance_days;
        delete item.bank_account;
        delete item.bank_name;
        delete item.is_social_insurance;
        delete item.contract_type;
      }
    }
    jsonSuccess(employees, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await Employee.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const { user } = req;
    await Employee.patch({
      id: req.params.id,
      data: {
        ...req.body,
        updated_by: user.email,
      },
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Get profile
 * @public
 */
exports.profile = async (req, res, next) => {
  try {
    const { user } = req;
    const employee = await Employee.profile({
      id: user._id,
      departmentModel: Department,
      positionModel: Position,
      employeePositionModel: EmployeePosition,
      employeePositionFields,
    });
    jsonSuccess(employee, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Get birthday
 * @public
 */
exports.birthday = async (req, res, next) => {
  try {
    const currentDate = moment();
    const month = moment(currentDate, 'M').format('MM');
    const employee = await Employee.birthday({ month, departmentModel: Department });
    jsonSuccess({ total: employee.length, data: employee }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.updateProfile = async (req, res, next) => {
  try {
    const { user } = req;
    await Employee.patch({
      id: user._id,
      data: { ...req.body, updated_by: user.email },
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};


exports.addDays = async (req, res, next) => {
  try {
    const date = new Date().getDate();
    if (date === 21) {
      const { employee, number } = req.body;
      const updated = await Employee.addDays({ employee, number });
      jsonSuccess({ updated }, req, res);
    } else {
      throw new APIError({
        message: 'Go back to sleep, Samurai',
        status: httpStatus.BAD_REQUEST,
      });
    }
  } catch (error) {
    next(error);
  }
};

exports.resetDay = async (req, res, next) => {
  try {
    const updated = await Employee.resetDay();
    jsonSuccess({ updated }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.addBonusDays = async (req, res, next) => {
  try {
    const now = new Date();
    const nowYear = now.getFullYear();
    const employees = await Employee.list({
      departmentModel: Department,
      is_lecturer: 0,
      perPage: -1,
      work_status: 0,
    });
    for (let i = 0; i < employees.total; i += 1) {
      const item = employees.data[i].dataValues;
      if (item.work_start_at) {
        const work_start_at = new Date(item.work_start_at);
        if (nowYear - work_start_at.getFullYear() >= 3) {
          const bonusDays = Math.floor((nowYear - work_start_at.getFullYear()) / 3);
          await Employee.patch({
            id: item._id,
            data: {
              leave_balance_days: item.leave_balance_days + bonusDays,
            },
          });
        }
      }
    }

    jsonSuccess({ now }, req, res);
  } catch (error) {
    next(error);
  }
};

// thống kê số lượng nhân viên
exports.counting = async (req, res, next) => {
  try {
    const { query } = req;
    const now = new Date();
    const nowDate = now.getDate();
    const nowMonth = now.getMonth();
    const nowYear = now.getFullYear();
    const oneYearAgo = new Date(nowYear - 1, nowMonth, nowDate);
    const countingMale = await Employee.countItem({
      [fields.deleted_at]: null,
      [fields.work_status]: 0,
      [fields.sex]: 'M',
      [fields.is_special]: 0,
    });
    const countingFemale = await Employee.countItem({
      [fields.deleted_at]: null,
      [fields.work_status]: 0,
      [fields.sex]: 'F',
      [fields.is_special]: 0,
    });
    const countingAll = await Employee.countItem({
      ...query,
      [fields.deleted_at]: null,
      [fields.work_status]: 0,
      [fields.is_special]: 0,
    });
    const countingNew = await Employee.countItem({
      [fields.work_start_at]: { [Op.gte]: oneYearAgo },
      [fields.deleted_at]: null,
      [fields.work_status]: 0,
      [fields.is_special]: 0,
    });
    jsonSuccess({
      countingMale, countingFemale, countingAll, countingNew,
    }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.importFromExcel = async (req, res, next) => {
  try {
    const { user } = req;
    const checkNotExistEmployee = async (employee) => {
      const fullName = employee.trim().split(' ');
      const firstName = fullName.pop();
      const lastName = fullName.join(' ');
      const existEmployee = await Employee.list({
        first_name: firstName,
        last_name: lastName,
        departmentModel: Department,
      });
      if (existEmployee.total === 0) {
        return true;
      }
      return false;
    };

    // bỏ dấu
    const removeAccents = (str) => {
      let newStr = str;
      const AccentsMap = [
        'aàảãáạăằẳẵắặâầẩẫấậ',
        'AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬ',
        'dđ', 'DĐ',
        'eèẻẽéẹêềểễếệ',
        'EÈẺẼÉẸÊỀỂỄẾỆ',
        'iìỉĩíị',
        'IÌỈĨÍỊ',
        'oòỏõóọôồổỗốộơờởỡớợ',
        'OÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢ',
        'uùủũúụưừửữứự',
        'UÙỦŨÚỤƯỪỬỮỨỰ',
        'yỳỷỹýỵ',
        'YỲỶỸÝỴ',
      ];
      for (let i = 0; i < AccentsMap.length; i += 1) {
        const item = AccentsMap[i];
        const re = new RegExp(`[${item.substr(1)}]`, 'g');
        const char = AccentsMap[i][0];
        newStr = newStr.replace(re, char);
      }
      return newStr.toLowerCase();
    };

    const fetchAcademicLevel = (level) => {
      switch (level) {
        case 'Tiến sĩ': return 3;
        case 'Thạc Sĩ': return 2;
        case 'Thạc sĩ': return 2;
        case 'Thạc sỹ': return 2;
        case 'Cử nhân': return 1;
        default: return 0;
      }
    };

    const fileName = path.join(__dirname, 'test.xlsx');
    const workbook = XLSX.readFile(fileName);
    const worksheet = XLSX.utils.sheet_to_json(workbook.Sheets['sheet 1']);
    const filtered = [];

    // lấy ds nhân viên từ file import
    for (let i = 0; i < worksheet.length; i += 1) {
      const item = worksheet[i];
      if (typeof item['1'] === 'number') {
        if (item['15'] && (item['15'].indexOf('Nghỉ') >= 0 || item['15'].indexOf('nghỉ') >= 0)) {
          continue;
        }
        const checkExist = await checkNotExistEmployee(item['2']);
        if (checkExist) {
          const fullName = item['2'].trim().split(' ');
          const firstName = fullName.pop();
          const lastName = fullName.join(' ');
          let email = `${removeAccents(firstName)}.${removeAccents(lastName.split(' ')[0])}@ttu.edu.vn`;
          const existEmail = await Employee.countItem({ [fields.email]: email });
          if (existEmail > 0) {
            email = `${removeAccents(firstName)}.${removeAccents(lastName.split(' ')[0])}<EMAIL>`;
          }
          const newEmployee = new Employee({
            [fields.first_name]: firstName,
            [fields.last_name]: lastName,
            [fields.phone]: item['5'],
            [fields.nationality]: item['8'],
            [fields.race]: item['9'],
            [fields.sex]: item['10'] === 'Nam' ? 'M' : 'F',
            [fields.address]: item['16'],
            [fields.identification_number]: item['21'],
            [fields.identification_place]: item['23'],
            [fields.bank_account]: item['37'],
            [fields.bank_name]: item['38'],
            [fields.bank_branch]: item['39'],
            [fields.tax_number]: item['41'],
            [fields.social_number]: item['42'],
            [fields.education]: item['27'],
            [fields.academic_level]: fetchAcademicLevel(item['25']),
            [fields.email]: email,
            [fields.created_by]: user.email,
          });
          filtered.push(newEmployee);
        }
      }
    }

    for (let i = 0; i < filtered.length; i += 1) {
      const item = filtered[i];
      const password = `${generator(8)}${generator(4)}`;
      await item.save();


      const newUser = new User({
        [userFields.employee]: item[fields._id],
        [userFields.email]: item[fields.email],
        [userFields.password_plain]: password,
        [userFields.password]: md5(password),
        [userFields.created_by]: user.email,
        [userFields.updated_by]: user.email,
        [userFields.is_active]: 1,
        [userFields.user_type]: 'M100',
      });
      await newUser.save();
    }

    jsonSuccess({ count: filtered.length }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.tmp = async (req, res, next) => {
  try {
    const employees = await Employee.tmp({ departmentModel: Department });
    jsonSuccess({ employees }, req, res);
  } catch (error) {
    next(error);
  }
};
