// 27/11/2024
const Joi = require('joi');

module.exports = {

  // GET /v1/student/grade-components
  listGradeComponents: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      semester: Joi.number(),
      year: Joi.number(),
      _class: Joi.number(),
      course: Joi.number(),
    }),
  },

  // POST /v1/student/grade-components
  createGradeComponent: {
    body: Joi.object({
      year: Joi.number().required(),
      semester: Joi.number().required(),
      class: Joi.number().required(),
      course: Joi.number(),
      weight: Joi.number(),
      name_eng: Joi.string(),
      name_vie: Joi.string(),
      description: Joi.string().allow('').optional(),
      note: Joi.string().allow('').optional(),
    }),
  },

  // PATCH /v1/student/grade-components/:id
  updateGradeComponent: {
    body: Joi.object({
      year: Joi.number(),
      semester: Joi.number(),
      class: Joi.number(),
      course: Joi.number(),
      weight: Joi.number(),
      name_eng: Joi.string(),
      name_vie: Joi.string(),
      description: Joi.string().allow('').optional(),
      note: Joi.string().allow('').optional(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/student/grade-components/:id
  deleteGradeComponent: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },
};
