language: node_js
node_js: '8'
cache: yarn

git:
  depth: 3

branches:
  only:
    - master
    - /^greenkeeper/.*$/

env:
  global:
    - NODE_ENV=test
    - PORT=3000
    - JWT_SECRET=bA2xcjpf8y5aSUFsNB2qN5yymUBSs6es3qHoFpGkec75RCeBb8cpKauGefw5qy4
    - JWT_EXPIRATION_MINUTES=15

script: yarn validate

before_install: yarn global add greenkeeper-lockfile@1
before_script: greenkeeper-lockfile-update
after_script: greenkeeper-lockfile-upload

# deploy:
# - provider: script
#   script: yarn deploy

after_success: yarn coverage
