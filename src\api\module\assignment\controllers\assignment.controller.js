// 8/11/2021
const _ = require('lodash');
const db = require('../../../../config/mysql');
const { fields } = require('../models/assignment.model');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');

const Assignment = db.assignment;
const AssignmentContent = db.assignmentContent;
const Employee = db.employee;
const Department = db.department;

/**
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const assignment = await Assignment.get({ id, employeeModel: Employee });
    req.locals = { assignment };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    data.created_by = req.user.email;
    data.employee = req.user._id;
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });

    const assignment = Assignment.build({
      ...dbData,
    });
    const saved = await assignment.save();
    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    let listAssignment = [];
    const assignmentContents = await AssignmentContent.list({
      employeeModel: Employee,
      assignmentModel: Assignment,
      perPage: -1,
      employee: req.user._id,
    });
    if (assignmentContents.total > 0) {
      for (let i = 0; i < assignmentContents.data.length; i += 1) {
        const item = assignmentContents.data[i];
        if (item.dataValues && item.dataValues.assignment) {
          listAssignment.push(item.dataValues.assignment.dataValues._id);
        }
      }
    }

    listAssignment = [...new Set(listAssignment)];

    const assignments = await Assignment.list({
      _id: listAssignment,
      ...query,
      employeeModel: Employee,
      departmentModel: Department,
    });

    jsonSuccess(assignments, req, res);
  } catch (error) {
    next(error);
  }
};

exports.listAll = async (req, res, next) => {
  try {
    const { query } = req;
    const assignments = await Assignment.list({
      ...query,
      employeeModel: Employee,
      departmentModel: Department,
    });

    jsonSuccess(assignments, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await Assignment.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await Assignment.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.analyst = async (req, res, next) => {
  try {
    const employeeId = req.query.employee || null;
    const assignmentId = req.query.assignment || null;
    const employee = await Employee.get(employeeId);
    const assignmentContents = await AssignmentContent.listByEmployeeTo({
      employee: employeeId,
      assignment: assignmentId,
    });
    let complete = 0;
    let pending = 0;
    let inProgress = 0;
    let overDue = 0;
    const ratio = 0;
    assignmentContents.forEach((value) => {
      switch (value.dataValues.status) {
        case 0:
          pending += 1;
          break;
        case 1:
          inProgress += 1;
          break;
        case 2:
          complete += 1;
          break;
        case 4:
          overDue += 1;
          break;
        case 3:
          console.log('daaaaa');
          break;
        default:
          break;
      }
      console.log('>>>>>>>>>', value.dataValues.progress);
    });
    jsonSuccess({
      employee, complete, pending, inProgress, overDue, total: assignmentContents.length, ratio,
    }, req, res);
  } catch (error) {
    next(error);
  }
};
