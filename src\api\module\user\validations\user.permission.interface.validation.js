const Joi = require('joi');

module.exports = {

  // GET /v1/department/department
  listDepartments: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      name: Joi.string(),
      sort: Joi.string(),
      is_active: Joi.bool(),
    }),
  },

  // POST /v1/department/department
  createDepartment: {
    body: Joi.object({
      name: Joi.string().required(),
      is_active: Joi.bool(),
    }),
  },

  // PATCH /v1/department/department/:id
  updateDepartment: {
    body: Joi.object({
      name: Joi.string(),
      is_active: Joi.bool(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/department/department/:id
  deleteDepartment: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

};
