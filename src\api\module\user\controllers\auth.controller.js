/* eslint-disable no-await-in-loop */
// const { ExtractJwt } = require('passport-jwt');
// const jwt = require('jwt-simple');
const httpStatus = require('http-status');
// const crypto = require('crypto');
// const moment = require('moment-timezone');

const db = require('../../../../config/mysql');
// const User = require('../../../../config/mysql').user;
// const UserPermissionList = (require('../../../../config/mysql')).userPermissionList;
const User = db.user;
const UserPermissionList = db.userPermissionList;
const UserPermission = db.userPermission;
const Student = db.student;
// const ForgotPassword = require('../models/forgot.password.model');
// const RefreshToken = require('../models/refreshToken.model');

// const { jwtSecret } = require('../../../../config/vars');
// const { jwtExpirationInterval } = require('../../../../config/vars');
const { success: jsonSuccess } = require('../../../middlewares/success');
const APIError = require('../../../utils/APIError');
const { forgotPassword } = require('../../../services/mailer');
const { generator } = require('../../../services/genPassword');
const ForgotPassword = require('../../user/models/forgot.password.model');
/**
* Returns a formated object with tokens
* @private
*/
// function generateTokenResponse(user, accessToken) {
//   const tokenType = 'Bearer';
//   const refreshToken = RefreshToken.generate(user).token;
//   const expiresIn = moment().add(jwtExpirationInterval, 'minutes');
//   // const expiresIn = moment().add(1, 'minutes');
//   return {
//     tokenType, accessToken, refreshToken, expiresIn,
//   };
// }

/**
 * Returns jwt token if registration was successful
 * @public
 */
// exports.register = async (req, res, next) => {
//   try {
//     const activeToken = crypto.randomBytes(16).toString('hex');
//     let user_id = '';
//     const checktoken = ExtractJwt.fromAuthHeaderAsBearerToken()(req);
//     if (checktoken) {
//       const decoded = jwt.decode(checktoken, jwtSecret);
//       user_id = decoded.sub;
//       const getuser = await User.get(user_id);
//       if (getuser) {
//         if (getuser.role !== 'admin') {
//           if (req.body.role === 'admin') {
//             throw new APIError({
//               message: 'User does not create admin permissions ',
//               status: httpStatus.NOT_FOUND,
//             });
//           }
//         }
//       }
//     } else if (req.body.role === 'admin') {
//       throw new APIError({
//         message: 'User does not create admin permissions ',
//         status: httpStatus.NOT_FOUND,
//       });
//     }
//     req.body.active_token = activeToken;
//     req.body.is_active = true;
//     const user = await (new User(req.body)).save();
//     const userTransformed = user.transform();
//     const token = generateTokenResponse(user, user.token());
//     res.status(httpStatus.CREATED);
//     return jsonSuccess({ token, user: userTransformed }, req, res);
//   } catch (error) {
//     return next(User.checkDuplicateEmail(error));
//   }
// };

/**
 * Returns jwt token if valid username and password is provided
 * @public
 */
exports.login = async (req, res, next) => {
  try {
    const {
      user, token, is_admin, is_post, is_hr, expiry_date, is_student, student,
    } = await User.findAndGenerateToken({
      body: req.body,
      permissionModel: UserPermissionList,
      userPermissionModel: UserPermission,
      studentModel: Student,
    });
    if (user.dataValues.is_active === 0) {
      throw new APIError({
        message: 'Email does not active',
        status: httpStatus.FORBIDDEN,
      });
    }
    return jsonSuccess({
      token, user, is_admin, is_post, is_hr, expiry_date, is_student, student,
    }, req, res);
  } catch (error) {
    return next(error);
  }
};

/**
 * login with an existing user or creates a new one if valid accessToken token
 * Returns jwt token
 * @public
 */
// exports.oAuth = async (req, res, next) => {
//   try {
//     console.error('HEADERS', req.headers);
//     console.error('BODY', req.body);
//     const { user } = req;
//     const accessToken = user.token();
//     const token = generateTokenResponse(user, accessToken);
//     const userTransformed = user.transform();
//     return jsonSuccess({ token, user: userTransformed }, req, res);
//   } catch (error) {
//     return next(error);
//   }
// };

/**
 * Returns jwt token if valid username and password is provided
 * @public
 */
exports.changePassword = async (req, res, next) => {
  try {
    req.body.email = req.user.email;
    const {
      user,
    } = await User.findAndGenerateToken({
      body: req.body,
      permissionModel: UserPermissionList,
      userPermissionModel: UserPermission,
    });

    await User.updatePassword({
      newPassword: req.body.newpassword,
      email: user.dataValues.email,
      password: req.body.password,
    });

    return jsonSuccess({ user }, req, res);
  } catch (error) {
    return next(error);
  }
};

exports.changeWithoutPassword = async (req, res, next) => {
  try {
    const token = await ForgotPassword.getByParams({
      token: req.body.token,
      email: req.body.email,
    });
    await User.updatePasswordWithoutPassword({
      newPassword: req.body.newpassword,
      email: req.body.email,
    });
    await token.remove();
    return jsonSuccess({}, req, res);
  } catch (error) {
    return next(error);
  }
};

/**
 * Forgot password
 * @public
 */
exports.forgotPassword = async (req, res, next) => {
  try {
    const { email } = req.body;
    const user = await User.get(email);
    const newToken = `${generator(8)}${generator(4)}${generator(6)}`;
    const token = new ForgotPassword({ email, token: newToken });
    await token.save();

    if (user) {
      // send mail
      await forgotPassword({
        email,
        password: newToken,
      });
      jsonSuccess({}, req, res);
    } else {
      throw new APIError({
        message: 'Email does not exist',
        status: httpStatus.NOT_FOUND,
      });
    }
  } catch (error) {
    next(error);
  }
};

exports.getOtp = async (req, res, next) => {
  try {
    const { email } = req.body;
    const user = await User.get(email);
    const opt = generator(6);
    const token = new ForgotPassword({ email, token: opt });
    await token.save();

    jsonSuccess({ user, opt }, req, res);
  } catch (error) {
    next(error);
  }
};
// exports.forgotPassword = async (req, res, next) => {
//   try {
//     const { email } = Object.assign(req.body, {});

//     const user = await User.findOne({ email: `${email}` }).exec();
//     if (user) {
//       const token = crypto.randomBytes(16).toString('hex');

//       const forgot = new ForgotPassword({ email: `${email}`, token: `${token}` });
//       await forgot.save();

//       jsonSuccess({}, req, res);
//     } else {
//       throw new APIError({
//         message: 'Invalid Email',
//         status: httpStatus.NOT_FOUND,
//       });
//     }
//   } catch (error) {
//     next(User.checkDuplicateEmail(error));
//   }
// };

/**
 * Returns a new jwt when given a valid refresh token
 * @public
 */
// exports.refresh = async (req, res, next) => {
//   try {
//     const { email, refreshToken } = req.body;
//     const refreshObject = await RefreshToken.findOneAndRemove({
//       userEmail: email,
//       token: refreshToken,
//     });
//     const { user, accessToken } = await User.findAndGenerateToken({ email, refreshObject });
//     const token = generateTokenResponse(user, accessToken);
//     const userTransformed = user.transform();
//     return jsonSuccess({ token, user: userTransformed }, req, res);
//   } catch (error) {
//     return next(error);
//   }
// };

/**
 * Forgot password
 * @public
 */
// exports.activeEmail = async (req, res, next) => {
//   try {
//     const user = await User.findOne({ active_token: req.body.active_token });
//     if (user) {
//       await User.findOneAndUpdate({ active_token: req.body.active_token }, { is_active: true });
//       jsonSuccess({}, req, res);
//     } else {
//       throw new APIError({
//         message: 'Active code incorrect',
//         status: httpStatus.NOT_FOUND,
//       });
//     }
//   } catch (error) {
//     next(error);
//   }
// };

// eslint-disable-next-line consistent-return
// exports.resetPassword = async (req, res, next) => {
//   try {
//     const { token } = req.params;
//     const decodeToken = jwt.decode(token, jwtSecret);
//     const user = await User.findById(decodeToken.sub);
//     if (user) {
//       user.password = req.body.password;
//       await user.save();
//       jsonSuccess(user, req, res);
//     }
//   } catch (error) {
//     return next(error);
//   }
// };
