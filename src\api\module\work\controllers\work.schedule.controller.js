// 28/9/2021
const _ = require('lodash');
const db = require('../../../../config/mysql');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');
const { fields } = require('../models/work.schedule.model');

const WorkSchedule = db.workSchedule;

/**
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const schedule = await WorkSchedule.get(id);
    req.locals = { schedule };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.schedule, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const { email } = req.user;
    data.created_by = email;
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });
    const schedule = WorkSchedule.build({
      ...dbData,
    });
    const saved = await schedule.save();
    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    const schedules = await WorkSchedule.list(query);
    jsonSuccess(schedules, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await WorkSchedule.remove({
      id: req.params.id,
      userId: req.user._id,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await WorkSchedule.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await WorkSchedule.remove({
      id: req.params.id,
      userId: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};
