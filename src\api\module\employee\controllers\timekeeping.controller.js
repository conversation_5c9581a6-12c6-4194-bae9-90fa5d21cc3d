/* eslint-disable no-await-in-loop */
// 9/2/2023
const XLSX = require('xlsx');
const { success: jsonSuccess } = require('../../../middlewares/success');
const moment = require('moment');
const multer = require('multer');
const db = require('../../../../config/mysql');
const { uploadDir, rolesVar } = require('../../../../config/vars');
const { fields } = require('../models/timekeeping.model');

// const { handler: errorHandler } = require('../../../middlewares/error');

const Employee = db.employee;
const Department = db.department;
const Timekeeping = db.timekeeping;

// bỏ dấu
const removeAccents = (str) => {
  let newStr = str;
  const AccentsMap = [
    'aàảãáạăằẳẵắặâầẩẫấậ',
    'AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬ',
    'dđ', 'DĐ',
    'eèẻẽéẹêềểễếệ',
    'EÈẺẼÉẸÊỀỂỄẾỆ',
    'iìỉĩíị',
    'IÌỈĨÍỊ',
    'oòỏõóọôồổỗốộơờởỡớợ',
    'OÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢ',
    'uùủũúụưừửữứự',
    'UÙỦŨÚỤƯỪỬỮỨỰ',
    'yỳỷỹýỵ',
    'YỲỶỸÝỴ',
  ];
  for (let i = 0; i < AccentsMap.length; i += 1) {
    const item = AccentsMap[i];
    const re = new RegExp(`[${item.substr(1)}]`, 'g');
    const char = AccentsMap[i][0];
    newStr = newStr.replace(re, char);
  }
  return newStr.toLowerCase();
};

exports.importFromExcel = async (req, res, next) => {
  try {
    const { user } = req;

    const func = multer.diskStorage({
      // multers disk storage settings
      destination: (rq, file, cb) => {
        cb(null, uploadDir.tmp);
      },
      filename: (rq, file, cb) => {
        const datetimestamp = Date.now();
        cb(null, `${file.fieldname}-${datetimestamp}.${file.originalname.split('.')[file.originalname.split('.').length - 1]}`);
      },
    });

    const upload = multer({
      storage: func,
    }).single('file');

    upload(req, res, async (err) => {
      if (err) {
        res.json({
          error_code: 1,
          err_desc: err,
        });
      }
      const { is_update_code } = req.body;
      const employees = await Employee.list({
        departmentModel: Department,
        work_status: 0,
        perPage: -1,
      });

      const workbook = XLSX.readFile(req.file.path, {
        cellDates: true,
        // dateNF: 'mm/dd/yyyy',
        // dateNF: 'dd/mm/yyyy',
      });
      const worksheet = XLSX.utils.sheet_to_json(workbook.Sheets['Xuất lưới']);
      const filtered = [];

      // lấy ds nhân viên từ file import{
      for (let i = 2; i < worksheet.length; i += 1) {
        const item = worksheet[i];
        const timekeepingDate = moment(new Date(item['GIỜ CHẤM CÔNG'])).add(1, 'hours').format('YYYY-MM-DD');
        item['GIỜ CHẤM CÔNG'] = timekeepingDate;
        for (let j = 0; j < employees.data.length; j += 1) {
          const employee = employees.data[j].dataValues;

          if (is_update_code === 'true') {
            // update timekeeping_code cho employee
            if (employee.timekeeping_code === null
              && removeAccents(item.__EMPTY_2) === `${removeAccents(employee.last_name)} ${removeAccents(employee.first_name)}`) {
              const timekeeping_code = item.__EMPTY_1;
              await Employee.patch({
                id: employee._id,
                data: { timekeeping_code },
              });
            }
          }
          if (employee.timekeeping_code && employee.timekeeping_code === item.__EMPTY_1) {
            const isExists = await Timekeeping.checkExists({ employee: employee._id, date: item['GIỜ CHẤM CÔNG'] });
            if (typeof item.__EMPTY_6 !== 'string') {
              item.__EMPTY_6 = '0';
            }
            if (isExists === 0) {
              const timekeeping = Timekeeping.build({
                [fields.employee]: employee._id,
                [fields.date]: item['GIỜ CHẤM CÔNG'],
                [fields.start_at]: item.__EMPTY_5,
                [fields.end_at]: item.__EMPTY_6,
                [fields.created_by]: user.email,
              });
              await timekeeping.save();
            }
          }
        }
        filtered.push(item);
      }

      jsonSuccess({ filtered, count: filtered.length }, req, res);
    });
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query, user } = req;
    if (!rolesVar.hr_dept.includes(user.role)) {
      query.employee = user._id;
    }
    const timekeeping = await Timekeeping.list({ ...query, employeeModel: Employee });

    jsonSuccess(timekeeping, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await Timekeeping.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};
