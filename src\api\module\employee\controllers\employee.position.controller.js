// 27/1/2022
/* eslint-disable no-await-in-loop */
const db = require('../../../../config/mysql');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const { rolesVar } = require('../../../../config/vars');

const EmployeePosition = db.employeePosition;
const Employee = db.employee;
const Department = db.department;
const Position = db.position;
const Contract = db.contract;

/**
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const employeePosition = await EmployeePosition.get({
      id,
      employeeModel: Employee,
      departmentModel: Department,
      positionModel: Position,
      contractModel: Contract,
    });
    req.locals = { employeePosition };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get employeePosition
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    const { employeePosition } = req.locals;
    const { user } = req;
    const employee = employeePosition.dataValues.employee.dataValues._id;
    if (employee === user._id || rolesVar.admin.includes(user.role)) {
      jsonSuccess(req.locals.employeePosition, req, res);
    } else {
      throw new APIError({
        message: 'You don\'t permission',
        status: httpStatus.BAD_REQUEST,
      });
    }
  } catch (error) {
    next(error);
  }
};

// Create and Save a new EmployeePosition
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    data.created_by = req.user.email;
    data.contract = 1;
    const employeePosition = await EmployeePosition.create(data);
    jsonSuccess(employeePosition, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    const employeePosition = await EmployeePosition.list({
      ...query,
      employeeModel: Employee,
      departmentModel: Department,
      positionModel: Position,
      contractModel: Contract,

    });

    jsonSuccess(employeePosition, req, res);
  } catch (error) {
    next(error);
  }
};

exports.listAll = async (req, res, next) => {
  try {
    const { query } = req;
    const employeePosition = await EmployeePosition.list({ ...query, employeeModel: Employee });

    jsonSuccess(employeePosition, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    req.body.updated_by = req.user.email;
    await EmployeePosition.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await EmployeePosition.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};
