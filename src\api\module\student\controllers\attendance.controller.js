/* eslint-disable no-await-in-loop */
// 1/12/2022
const _ = require('lodash');
const fs = require('fs');
const db = require('../../../../config/mysql');
const { fields } = require('../models/attendance.model');
const absentFields = require('../models/absent.model').fields;
const { uploadDir } = require('../../../../config/vars');
const classFields = require('../models/class.model').fields;
const lecturerFields = require('../../employee/models/employee.model').fields;
const { success: jsonSuccess } = require('../../../middlewares/success');
const AttendancePermission = require('../models/attendance.permission.model');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
// const XLSX = require('xlsx');
const XLSX = require('xlsx-js-style');
const moment = require('moment');
const { registrarEmails, secretaryEmails } = require('../../../../config/vars');

const Attendance = db.attendance;
const Class = db.class;
const Lecturer = db.employee;
const Enroll = db.enroll;
const Student = db.student;
const Absent = db.absent;
const Department = db.department;

// Create and Save a new Attendance
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const { user } = req;
    let checkPermission = 0;
    if (user.role === 0) {
      // db xài mongo
      checkPermission = await AttendancePermission.count({
        _class: data.class,
        student: user.email,
      });
      data.student = user._id;
    } else {
      checkPermission = await Class.countItem({
        [classFields.lecturer]: user._id,
        [classFields._id]: data.class,
      });
    }

    if (checkPermission > 0) {
      const _class = await Class.get(data.class);
      data.code = _class.dataValues.code;
      data.lecturer = _class.dataValues.lecturer;
      data.year = _class.dataValues.year;
      data.semester = _class.dataValues.semester;
      data.created_by = user.email;
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      const attendance = Attendance.build({
        ...dbData,
      });
      const saved = await attendance.save();
      jsonSuccess(saved, req, res);
    } else {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.BAD_REQUEST,
      });
    }
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query, user } = req;
    // sv
    if (user.role === 0) {
      if (query._class) {
        const checkPermission = await AttendancePermission.count({
          _class: query._class,
          student: user.email,
        });

        // ko có quyền thì báo lỗi
        if (checkPermission <= 0) {
          throw new APIError({
            message: 'Don\'t have permission',
            status: httpStatus.BAD_REQUEST,
          });
        }
      } else {
        const attendancePermission = await AttendancePermission.list({
          perPage: -1,
          student: user.email,
        });

        query._class = attendancePermission.map(element => element.class);

        const attendance = await Attendance.listForStudent({
          perPage: -1,
          ...query,
          lecturerModel: Lecturer,
          classModel: Class,
        });
        jsonSuccess(attendance, req, res);
      }
    } else if (
      registrarEmails.includes(user.email)
      || secretaryEmails.includes(user.email)
    ) { // admin
      const attendance = await Attendance.list({
        ...query,
        lecturerModel: Lecturer,
        classModel: Class,
      });
      jsonSuccess(attendance, req, res);
    } else { // gv
      const attendance = await Attendance.list({
        ...query,
        lecturer: user._id,
        lecturerModel: Lecturer,
        classModel: Class,
      });
      jsonSuccess(attendance, req, res);
    }
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const { user } = req;
    let checkPermission = 0;
    const { id } = req.params;
    const attendance = await Attendance.get({
      id,
      lecturerModel: Lecturer,
      classModel: Class,
    });
    const _class = attendance.dataValues.class.dataValues._id;
    const lecturer = attendance.dataValues.lecturer.dataValues._id;
    if (user.role === 0) {
      checkPermission = await AttendancePermission.count({
        _class,
        student: user.email,
      });
    } else if (lecturer === user._id) {
      checkPermission = 1;
    }

    if (checkPermission > 0) {
      await Attendance.patch({
        id: req.params.id,
        data: {
          updated_by: user.email,
          ...req.body,
        },
      });
      jsonSuccess({}, req, res);
    } else {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.BAD_REQUEST,
      });
    }
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { email } = req.user;
    const attendance = await Attendance.get({
      id,
      lecturerModel: Lecturer,
      classModel: Class,
    });
    const { created_by } = attendance.dataValues;
    const lecturer = attendance.dataValues.lecturer.dataValues.email;
    let is_permission = false;

    if (lecturer === email || created_by === email) {
      is_permission = true;
    }

    if (is_permission) {
      const data = {
        [absentFields.deleted_at]: moment().format(),
        [absentFields.deleted_by]: req.user.email,
      };
      const where = {
        [absentFields.attendance]: req.params.id,
      };
      const result = await Attendance.remove({
        id: req.params.id,
        email: req.user.email,
      });
      await Absent.patchMany({ data, where });
      jsonSuccess(result, req, res);
    } else {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.BAD_REQUEST,
      });
    }
  } catch (error) {
    next(error);
  }
};

exports.get = async (req, res, next) => {
  try {
    const { user } = req;
    const { id } = req.params;
    let checkPermission = 0;

    const attendance = await Attendance.get({
      id,
      lecturerModel: Lecturer,
      classModel: Class,
    });
    const lecturer = attendance.dataValues.lecturer.dataValues._id;
    const _class = attendance.dataValues.class.dataValues._id;
    if (user.role === 0) {
      checkPermission = await AttendancePermission.count({
        _class,
        student: user.email,
      });
    } else if (lecturer === user._id) {
      checkPermission = 1;
    }

    if (checkPermission > 0) {
      jsonSuccess(attendance, req, res);
    } else {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.BAD_REQUEST,
      });
    }
  } catch (error) {
    next(error);
  }
};

exports.report = async (req, res, next) => {
  try {
    const { query, user } = req;
    const now = Date.now();
    const dir = uploadDir.att_report;
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir);
    }
    let _class = null;

    if (user.role === 0) {
      const attendancePermission = await AttendancePermission.list({
        student: user.email,
        _class: query._class,
      });
      if (attendancePermission.length === 0) {
        throw new APIError({
          message: 'You don\'t have permission',
          status: httpStatus.BAD_REQUEST,
        });
      }
      _class = await Class.get(query._class);
    } else if (registrarEmails.includes(user.email) || secretaryEmails.includes(user.email)) {
      _class = await Class.get(query._class);
    } else {
      _class = await Class.get(query._class);
      if (!_class || _class.dataValues.lecturer !== user._id) {
        throw new APIError({
          message: 'You don\'t have permission',
          status: httpStatus.BAD_REQUEST,
        });
      }
    }

    const enrolls = await Enroll.list({ studentModel: Student, perPage: -1, _class: query._class });
    const attendances = await Attendance.list({
      lecturerModel: Lecturer,
      classModel: Class,
      _class: query._class,
      perPage: -1,
      order_way: 'asc',
      order_by: fields.date,
    });

    let lecturer = null;
    if (_class) {
      lecturer = await Lecturer.get({
        id: _class.dataValues.lecturer,
        departmentModel: Department,
      });
      if (lecturer) {
        lecturer = `${lecturer.dataValues.last_name} ${lecturer.dataValues.first_name}`;
      }
    }
    const fileName = `Report-[${_class.dataValues.name}][${moment(now).format('DD-MM-YY')}][${now}].xlsx`;
    const directory = `${dir}/${fileName}`;
    const blackBorder = {
      border: {
        top: { style: 'medium', color: { rgb: '696969' } },
        bottom: { style: 'medium', color: { rgb: '696969' } },
        right: { style: 'medium', color: { rgb: '696969' } },
        left: { style: 'medium', color: { rgb: '696969' } },
      },
    };
    const headerStyle = {
      font: { bold: true, name: 'Times New Roman', sz: 12 },
      alignment: { vertical: 'center', horizontal: 'center', wrapText: true },
    };
    const bodyStyle = {
      font: { name: 'Times New Roman', sz: 12 },
    };
    const dateCols = [];
    const wthCols = [];

    for (let i = 0; i < attendances.data.length; i += 1) {
      const item = attendances.data[i].dataValues;
      const date = moment(item.date).format('DD/MM/YYYY');
      dateCols.push({
        v: `Day ${i + 1}\n${date}`, t: 's', s: { ...headerStyle, ...blackBorder }, _id: item._id,
      });
      wthCols.push({ width: 15 });
    }

    const courseRow = [{ v: `Course: ${_class.dataValues.name} [${_class.dataValues.code}]`, t: 's', s: { font: { name: 'Times New Roman', sz: 12 } } }];
    const lecturerRow = [{ v: `Lecturer: ${lecturer}`, t: 's', s: { font: { name: 'Times New Roman', sz: 12 } } }];
    const headerRow = [
      { v: 'No.', t: 's', s: { ...headerStyle, ...blackBorder } },
      { v: 'Student ID', t: 's', s: { ...headerStyle, ...blackBorder } },
      { v: 'Full name', t: 's', s: { ...headerStyle, ...blackBorder } },
      { v: 'Email', t: 's', s: { ...headerStyle, ...blackBorder } },
      ...dateCols,
      { v: 'Total days', t: 's', s: { ...headerStyle, ...blackBorder, fill: { fgColor: { rgb: 'FFFF8F' } } } },
      { v: 'Total absent days', t: 's', s: { ...headerStyle, ...blackBorder } },
      { v: 'Absent percentage', t: 's', s: { ...headerStyle, ...blackBorder } },
      {
        v: 'Banned from final exam (Yes: absent percentage > 20%; No: absent percentage < 20% )',
        t: 's',
        s: {
          ...headerStyle,
          ...blackBorder,
          fill: { fgColor: { rgb: 'FFFF8F' } },
        },
      },

    ];
    const bodyRow = [];
    // for từng sinh viên
    for (let i = 0; i < enrolls.data.length; i += 1) {
      const item = enrolls.data[i].dataValues;
      const student = item.student.dataValues;
      const attCols = [];
      const totalAbsent = await Absent.countAbsentEachStudent({
        _class: query._class,
        student: student._id,
      });
      let absentPercent = (((totalAbsent * 100) / dateCols.length) * 100) / 100;
      absentPercent = absentPercent.toFixed(1);
      const isBanned = absentPercent > 20;
      const bannedRow = isBanned ? { fill: { fgColor: { rgb: 'F1DDE0' } } } : {};

      // for từng buổi điểm danh
      for (let j = 0; j < dateCols.length; j += 1) {
        const att = dateCols[j];
        const checkAbsent = await Absent.checkAbsent({
          attendance: att._id,
          student: student._id,
        });
        // check có nghỉ ko
        if (checkAbsent) {
          if (checkAbsent.dataValues.is_permission === 1) {
            attCols.push({
              v: 'R/A',
              t: 's',
              s: {
                ...blackBorder,
                font: { name: 'Times New Roman', sz: 12, color: { rgb: '32AD32' } },
                alignment: { vertical: 'center', horizontal: 'center' },
                ...bannedRow,
              },
            });
          } else {
            attCols.push({
              v: 'A',
              t: 's',
              s: {
                ...blackBorder,
                font: { name: 'Times New Roman', sz: 12, color: { rgb: '922820' } },
                alignment: { vertical: 'center', horizontal: 'center' },
                ...bannedRow,
              },
            });
          }
        } else {
          attCols.push({
            v: 'P',
            t: 's',
            s: {
              ...blackBorder,
              ...bodyStyle,
              ...bannedRow,
              font: { name: 'Times New Roman', sz: 12, color: { rgb: '6495ED' } },
              alignment: { vertical: 'center', horizontal: 'center' },
            },
          });
        }
      }

      bodyRow.push([
        { v: i + 1, t: 's', s: { ...blackBorder, ...bodyStyle, ...bannedRow } },
        { v: student.student_id, t: 's', s: { ...blackBorder, ...bodyStyle, ...bannedRow } },
        { v: `${student.last_name} ${student.first_name}`, t: 's', s: { ...blackBorder, ...bodyStyle, ...bannedRow } },
        { v: student.email, t: 's', s: { ...blackBorder, ...bodyStyle, ...bannedRow } },
        ...attCols,
        { v: dateCols.length, t: 's', s: { ...blackBorder, ...bodyStyle, ...bannedRow } },
        { v: totalAbsent, t: 's', s: { ...blackBorder, ...bodyStyle, ...bannedRow } },
        {
          v: `${absentPercent}%`,
          t: 's',
          s: {
            ...blackBorder, ...bodyStyle, numFmt: '0%', ...bannedRow,
          },
        },
        { v: isBanned ? 'Yes' : 'No', t: 's', s: { ...blackBorder, ...bodyStyle, ...bannedRow } },
      ]);
    }
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet([
      [{
        v: `${moment(now).format('LLLL')} by ${req.user.email}`,
        t: 's',
      }],
      [],
      courseRow,
      lecturerRow,
      [{
        v: 'P: Present',
        t: 's',
        s: {
          font: { name: 'Times New Roman', sz: 12, color: { rgb: '6495ED' } },
        },
      }, {
        v: 'R/A: Request for Absence',
        t: 's',
        s: {
          font: { name: 'Times New Roman', sz: 12, color: { rgb: '32AD32' } },
        },
      }, {
        v: 'A: Absent',
        t: 's',
        s: {
          font: { name: 'Times New Roman', sz: 12, color: { rgb: '922820' } },
        },
      }],
      [],
      headerRow,
      ...bodyRow,
    ]);
    const sheet_name = 'Result';
    worksheet['!cols'] = [{ width: 10 }, { width: 20 }, { width: 20 }, { width: 20 }, ...wthCols, { width: 20 }, { width: 20 }, { width: 20 }, { width: 40 }];
    XLSX.utils.book_append_sheet(workbook, worksheet, sheet_name);
    XLSX.writeFile(workbook, `${directory}`);
    jsonSuccess({ fileName, directory: `att_report/${fileName}` }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.reportPreview = async (req, res, next) => {
  try {
    const { query, user } = req;
    let _class = null;

    if (user.role === 0) {
      const attendancePermission = await AttendancePermission.list({
        student: user.email,
        _class: query._class,
      });
      if (attendancePermission.length === 0) {
        throw new APIError({
          message: 'You don\'t have permission',
          status: httpStatus.BAD_REQUEST,
        });
      }
      _class = await Class.get(query._class);
    } else if (registrarEmails.includes(user.email) || secretaryEmails.includes(user.email)) {
      _class = await Class.get(query._class);
    } else {
      _class = await Class.get(query._class);
      if (!_class || _class.dataValues.lecturer !== user._id) {
        throw new APIError({
          message: 'You don\'t have permission',
          status: httpStatus.BAD_REQUEST,
        });
      }
    }

    const enrolls = await Enroll.list({
      studentModel: Student,
      perPage: -1,
      _class: query._class,
    });
    const attendances = await Attendance.list({
      lecturerModel: Lecturer,
      classModel: Class,
      _class: query._class,
      perPage: -1,
      order_way: 'asc',
      order_by: fields.date,
    });

    let lecturer = null;
    if (_class) {
      lecturer = await Lecturer.get({
        id: _class.dataValues.lecturer,
        departmentModel: Department,
      });
      if (lecturer) {
        lecturer = `${lecturer.dataValues.last_name} ${lecturer.dataValues.first_name}`;
      }
    }
    const table = [];
    const dateCols = [];
    const attIdCols = [];
    const bodyRow = [];
    for (let i = 0; i < attendances.data.length; i += 1) {
      const item = attendances.data[i].dataValues;
      const date = moment(item.date).format('DD/MM/YYYY');
      dateCols.push(`Day ${i + 1}\n${date}`);
      attIdCols.push(item._id);
    }
    const headerRow = [
      'No.',
      'Full name',
      'Student ID',
      'Email',
      ...dateCols,
      'Total days',
      'Total absent days',
      'Absent percentage',
      'Banned from final exam (Yes: absent percentage > 20%; No: absent percentage < 20% )',
    ];

    // for từng sinh viên
    for (let i = 0; i < enrolls.data.length; i += 1) {
      const item = enrolls.data[i].dataValues;
      const student = item.student.dataValues;
      const attCols = [];
      const totalAbsent = await Absent.countAbsentEachStudent({
        _class: query._class,
        student: student._id,
      });
      let absentPercent = (((totalAbsent * 100) / dateCols.length) * 100) / 100;
      absentPercent = absentPercent.toFixed(1);
      const isBanned = absentPercent > 20;

      // for từng buổi điểm danh
      for (let j = 0; j < dateCols.length; j += 1) {
        const att = {
          id: attIdCols[j],
          date: dateCols[j],
        };
        const checkAbsent = await Absent.checkAbsent({
          attendance: att.id,
          student: student._id,
        });
        // check có nghỉ ko
        if (checkAbsent) {
          if (checkAbsent.dataValues.is_permission === 1) {
            attCols.push('A');
          } else {
            attCols.push('R/A');
          }
        } else {
          attCols.push('P');
        }
      }

      bodyRow.push([
        i + 1,
        `${student.last_name} ${student.first_name}`,
        student.student_id,
        student.email,
        ...attCols,
        dateCols.length,
        totalAbsent,
        `${absentPercent}%`,
        isBanned ? 'Yes' : 'No',
      ]);
    }
    table.push([lecturer]);
    table.push(headerRow);
    table.push(...bodyRow);
    jsonSuccess({
      table,
    }, req, res);
  } catch (error) {
    next(error);
  }
};
exports.reportNoAttendance = async (req, res, next) => {
  try {
    const semester = ['', 'fall', 'spring', 'summer'];
    const { query } = req;
    const bodyRow = [];
    const now = Date.now();
    const dir = uploadDir.att_report;
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir);
    }

    const fileName = `Report-[year-${query.year}][semester-${semester[query.semester]}][${moment(now).format('DD-MM-YY')}][${now}].xlsx`;
    const directory = `${dir}/${fileName}`;
    const blackBorder = {
      border: {
        top: { style: 'medium', color: { rgb: '696969' } },
        bottom: { style: 'medium', color: { rgb: '696969' } },
        right: { style: 'medium', color: { rgb: '696969' } },
        left: { style: 'medium', color: { rgb: '696969' } },
      },
    };
    const bodyStyle = {
      font: { name: 'Times New Roman', sz: 12 },
    };

    const classes = await Class.list({ perPage: -1, ...query });
    for (let i = 0; i < classes.data.length; i += 1) {
      const item = classes.data[i].dataValues;
      const attendances = await Attendance.list({
        _class: item._id,
        lecturerModel: Lecturer,
        classModel: Class,
      });
      if (attendances.total === 0) {
        const isExistLecturer = await Lecturer.findOne({
          attributes: [
            [lecturerFields._id, '_id'],
          ],
          where: {
            [lecturerFields._id]: item.lecturer,
            [lecturerFields.deleted_at]: null,
          },
        });
        if (isExistLecturer) {
          const lecturer = await Lecturer.get({ id: item.lecturer, departmentModel: Department });
          if (lecturer.dataValues.email !== '<EMAIL>') {
            bodyRow.push([
              { v: i + 1, t: 's', s: { ...blackBorder, ...bodyStyle } },
              { v: item.name_vn, t: 's', s: { ...blackBorder, ...bodyStyle } },
              { v: item.code, t: 's', s: { ...blackBorder, ...bodyStyle } },
              { v: `${lecturer.dataValues.last_name} ${lecturer.dataValues.first_name}`, t: 's', s: { ...blackBorder, ...bodyStyle } },
              { v: lecturer.dataValues.email, t: 's', s: { ...blackBorder, ...bodyStyle } },
              { v: lecturer.dataValues.department.dataValues.name_vn, t: 's', s: { ...blackBorder, ...bodyStyle } },
            ]);
          }
        } else {
          bodyRow.push([
            { v: i + 1, t: 's', s: { ...blackBorder, ...bodyStyle } },
            { v: item.name_vn, t: 's', s: { ...blackBorder, ...bodyStyle } },
            { v: item.code, t: 's', s: { ...blackBorder, ...bodyStyle } },
            { v: '', t: 's', s: { ...blackBorder, ...bodyStyle } },
            { v: '', t: 's', s: { ...blackBorder, ...bodyStyle } },
            { v: '', t: 's', s: { ...blackBorder, ...bodyStyle } },
          ]);
        }
      }
    }
    const headerStyle = {
      font: { bold: true, name: 'Times New Roman', sz: 12 },
      alignment: { vertical: 'center', horizontal: 'center', wrapText: true },
    };
    const DateRow = [{ v: `Year: ${query.year}, semester: ${semester[query.semester]}`, t: 's', s: { font: { name: 'Times New Roman', sz: 12 } } }];

    const headerRow = [
      { v: 'No.', t: 's', s: { ...headerStyle, ...blackBorder } },
      { v: 'Class', t: 's', s: { ...headerStyle, ...blackBorder } },
      { v: 'Code', t: 's', s: { ...headerStyle, ...blackBorder } },
      { v: 'Lecturer name', t: 's', s: { ...headerStyle, ...blackBorder } },
      { v: 'Lecturer email', t: 's', s: { ...headerStyle, ...blackBorder } },
      { v: 'Lecturer department', t: 's', s: { ...headerStyle, ...blackBorder } },
    ];

    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet([
      [{
        v: `${moment(now).format('LLLL')} by ${req.user.email}`,
        t: 's',
      }],
      [],
      DateRow,
      headerRow,
      ...bodyRow,
    ]);
    const sheet_name = 'Result';

    XLSX.utils.book_append_sheet(workbook, worksheet, sheet_name);
    XLSX.writeFile(workbook, `${directory}`);
    jsonSuccess({ fileName, directory: `att_report/${fileName}` }, req, res);
  } catch (error) {
    next(error);
  }
};
