const Joi = require('joi');

module.exports = {

  // GET /v1/student/component-scores
  listComponentScores: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      student: Joi.number(),
      semester: Joi.number(),
      _class: Joi.number(),
      school: Joi.number(),
      cohort: Joi.number(),
    }),
  },

  // POST /v1/student/component-scores
  createComponentScore: {
    body: Joi.object({
      year: Joi.number().required(),
      semester: Joi.number().required(),
      grade_component: Joi.number().required(),
      class: Joi.number().required(),
      course: Joi.number(),
      student: Joi.number().required(),
      school: Joi.number(),
      cohort: Joi.number(),
      ten_point_scale: Joi.string(),
      description: Joi.string().allow('').optional(),
      note: Joi.string().allow('').optional(),
    }),
  },

  // PATCH /v1/student/component-scores/:id
  updateComponentScore: {
    body: Joi.object({
      year: Joi.number(),
      semester: Joi.number(),
      grade_component: Joi.number(),
      class: Joi.number(),
      course: Joi.number(),
      student: Joi.number(),
      school: Joi.number(),
      cohort: Joi.number(),
      ten_point_scale: Joi.string(),
      description: Joi.string().allow('').optional(),
      note: Joi.string().allow('').optional(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/student/component-scores/:id
  deleteComponentScore: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },
};
