// 19/02/2021
const moment = require('moment-timezone');
const { handler: errorHand<PERSON> } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const db = require('../../../../config/mysql');

const PermissionInterface = db.userPermissionInterface;

/**
 * Load userPermissionInterface and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const userPermissionInterface = await PermissionInterface.get(id);
    req.locals = { userPermissionInterface };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get userPermissionInterface
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.userPermissionInterface, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Create new userPermissionInterface
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    const saved = await PermissionInterface.insert(req.body);

    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing userPermissionInterface
 * @public
 */
exports.update = async (req, res, next) => {
  try {
    await PermissionInterface.patch({
      data: req.body,
      _id: req.params.id,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Get userPermissionInterface list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await PermissionInterface.countItem(req.query);
    const userPermissionInterfaces = await PermissionInterface.list(req.query);
    jsonSuccess({ total: count, docs: userPermissionInterfaces }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete userPermissionInterface
 * @public
 */
exports.remove = async (req, res, next) => {
  try {
    await PermissionInterface.patch({
      data: {
        deleted_at: moment(),
      },
      _id: req.params.id,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

