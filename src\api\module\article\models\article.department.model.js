const mongoose = require('mongoose');
const httpStatus = require('http-status');
const Slug = require('slug');
const { omitBy, isNil } = require('lodash');
const APIError = require('../../../utils/APIError');

/**
 * ArticleDepartment Schema
 * @private
 */
const articleDepartment = new mongoose.Schema({
  title: {
    type: String,
    maxlength: 128,
    trim: true,
  },
  slug: {
    type: String,
    trim: true,
  },
  order: {
    type: Number,
    default: 0,
  },
  is_active: {
    type: Boolean,
    default: true,
  },
  created_by: {
    type: String,
    default: '',
  },
  updated_by: {
    type: String,
    default: '',
  },
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
});

/**
 * Add your
 * - pre-save hooks
 * - validations
 * - virtuals
 */
articleDepartment.pre('save', async function save(next) {
  try {
    /*
    slug
    */
    if (this.isModified('title')) {
      this.slug = Slug(this.title).toLowerCase();
    }
    next();
  } catch (error) {
    next(error);
  }
});

/**
 * Methods
 */
articleDepartment.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'title',
      'slug',
      'order',
      'is_active',
      'created_at',
    ];

    fields.forEach((field) => {
      transformed[field] = this[field];
    });

    return transformed;
  },
});

/**
 * Statics
 */
articleDepartment.statics = {
  /**
   * Get ArticleDepartment
   *
   * @param {ObjectId} id - The objectId of ArticleDepartment.
   * @returns {Promise<Post, APIError>}
   */
  async get(id) {
    try {
      let ArticleDepartment;

      if (mongoose.Types.ObjectId.isValid(id)) {
        ArticleDepartment = await this.findById(id)
          .exec();
      }
      if (ArticleDepartment) {
        return ArticleDepartment;
      }

      throw new APIError({
        message: 'ArticleDepartment does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * List categories in descending order of 'createdAt' timestamp.
   *
   * @param {number} skip - Number of categories to be skipped.
   * @param {number} limit - Limit number of categories to be returned.
   * @returns {Promise<User[]>}
   */
  list({
    page = 1, perPage = 30, sort, title, slug,
  }) {
    perPage = parseInt(perPage, 10);
    page = parseInt(page, 10);
    const options = omitBy({ title, slug }, isNil);
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options)
      .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  },

  /**
   * Count radios.
   * @returns {Promise<Number>}
   */
  async count({
    title, slug,
  }) {
    const options = omitBy({ title, slug }, isNil);
    return this.find(options).count();
  },
};

/**
 * @typedef ArticleDepartment
 */
module.exports = mongoose.model('ArticleDepartment', articleDepartment);
