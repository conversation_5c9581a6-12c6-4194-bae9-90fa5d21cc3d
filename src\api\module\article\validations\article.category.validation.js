const Joi = require('joi');

module.exports = {

  // GET /v1/post-category
  listPostCategories: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      title: Joi.string(),
      is_parent: Joi.bool(),
      sort: Joi.string(),
    }),
  },

  // POST /v1/post-category
  createPostCategory: {
    body: Joi.object({
      title: Joi.string().required(),
      parent: Joi.string(),
    }),
  },

  // PATCH /v1/post-category/:id
  updatePostCategory: {
    body: Joi.object({
      title: Joi.string(),
      parent: Joi.string(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/post-category/:id
  deletePostCategory: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },
};
