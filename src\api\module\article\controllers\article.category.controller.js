/* eslint-disable no-restricted-syntax */
const ArticleCategory = require('../models/article.category.model');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

/**
 * Load article category and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const articleCategory = await ArticleCategory.get(id);
    req.locals = { articleCategory };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get article category
 * @public
 */
exports.get = (req, res) => {
  jsonSuccess(req.locals.articleCategory.transform(), req, res);
};

/**
 * Create new article category
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user._id;
    const category = await ArticleCategory.findOne({ title: req.body.title }).exec();
    if (category) {
      throw new APIError({
        message: 'ArticleCategory already exists',
        status: httpStatus.BAD_REQUEST,
      });
    } else {
      const articleCategory = new ArticleCategory(req.body);
      const savedCategory = await articleCategory.save();
      jsonSuccess(savedCategory.transform(), req, res);
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing article category
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const articleCategory = Object.assign(req.locals.articleCategory, req.body);

  articleCategory.save()
    .then(savedcategory => jsonSuccess(savedcategory.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get article category list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await ArticleCategory.count(req.query);
    const categories = await ArticleCategory.list(req.query);
    for (let i = 0; i < categories.length; i += 1) {
      categories[i].key = categories[i]._id.toString();
      if (categories[i].children.length > 0) {
        for (const item of categories[i].children) {
          item.key = item._id.toString();
        }
      }
    }
    const transformedCategories = categories.map(category => category.transform());

    jsonSuccess({ total: count, docs: transformedCategories }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete article category
 * @public
 */
exports.remove = (req, res, next) => {
  const { articleCategory } = req.locals;

  articleCategory.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};
