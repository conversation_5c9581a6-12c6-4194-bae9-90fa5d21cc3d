// 1/12/2021
const moment = require('moment');
const employeeFields = (require('../../employee/models/employee.model')).fields;
const httpStatus = require('http-status');
const _ = require('lodash');
const { Op } = require('sequelize');
const APIError = require('../../../utils/APIError');

const typeOpts = [['', 'assignment', 'all', 'leave']];
const statusOpts = [[0, 1]];

const fields = {
  table: 't700',
  _id: 'pt700',
  employee: 'fm100', // ID Nhân viên / Giảng viên
  content: 'tv702', // Nội dung
  type: 'tv703', // Loại
  status: 'tn701p', // trạng thái

  deleted_by: 'tl744',
  deleted_at: 'tl745',
  created_by: 'tl747',
  updated_by: 'tl749',
  updated_at: 'tl748',
  created_at: 'tl746',
};

const schema = (sequelize, DataTypes) => {
  const notificationSchema = sequelize.define('Notification', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.content]: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    [fields.type]: {
      type: DataTypes.STRING(150),
      defaultValue: '',
      validate: {
        isIn: typeOpts,
      },
    },
    [fields.status]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: statusOpts,
      },
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Notification = notificationSchema;

  notificationSchema.create = async (data) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      const notification = Notification.build({ ...dbData });
      const saved = await notification.save();
      return saved;
    } catch (error) {
      throw error;
    }
  };

  notificationSchema.get = async ({ id, employeeModel }) => {
    try {
      const notification = await Notification.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.content, 'content'],
          [fields.type, 'type'],
          [fields.status, 'status'],
          [fields.created_at, 'created_at'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
        include: [
          {
            model: employeeModel,
            as: 'employee',
            attributes: [
              [employeeFields._id, '_id'],
              [employeeFields.last_name, 'last_name'],
              [employeeFields.first_name, 'first_name'],
              [employeeFields.phone, 'phone'],
              [employeeFields.email, 'email'],
              [employeeFields.avatar, 'avatar'],
            ],
          },
        ],
      });
      if (notification) {
        return notification;
      }
      throw new APIError({
        message: 'Notification does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  notificationSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    status = { [Op.not]: null },
    employee = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await Notification.countItem({
      [fields.deleted_at]: null,
      [fields.status]: status,
      [fields.employee]: employee,
    });
    const notifications = await Notification.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.content, 'content'],
        [fields.type, 'type'],
        [fields.status, 'status'],
        [fields.created_at, 'created_at'],
        [fields.created_by, 'created_by'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.status]: status,
        [fields.employee]: employee,
      },
      include: {
        model: employeeModel,
        as: 'employee',
        attributes: [
          [employeeFields._id, '_id'],
          [employeeFields.last_name, 'last_name'],
          [employeeFields.first_name, 'first_name'],
          [employeeFields.phone, 'phone'],
          [employeeFields.email, 'email'],
          [employeeFields.avatar, 'avatar'],
        ],
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: notifications };
  };

  notificationSchema.countItem = async (query) => {
    const count = await Notification.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  notificationSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Notification.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  notificationSchema.remove = async ({ id, email }) => {
    try {
      const result = await Notification.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  return notificationSchema;
};

module.exports = {
  schema,
  fields,
  opts: {
    statusOpts,
    typeOpts,
  },
};
