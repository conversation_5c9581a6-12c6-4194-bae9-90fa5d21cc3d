/* eslint-disable no-restricted-syntax */
const ArticleField = require('../models/article.field.model');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

/**
 * Load article field and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const articleField = await ArticleField.get(id);
    req.locals = { articleField };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get article field
 * @public
 */
exports.get = (req, res) => {
  jsonSuccess(req.locals.articleField.transform(), req, res);
};

/**
 * Create new article field
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user._id;
    const category = await ArticleField.findOne({ title: req.body.title }).exec();
    if (category) {
      throw new APIError({
        message: 'ArticleField already exists',
        status: httpStatus.BAD_REQUEST,
      });
    } else {
      const articleField = new ArticleField(req.body);
      const saved = await articleField.save();
      jsonSuccess(saved.transform(), req, res);
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing article field
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const articleField = Object.assign(req.locals.articleField, req.body);

  articleField.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get article field list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await ArticleField.count(req.query);
    const types = await ArticleField.list(req.query);
    const transformed = types.map(type => type.transform());

    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete article field
 * @public
 */
exports.remove = (req, res, next) => {
  const { articleField } = req.locals;

  articleField.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};
