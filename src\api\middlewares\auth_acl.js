/* eslint-disable new-cap */
const node_acl = require('acl');
const passport = require('passport');
const httpStatus = require('http-status');
const APIError = require('../utils/APIError');
const User = require('../module/user/models/user.model');
const acl_permissions = require('../../config/node_acl');

let acl = null;

const handleJWT = (req, res, next, roles) => async (err, user, info) => {
  const error = err || info;
  const logIn = Promise.promisify(req.logIn);
  const apiError = new APIError({
    message: error ? error.message : 'Unauthorized',
    status: httpStatus.UNAUTHORIZED,
    stack: error ? error.stack : undefined,
  });

  try {
    if (error || !user) throw error;
    await logIn(user, { session: false });
  } catch (e) {
    return next(apiError);
  }
  if (!roles.includes(user.role)) {
    apiError.status = httpStatus.FORBIDDEN;
    apiError.message = 'Forbidden';
    return next(apiError);
  } else if (err || !user) {
    return next(apiError);
  }
  req.user = user;
  /* add role -> id user */
  acl.addUserRoles(req.user._id.toString(), req.user.role);
  return next();
};

exports.createAcl = () => {
  /* tạo acl trên memory */
  acl = new node_acl(new node_acl.memoryBackend());

  acl.allow([
    acl_permissions.admin(),
    //   // acl_roles.user(),
  ]);
  acl.addRoleParents('admin', 'user');
};

/* lấy role từ user đã login, nếu hợp lệ bỏ vào req.user và add role vào id */
exports.addUser = (roles = User.roles) => (req, res, next) => {
  passport.authenticate(
    'jwt', { session: false },
    handleJWT(req, res, next, roles),
  )(req, res, next);
};

/* kiểm tra quyền của user -> resouce */
exports.acl_middleware = resouce => (req, res, next) => {
  const permission = req.method.toLowerCase();
  acl.isAllowed(req.user._id.toString(), resouce, permission, (err, allowed) => {
    if (err) {
      console.log(err);
    }
    if (!allowed) {
      const apiError = new APIError({
        message: 'Unauthorized',
        status: httpStatus.UNAUTHORIZED,
      });
      return next(apiError);
    }
    return next();
  });
};

