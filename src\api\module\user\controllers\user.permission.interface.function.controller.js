// 23/02/2021
const moment = require('moment-timezone');
const { handler: errorHand<PERSON> } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const db = require('../../../../config/mysql');

const PermissionInterfaceFunction = db.userPermissionInterfaceFunction;

/**
 * Load userPermissionInterfaceFunction and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const userPermissionInterfaceFunction = await PermissionInterfaceFunction.get(id);
    req.locals = { userPermissionInterfaceFunction };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get userPermissionInterfaceFunction
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.userPermissionInterfaceFunction, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Create new userPermissionInterfaceFunction
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    const saved = await PermissionInterfaceFunction.insert(req.body);

    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing userPermissionInterfaceFunction
 * @public
 */
exports.update = async (req, res, next) => {
  try {
    await PermissionInterfaceFunction.patch({
      data: req.body,
      _id: req.params.id,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Get userPermissionInterfaceFunction list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await PermissionInterfaceFunction.countItem(req.query);
    const userPermissionInterfaces = await PermissionInterfaceFunction.list(req.query);
    jsonSuccess({ total: count, docs: userPermissionInterfaces }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete userPermissionInterfaceFunction
 * @public
 */
exports.remove = async (req, res, next) => {
  try {
    await PermissionInterfaceFunction.patch({
      data: {
        deleted_at: moment(),
      },
      _id: req.params.id,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

