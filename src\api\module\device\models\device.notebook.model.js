// 12/9/2022
// @dnine
const mongoose = require('mongoose');
const { omitBy, isNil } = require('lodash');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const Device = require('./device.model');

/**
 * DeviceNotebook Schema
 * @private
 */
const deviceNotebookSchema = new mongoose.Schema({
  device: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Device',
  },
  start_at: {
    type: Date,
    default: null,
  },
  end_at: {
    type: Date,
    default: null,
  },
  name: {
    type: String,
    default: '',
    trim: true,
    require: true,
  },
  code: {
    type: String,
    default: '',
    trim: true,
  },
  phone: {
    type: String,
    default: '',
    trim: true,
  },
  department: {
    type: String,
    default: '',
    trim: true,
  },
  is_borrow: {
    type: Boolean,
    default: true,
  },
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
});

/**
 * Methods
 */
deviceNotebookSchema.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'device',
      'start_at',
      'end_at',
      'name',
      'code',
      'phone',
      'department',
      'is_borrow',
      'created_at',
    ];

    fields.forEach((field) => {
      transformed[field] = this[field];
    });

    return transformed;
  },
});

deviceNotebookSchema.pre('save', async function save(next) {
  try {
    const doc = this;
    if (this.isModified('is_borrow') && doc.is_borrow === false) {
      const device = await Device.get(doc.device);

      const updated = Object.assign(device, { notebook: undefined, is_borrow: false });
      await updated.save();
    }
    next();
  } catch (error) {
    next(error);
  }
});

deviceNotebookSchema.post('save', async (doc, next) => {
  try {
    if (doc.is_borrow !== false) {
      const device = await Device.get(doc.device);
      const updated = Object.assign(device, { notebook: doc._id, is_borrow: true });
      await updated.save();
    }
    return next();
  } catch (error) {
    return next(error);
  }
});

/**
 * Statics
 */
deviceNotebookSchema.statics = {
  /**
   * Get DeviceNotebook
   *
   * @param {ObjectId} id - The objectId of DeviceNotebook.
   * @returns {Promise<DeviceNotebook, APIError>}
   */
  async get(id) {
    try {
      const DeviceNotebook = await this.findById(id)
        .exec();

      if (DeviceNotebook) {
        return DeviceNotebook;
      }

      throw new APIError({
        message: 'DeviceNotebook does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * List articles in descending order of 'created_at' timestamp.
   * @param {number} skip - Number of articles to be skipped.
   * @param {number} limit - Limit number of articles to be returned.
   * @returns {Promise<DeviceNotebook[]>}
   */
  async list({
    page = 1, perPage = 30,
    sort,
    device,
    start_at,
    phone,
    is_borrow,
    name,
  }) {
    try {
      const options = omitBy({
        name: new RegExp(name, 'i'),
        is_borrow,
        device,
        phone,
      }, isNil);
      if (start_at) {
        options.start_at = {
          $gte: `${start_at}T00:00:00.000Z`,
          $lte: `${start_at}T23:59:00.000Z`,
        };
      }
      const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
      const result = this.find(options)
        .populate('device', '_id name')
        .sort(sortOpts);
      if (perPage > -1) {
        result.skip(perPage * (page - 1)).limit(perPage);
      }
      return result.exec();
    } catch (error) {
      throw error;
    }
  },

  /**
   * Count articles.
   * @returns {Promise<Number>}
   */
  async count({
    device,
    start_at,
    phone,
    is_borrow,
    name,
  }) {
    const options = omitBy({
      name: new RegExp(name, 'i'),
      is_borrow,
      device,
      phone,
    }, isNil);
    if (start_at) {
      options.start_at = {
        $gte: `${start_at}T00:00:00.000Z`,
        $lte: `${start_at}T23:59:00.000Z`,
      };
    }
    return this.find(options).count();
  },

  async getByDevice(device) {
    try {
      const DeviceNotebook = await this.findOne({ device, is_borrow: true })
        .exec();

      if (DeviceNotebook) {
        return DeviceNotebook;
      }

      return null;
    } catch (error) {
      throw error;
    }
  },

};


/**
 * @typedef DeviceNotebook
 */
module.exports = mongoose.model('DeviceNotebook', deviceNotebookSchema);
