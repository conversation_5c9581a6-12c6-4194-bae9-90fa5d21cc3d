
const { handler: errorHand<PERSON> } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const JobDescriptionItem = require('../models/job_description_item.model');

/**
 * Load jobDescriptionItem and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const jobDescriptionItem = await JobDescriptionItem.get(id);
    req.locals = { jobDescriptionItem };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get jobDescriptionItem
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.jobDescriptionItem.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Create new jobDescriptionItem
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user._id;
    const jobDescriptionItem = new JobDescriptionItem(req.body);
    const saved = await jobDescriptionItem.save();

    jsonSuccess(saved.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing jobDescriptionItem
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const jobDescriptionItem = Object.assign(req.locals.jobDescriptionItem, req.body);

  jobDescriptionItem.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get jobDescriptionItem list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await JobDescriptionItem.count(req.query);
    const jdItems = await JobDescriptionItem.list(req.query);
    const transformed = jdItems.map(jobDescriptionItem => jobDescriptionItem.transform());
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete jobDescriptionItem
 * @public
 */
exports.remove = (req, res, next) => {
  const { jobDescriptionItem } = req.locals;

  jobDescriptionItem.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};

