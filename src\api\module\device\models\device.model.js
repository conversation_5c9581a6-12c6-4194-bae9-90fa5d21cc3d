// 21/9/2022
// @dnine
const mongoose = require('mongoose');
const { omitBy, isNil } = require('lodash');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

/**
 * Device Schema
 * @private
 */
const deviceSchema = new mongoose.Schema({
  tag_id: {
    type: String,
    trim: true,
    unique: true,
  },
  name: {
    type: String,
    require: true,
    default: '',
    trim: true,
  },
  serial: {
    type: String,
    default: '',
    trim: true,
  },
  brand: {
    type: String,
    default: '',
    trim: true,
  },
  model: {
    type: String,
    default: '',
    trim: true,
  },
  type: {
    type: String,
    default: '',
    require: true,
    trim: true,
  },
  avatar: {
    type: String,
    default: '',
    trim: true,
  },
  is_borrow: {
    type: Boolean,
    default: false,
  },
  is_faulty: {
    type: Boolean,
    default: false,
  },
  faulty_description: {
    type: String,
    default: '',
    trim: true,
  },
  notebook: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DeviceNotebook',
  },
  created_by: {
    type: String,
    default: '',
  },
  updated_by: {
    type: String,
    default: '',
  },
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
});

/**
 * Methods
 */
deviceSchema.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'tag_id',
      'name',
      'serial',
      'brand',
      'model',
      'type',
      'avatar',
      'is_borrow',
      'notebook',
      'is_faulty',
      'faulty_description',
      'created_at',
      'created_by',
    ];

    fields.forEach((field) => {
      transformed[field] = this[field];
    });

    return transformed;
  },
});

/**
 * Statics
 */
deviceSchema.statics = {
  /**
   * Get Device
   *
   * @param {ObjectId} id - The objectId of Device.
   * @returns {Promise<Device, APIError>}
   */
  async get(id) {
    try {
      const Device = await this.findById(id)
        .exec();

      if (Device) {
        return Device;
      }

      throw new APIError({
        message: 'Device does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  async getByTag(tag_id) {
    try {
      const Device = await this.findOne({ tag_id })
        .exec();

      if (Device) {
        return Device;
      }

      throw new APIError({
        message: 'Device does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * List articles in descending order of 'created_at' timestamp.
   * @param {number} skip - Number of articles to be skipped.
   * @param {number} limit - Limit number of articles to be returned.
   * @returns {Promise<Device[]>}
   */
  async list({
    page = 1, perPage = 30,
    sort,
    type,
    is_borrow,
    name,
    is_faulty,
  }) {
    try {
      const options = omitBy({
        name: new RegExp(name, 'i'),
        type: new RegExp(type, 'i'),
        is_borrow,
        is_faulty,
      }, isNil);
      const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
      const result = this.find(options)
        .populate('notebook', '_id name start_at department phone')
        .sort(sortOpts);
      if (perPage > -1) {
        result.skip(perPage * (page - 1)).limit(perPage);
      }
      return result.exec();
    } catch (error) {
      throw error;
    }
  },

  /**
   * Count articles.
   * @returns {Promise<Number>}
   */
  async count({
    type,
    is_borrow,
    name,
    is_faulty,
  }) {
    const options = omitBy({
      name: new RegExp(name, 'i'),
      is_borrow,
      is_faulty,
      type: new RegExp(type, 'i'),
    }, isNil);
    return this.find(options).count();
  },
};

/**
 * @typedef Device
 */
module.exports = mongoose.model('Device', deviceSchema);
