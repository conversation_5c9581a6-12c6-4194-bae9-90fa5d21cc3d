// 12/11/2024
const _ = require('lodash');
const moment = require('moment');
const httpStatus = require('http-status');
const { Op } = require('sequelize');
const APIError = require('../../../utils/APIError');

const fields = {
  table: 'v400',
  _id: 'pv400',
  year: 'fh050', // Năm học
  semester: 'fh025', // Học kỳ
  grade_component: 'fv450', // Thành phần
  class: 'fb200', // ID Lớp học
  course: 'fh750', // ID Môn học
  student: 'fn100', //  ID Sinh viên
  school: 'fn450', // ID Khoa của sinh viên
  cohort: 'fh050k', // niên khóa
  // ten_point_scale: 'vn403', // Điểm hệ 10
  ten_point_scale: 'vv403', // Điể<PERSON> hệ 10
  description: 'vl438', // mô tả
  note: 'vl439', // ghi chú

  deleted_by: 'vl444',
  deleted_at: 'vl445',
  updated_by: 'vl449',
  updated_at: 'vl448',
  created_by: 'vl447',
  created_at: 'vl446',
};

const schema = (sequelize, DataTypes) => {
  const componentScoreSchema = sequelize.define(
    'ComponentScore',
    {
      [fields._id]: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      [fields.year]: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      [fields.semester]: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      [fields.grade_component]: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      [fields.class]: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      [fields.course]: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      [fields.student]: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      [fields.school]: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      [fields.cohort]: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      [fields.ten_point_scale]: {
        type: DataTypes.STRING(5),
        defaultValue: null,
      },
      [fields.description]: {
        type: DataTypes.TEXT,
        defaultValue: null,
      },
      [fields.note]: {
        type: DataTypes.STRING(512),
        defaultValue: null,
      },

      [fields.deleted_by]: {
        type: DataTypes.STRING(128),
        defaultValue: null,
      },
      [fields.deleted_at]: {
        type: DataTypes.DATEONLY,
        defaultValue: null,
      },
      [fields.created_by]: {
        type: DataTypes.STRING(128),
        defaultValue: null,
      },
      [fields.updated_by]: {
        type: DataTypes.STRING(128),
        defaultValue: null,
      },
    },
    {
      tableName: fields.table,
      createdAt: fields.created_at,
      updatedAt: fields.updated_at,
    },
  );

  const ComponentScore = componentScoreSchema;

  componentScoreSchema.get = async ({ id }) => {
    try {
      const componentScore = await ComponentScore.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.year, 'year'],
          [fields.semester, 'semester'],
          [fields.grade_component, 'grade_component'],
          [fields.class, 'class'],
          [fields.student, 'student'],
          [fields.course, 'course'],
          [fields.school, 'school'],
          [fields.cohort, 'cohort'],
          [fields.ten_point_scale, 'ten_point_scale'],
          [fields.description, 'description'],
          [fields.note, 'note'],
          [fields.created_by, 'created_by'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      if (componentScore) {
        return componentScore;
      }
      throw new APIError({
        message: 'ComponentScore does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  componentScoreSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    student = { [Op.not]: null },
    semester = { [Op.not]: null },
    _class = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await ComponentScore.countItem({
      [fields.deleted_at]: null,
      [fields.student]: student,
      [fields.semester]: semester,
      [fields.class]: _class,
    });
    const componentScores = await ComponentScore.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.year, 'year'],
        [fields.semester, 'semester'],
        [fields.grade_component, 'grade_component'],
        [fields.class, 'class'],
        [fields.course, 'course'],
        [fields.school, 'school'],
        [fields.cohort, 'cohort'],
        [fields.student, 'student'],
        [fields.ten_point_scale, 'ten_point_scale'],
        [fields.description, 'description'],
        [fields.note, 'note'],
        [fields.created_by, 'created_by'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.student]: student,
        [fields.semester]: semester,
        [fields.class]: _class,
      },
      ...pagination,
      order: [[order_by, order_way]],
    });
    return { total: count, data: componentScores };
  };

  componentScoreSchema.countItem = async (query) => {
    const count = await ComponentScore.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  componentScoreSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await ComponentScore.update(
        { ...dbData },
        {
          where: {
            [fields._id]: id,
          },
        },
      );
    } catch (error) {
      throw error;
    }
  };

  componentScoreSchema.remove = async ({ id, email }) => {
    try {
      const result = await ComponentScore.update(
        {
          [fields.deleted_at]: moment().format(),
          [fields.deleted_by]: email,
        },
        {
          where: {
            [fields._id]: id,
            [fields.deleted_at]: null,
          },
        },
      );
      return result;
    } catch (error) {
      throw error;
    }
  };

  return componentScoreSchema;
};

module.exports = {
  schema,
  fields,
};
