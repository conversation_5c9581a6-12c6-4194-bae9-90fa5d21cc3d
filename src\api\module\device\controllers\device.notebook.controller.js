/* eslint-disable no-return-await */
/* eslint-disable no-await-in-loop */
// 12/9/2022
// @dnine
const httpStatus = require('http-status');
const XLSX = require('xlsx');
const moment = require('moment');
const APIError = require('../../../utils/APIError');

const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { uploadDir } = require('../../../../config/vars');
const DeviceNotebook = require('../models/device.notebook.model');
const Device = require('../models/device.model');

/**
 * Load deviceNotebook and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const deviceNotebook = await DeviceNotebook.get(id);
    req.locals = { deviceNotebook };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get deviceNotebook
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.deviceNotebook, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Create new article field
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    const { body } = req;
    const now = new Date();
    body.start_at = now;
    if (body.devices && body.devices.length > 0) {
      for (let i = 0; i < body.devices.length; i += 1) {
        const item = body.devices[i];
        const device = await Device.getByTag(item);
        if (!device.is_borrow) {
          const newNotebook = new DeviceNotebook({ ...req.body, device: device._id });
          await newNotebook.save();
        }
        jsonSuccess({}, req, res);
      }
    } else {
      const notebook = await DeviceNotebook.findOne({
        device: body.device,
        is_borrow: true,
      }).exec();
      if (notebook) {
        throw new APIError({
          message: 'Device not available',
          status: httpStatus.BAD_REQUEST,
        });
      } else {
        const newNotebook = new DeviceNotebook(req.body);
        const saved = await newNotebook.save();
        jsonSuccess(saved.transform(), req, res);
      }
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing deviceNotebook
 * @public
 */
exports.update = (req, res, next) => {
  const now = new Date();
  req.body.end_at = now;
  const deviceNotebook = Object.assign(req.locals.deviceNotebook, req.body);

  deviceNotebook.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get deviceNotebook list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await DeviceNotebook.count(req.query);
    const notebooks = await DeviceNotebook.list(req.query);
    const transformed = notebooks.map(deviceNotebook => deviceNotebook.transform());
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete deviceNotebook
 * @public
 */
exports.remove = async (req, res, next) => {
  try {
    const { deviceNotebook } = req.locals;
    const result = await deviceNotebook.remove();
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.updateMany = async (req, res, next) => {
  try {
    const now = new Date();
    const { body } = req;
    for (let i = 0; i < body.devices.length; i += 1) {
      const item = body.devices[i];
      const device = await Device.getByTag(item);
      const notebook = await DeviceNotebook.getByDevice(device._id);
      if (notebook) {
        const updated = Object.assign(notebook, { is_borrow: body.is_borrow, end_at: now });
        await updated.save();
      }
    }
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.report = async (req, res, next) => {
  try {
    const now = Date.now();
    const dir = uploadDir.att_report;
    const notebooks = await DeviceNotebook.list({ perPage: -1, sort: '{"created_at": 1}' });
    const fileName = `report-muon-tra-thiet-bi[${moment(now).format('DD-MM-YY')}][${now}].xlsx`;
    const directory = `${dir}/${fileName}`;
    const workbook = XLSX.utils.book_new();
    const body = [];
    for (let i = 0; i < notebooks.length; i += 1) {
      const item = notebooks[i];
      body.push([
        i + 1,
        item.device ? item.device.name : '',
        item.start_at,
        item.end_at,
        item.name,
        item.department,
        item.phone,
      ]);
    }
    const worksheet = XLSX.utils.aoa_to_sheet([
      ['STT', 'Thiết bị', 'Thời gian mượn', 'Thời gian trả', 'Người mượn', 'Bộ phận', 'Số điện thoại'],
      ...body,
    ]);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Report');
    XLSX.writeFile(workbook, directory);
    jsonSuccess({ fileName, directory: `att_report/${fileName}` }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.fakeReport = async (req, res, next) => {
  try {
    // kiểm tra ngày có tồn tại
    const isValidDate = (year, month, day) => {
      const date = new Date(year, month - 1, day); // Tháng trong JavaScript bắt đầu từ 0 (0 - 11)
      return date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;
    };
    const randomNumber = limit => Math.floor(Math.random() * limit) + 1;
    const totalNotebook = await DeviceNotebook.count({});
    const totalDevice = await Device.count({});
    const randomRecord = async () => await DeviceNotebook.find({}).skip(randomNumber(totalNotebook - 1)).limit(1);
    const randomDevice = async () => await Device.find({}).skip(randomNumber(totalDevice - 1)).limit(1);
    const fakeRow = async (year, month, day) => {
      const fakeRecord = await randomRecord();
      const fakeDevice = await randomDevice();
      return [
        0,
        fakeDevice[0].name ? fakeDevice[0].name : '',
        `${day}/${month}/${year}`,
        `${day}/${month}/${year}`,
        fakeRecord[0].name,
        fakeRecord[0].department,
        fakeRecord[0].phone,
      ];
    };


    const now = Date.now();
    const dir = uploadDir.att_report;
    const fileName = `report-muon-tra-thiet-bi[${moment(now).format('DD-MM-YY')}][${now}].xlsx`;
    const directory = `${dir}/${fileName}`;
    const workbook = XLSX.utils.book_new();
    const body = [];

    for (let i = 6; i <= 12; i += 1) {
      for (let j = 1; j <= 31; j += 1) {
        if (isValidDate(parseInt(req.query.year, 10), i, j)) {
          const newDate = new Date(`${parseInt(req.query.year, 10)}-${i}-${j}`);
          if (newDate.getDay() !== 0 && newDate.getDay() !== 6) {
            const randomNumberReport = Math.floor(Math.random() * ((8 - 5) + 1)) + 5;
            for (let l = 0; l <= randomNumberReport; l += 1) {
              body.push(await fakeRow(parseInt(req.query.year, 10), i, j));
            }
          } else {
            console.log('?????????????', parseInt(req.query.year, 10), i, j, newDate.getDay());
          }
        }
      }
    }

    const worksheet = XLSX.utils.aoa_to_sheet([
      ['STT', 'Thiết bị', 'Thời gian mượn', 'Thời gian trả', 'Người mượn', 'Bộ phận', 'Số điện thoại'],
      ...body,
    ]);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Report');
    XLSX.writeFile(workbook, directory);
    jsonSuccess({ fileName, directory: `att_report/${fileName}` }, req, res);

    // jsonSuccess({ randomRecord: await randomRecord(), randomDevice: await randomDevice() }, req, res);
  } catch (error) {
    next(error);
  }
};
