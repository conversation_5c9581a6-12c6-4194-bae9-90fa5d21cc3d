const Joi = require('joi');

module.exports = {

  // GET /v1/student/calendar
  listCalendars: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      year: Joi.number().min(2020).max(2099),
    }),
  },

  // POST /v1/student/calendar
  createCalendar: {
    body: Joi.object({
      year: Joi.number().required(),
      semester: Joi.number().required(),
      start_at: Joi.date().required(),
      end_at: Joi.date().required(),
    }),
  },

  // PATCH /v1/student/calendar/:id
  updateCalendar: {
    body: Joi.object({
      start_at: Joi.date(),
      end_at: Joi.date(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/student/calendar/:id
  deleteCalendar: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },
};
