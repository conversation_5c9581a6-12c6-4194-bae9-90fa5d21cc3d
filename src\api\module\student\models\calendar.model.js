//  22/9/2023
const mongoose = require('mongoose');
const { omitBy, isNil } = require('lodash');
const APIError = require('../../../utils/APIError');
const httpStatus = require('http-status');

const calendar = new mongoose.Schema({
  year: {
    type: Number,
    require: true,
  },
  semester: {
    type: Number,
    require: true,
  },
  start_at: {
    type: Date,
    require: true,
  },
  end_at: {
    type: Date,
    require: true,
  },
  created_by: {
    type: String,
    default: '',
  },
  updated_by: {
    type: String,
    default: '',
  },
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
});

calendar.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'year',
      'semester',
      'start_at',
      'end_at',
      'created_by',
      'created_at',
    ];

    fields.forEach((field) => {
      transformed[field] = this[field];
    });

    return transformed;
  },
});

calendar.statics = {
  async get(id) {
    try {
      let Calendar;

      if (mongoose.Types.ObjectId.isValid(id)) {
        Calendar = await this.findById(id)
          .exec();
      }
      if (Calendar) {
        return Calendar;
      }

      throw new APIError({
        message: 'Calendar does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  async list({
    page = 1,
    perPage = 30,
    year,
  }) {
    perPage = parseInt(perPage, 10);
    page = parseInt(page, 10);
    const options = omitBy({ year }, isNil);
    const sortOpts = { semester: 1, year: -1 };
    const result = this.find(options)
      .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  },

  async now(now) {
    try {
      const Calendar = await this.findOne({
        $and: [
          { start_at: { $lte: now } },
          { end_at: { $gte: now } },
        ],
      });
      if (Calendar) {
        return Calendar;
      }
      throw new APIError({
        message: 'Calendar does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

};

module.exports = mongoose.model('Calendar', calendar);
