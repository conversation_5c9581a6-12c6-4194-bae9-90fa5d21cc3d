// make bluebird default Promise
Promise = require('bluebird'); // eslint-disable-line no-global-assign
const { port, env } = require('./config/vars');
const app = require('./config/express');
const mysql = require('./config/mysql');
const mongoose = require('./config/mongoose');
const {
  start,
  // birthdayEmailSend,
  workReportRemind,
} = require('./config/cron');
require('./config/google');

mysql.sequelize.sync({
  alter: false,
  force: false,
});

// open mongoose connection
mongoose.connect();

// run crontab
start();
workReportRemind();
// birthdayEmailSend();

// listen to requests
app.listen(port, () => console.info(`server started on port ${port} (${env})`));

/**
* Exports express
* @public
*/
module.exports = app;
