const Joi = require('joi');

module.exports = {

  // GET /v1/plan/plan
  listPlans: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      name: Joi.string(),
      sort: Joi.string(),
      department: Joi.string(),
      year: Joi.number().min(2000).max(2999),
      is_active: Joi.bool(),
    }),
  },

  // POST /v1/plan/plan
  createPlan: {
    body: Joi.object({
      year: Joi.number().required(),
      department: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
      address: Joi.string().allow(''),
      note: Joi.string().allow(''),
      pay_roll: Joi.number(),
      training_cost: Joi.number(),
      other_cost: Joi.number(),
      workforces: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)).min(1).required(),
      is_active: Joi.bool(),
    }),
  },

  // PATCH /v1/plan/plan/:id
  updatePlan: {
    body: Joi.object({
      year: Joi.number(),
      department: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      address: Joi.string().allow(''),
      note: Joi.string().allow(''),
      pay_roll: Joi.number(),
      training_cost: Joi.number(),
      other_cost: Joi.number(),
      workforces: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)).min(1),
      is_active: Joi.bool(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/plan/plan/:id
  deletePlan: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

};
