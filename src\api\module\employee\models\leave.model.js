// 27/05/2021
const { Op } = require('sequelize');
const _ = require('lodash');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const employeeFields = (require('./employee.model')).fields;
const departmentFields = (require('../../department/models/department.model')).fields;

const approvedOpts = [[0, 1, 2]];
const fields = {
  table: 'm500',
  _id: 'pm500', // ID
  department: 'fn450', // ID Phòng ban / Khoa
  employee: 'fm100', // ID Nhân viên / giảng viên
  reason: 'mv502', // Lý do
  start_at: 'md503', // Ngày bắt đầu
  end_at: 'md503e', // Ng<PERSON>y kết thúc
  is_approved: 'mn504', // trạng thái chấp nhận

  deleted_by: 'ml544', // <PERSON>ail ng<PERSON>ờ<PERSON> x<PERSON>a
  deleted_at: 'ml545', // Th<PERSON>i gian <PERSON>
  created_by: 'ml547', // <PERSON>ail người tạo
  updated_by: 'ml549', // Email người cập nhật
  updated_at: 'ml548',
  created_at: 'ml546',
};

const schema = (sequelize, DataTypes) => {
  const leaveSchema = sequelize.define('Leave', {
    [fields._id]: {
      // ID
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.reason]: {
      // Lý do
      type: DataTypes.TEXT,
      defaultValue: '',
    },
    [fields.is_approved]: {
      // trạng thái chấp nhận
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: approvedOpts,
      },
    },
    [fields.start_at]: {
      // Ngày bắt đầu
      type: DataTypes.DATE,
      allowNull: false,
    },
    [fields.end_at]: {
      // Ngày kết thúc
      type: DataTypes.DATE,
      allowNull: false,
    },
    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Leave = leaveSchema;

  leaveSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    departmentModel,
    employee = { [Op.not]: null },
    department = { [Op.not]: null },
    is_approved = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    if (department) {
      department = { [Op.or]: department };
    }
    const count = await Leave.countItem({
      [fields.deleted_at]: null,
      [fields.employee]: employee,
      [fields.department]: department,
      [fields.is_approved]: is_approved,

    });
    const leave = await Leave.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.reason, 'reason'],
        [fields.start_at, 'start_at'],
        [fields.end_at, 'end_at'],
        [fields.is_approved, 'is_approved'],
        [fields.created_at, 'created_at'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.employee]: employee,
        [fields.department]: department,
        [fields.is_approved]: is_approved,
      },
      include: [{
        model: employeeModel,
        as: 'employee',
        attributes: [
          [employeeFields._id, '_id'],
          [employeeFields.first_name, 'first_name'],
          [employeeFields.last_name, 'last_name'],
          [employeeFields.email, 'email'],
        ],
      }, {
        model: departmentModel,
        as: 'department',
        attributes: [
          [departmentFields._id, '_id'],
          [departmentFields.name, 'name'],
          [departmentFields.name_vn, 'name_vn'],
        ],
      }],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: leave };
  };

  leaveSchema.countItem = async (query) => {
    const count = await Leave.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  leaveSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Leave.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  leaveSchema.remove = async ({ id, employee }) => {
    try {
      const leave = await Leave.destroy({
        where: {
          [fields._id]: id,
          [fields.is_approved]: 0,
          [fields.employee]: employee,
        },
      });
      return leave;
    } catch (error) {
      throw error;
    }
  };

  leaveSchema.get = async (id) => {
    try {
      const leave = await Leave.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.department, 'department'],
          [fields.employee, 'employee'],
          [fields.start_at, 'start_at'],
          [fields.end_at, 'end_at'],
          [fields.is_approved, 'is_approved'],
          [fields.created_at, 'created_at'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      if (leave) {
        return leave;
      }
      throw new APIError({
        message: 'Leave does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  return leaveSchema;
};

module.exports = {
  schema,
  fields,
  opts: { approvedOpts },
};
