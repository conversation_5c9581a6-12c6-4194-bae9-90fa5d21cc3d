const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const { omitBy, isNil } = require('lodash');

const fields = {
  table: 'q350', // DS quyền cá nhân
  _id: 'pq350', // ID Nhóm quyền
  name: 'qv351', // Tên nhóm quyền

  deleted_at: 'ql145', // Thời gian xóa
  created_at: 'ql146', // Thời gian tạo
  created_by: 'ql147', // Email người tạo
  updated_at: 'ql148', // Thời gian cập nhật
  updated_by: 'ql149', // Email người cập nhật
};

const primaryKey = 'pq350';
const defaultSort = 'ql146';
const attributes = [
  [fields._id, '_id'],
  [fields.name, 'name'],
  [fields.created_at, 'created_at'],
  [fields.deleted_at, 'deleted_at'],
];

const schema = (sequelize, DataTypes) => {
  const userPermissionGroupSchema = sequelize.define('UserPermissionGroup', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.name]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const PermissionGroup = userPermissionGroupSchema;

  userPermissionGroupSchema.get = async (id) => {
    try {
      const permissionGroup = await PermissionGroup.findOne({
        attributes,
        where: {
          [primaryKey]: id,
        },
      });
      if (permissionGroup) {
        return permissionGroup;
      }
      throw new APIError({
        message: 'Permission group does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  userPermissionGroupSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = defaultSort,
    order_way = 'desc',
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const list = await PermissionGroup.findAll({
      attributes,
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return list;
  };

  userPermissionGroupSchema.countItem = async () => {
    const count = await PermissionGroup.count();
    return count;
  };

  userPermissionGroupSchema.insert = async (data) => {
    const newData = omitBy({
      [fields.code]: data.code,
      [fields.name]: data.name,

    }, isNil);
    const permissionGroup = await PermissionGroup.create(newData);
    return permissionGroup;
  };

  userPermissionGroupSchema.patch = async ({ data, _id }) => {
    const newData = omitBy({
      [fields.code]: data.code,
      [fields.name]: data.name,
      [fields.deleted_at]: data.deleted_at,
    }, isNil);
    const permissionGroup = await PermissionGroup.update(newData, {
      where: {
        [primaryKey]: _id,
      },
    });
    return permissionGroup;
  };

  return userPermissionGroupSchema;
};

module.exports = {
  schema,
};
