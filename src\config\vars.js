const path = require('path');

// import .env variables
require('dotenv-safe').load({
  path: path.join(__dirname, '../../.env'),
  sample: path.join(__dirname, '../../.env.example'),
});

module.exports = {
  env: process.env.NODE_ENV,
  port: process.env.PORT,
  jwtSecret: process.env.JWT_SECRET,
  jwtExpirationInterval: process.env.JWT_EXPIRATION_MINUTES,
  rolesVar: {
    admin: JSON.parse(process.env.ADMIN_ROLES),
    hr_dept: JSON.parse(process.env.HR_DEPT_ROLES),
  },
  elasticsearch: {
    uri: process.env.ELASTICSEARCH_URI,
  },
  mongo: {
    uri: process.env.NODE_ENV === 'test'
      ? process.env.MONGO_URI_TESTS
      : process.env.MONGO_URI,
  },
  mysql: {
    host: process.env.MYSQL_HOST,
    user: process.env.MYSQL_USER,
    password: process.env.MYSQL_PASSWORD,
    db: process.env.MYSQL_DB,
  },
  logs: process.env.NODE_ENV === 'production' ? 'combined' : 'dev',
  uploadDir: {
    postAvatar: process.env.UPLOAD_POST_AVATAR_DIR,
    userAvatar: process.env.UPLOAD_AVATAR_DIR,
    tmp: process.env.UPLOAD_TMP_DIR,
    post: process.env.UPLOAD_POST_DIR,
    post_content: process.env.UPLOAD_POST_CONTENT_DIR,
    feature: process.env.UPLOAD_FEATURE_DIR,
    avatar: process.env.UPLOAD_AVATAR_DIR,
    external_image: process.env.UPLOAD_EXTERNAL_IMG_DIR,
    pdf: process.env.UPLOAD_PDF_DIR,
    att_report: process.env.UPLOAD_ATT_REPORT_DIR,
    employee_file: process.env.UPLOAD_EMPLOYEE_FILE_DIR,
  },
  mediaDir: {
    directory: process.env.MEDIA_DIRECTORY_DIR,
  },
  mail: {
    host: process.env.MAIL_HOST,
    port: process.env.MAIL_PORT,
    user: process.env.MAIL_USER,
    password: process.env.MAIL_PASSWORD,

    clientId: process.env.MAILER_CLIENT_ID,
    clientSecret: process.env.MAILER_CLIENT_SECRET,
    redirectUri: process.env.MAILER_REDIRECT_URI,
    refreshToken: process.env.MAILER_REFRESH_TOKEN,
    senderEmail: process.env.MAILER_EMAIL,

  },
  domain: process.env.DOMAIN,
  route: {
    article: '/article/article',
    plan: '/plan/plan',
    department: '/department/department',
    jobDescriptionItem: '/plan/job-description-item',
    jobDescription: '/plan/job-description',
    workforce: '/plan/workforce',
    recruitmentRequest: '/plan/recruitment-request',
  },
  googleService: {
    domain: process.env.GOOGLE_SERVICE_DOMAIN,
    token_path: process.env.GOOGLE_TOKEN_PATH,
    credentials_path: process.env.GOOGLE_CREDENTIALS_PATH,
    drive_id: process.env.GOOGLE_DRIVE_ID,
    drive_document_id: process.env.GOOGLE_DRIVE_DOCUMENT_ID,
  },
  workdayVar: {
    start: parseInt(process.env.WORKDAY_START, 10),
    finish: parseInt(process.env.WORKDAY_FINISH, 10),
  },
  birthdayMailVar: {
    hour: process.env.BIRTHDAY_MAIL_HOUR,
    minute: process.env.BIRTHDAY_MAIL_MINUTE,
  },
  registrarEmails: process.env.REGISTRAR_EMAILS.split(','),
  secretaryEmails: process.env.SECRETARY_EMAILS.split(','),
};

