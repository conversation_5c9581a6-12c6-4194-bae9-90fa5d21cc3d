// 8/11/2021
const APIError = require('../../../utils/APIError');
const httpStatus = require('http-status');
const { Op } = require('sequelize');
const moment = require('moment');
const _ = require('lodash');
const employeeFields = (require('../../employee/models/employee.model')).fields;
const departmentFields = (require('../../department/models/department.model')).fields;

const isApproveOpts = [[0, 1]]; // Approval status (0: Disapprove, 1: Approve)
const isDisplayOpts = [[0, 1]];// Publication status (1: Public)
// const statusOpts = [[0, 1]];// Result (0: In process, 1: Finished)
// const levelOpts = [[0, 1]];// Degree of urgency (0: Normal, 1: Emergency)
const fields = {
  table: 't600',
  _id: 'pt600',
  department: 'fn450', // ID Phòng ban / Khoa
  employee: 'fm100', // ID Nhân viên / Giảng viên chủ đạo
  is_approve: 'tn601', // Trạng thái xét duyệt
  approved_at: 'td601', // Ngày xét duyệt
  is_display: 'tn601p', // Trạng thái hiển thị
  public_at: 'td601p', // Ngày công bố
  title: 'tv602', // Công việc
  // status: 'tn604', // Kết quả
  // start_date: 'td607', // Thời gian bắt đầu
  // end_date: 'td607r', // Thời gian kết thúc dự kiện
  // start_time: 'tv608', // Giờ bắt đầu
  // end_time: 'tv608r', // Giờ kết thúc dự kiến
  // description: 'tl638', // Mô tả
  note: 'tl639', // Ghi chú
  // level: 'tn605', // Mức độ khẩn cấp

  deleted_by: 'tl644', // Email người xóa
  deleted_at: 'tl645', // Thời gian xóa
  created_by: 'tl647', // Email người tạo
  updated_by: 'tl649', // Email người cập nhật
  updated_at: 'tl648',
  created_at: 'tl646',
};

const schema = (sequelize, DataTypes) => {
  const assignmentSchema = sequelize.define('Assignment', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.is_approve]: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      validate: {
        isIn: isApproveOpts,
      },
    },
    [fields.approved_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.is_display]: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      validate: {
        isIn: isDisplayOpts,
      },
    },
    // [fields.level]: {
    //   type: DataTypes.INTEGER,
    //   defaultValue: 1,
    //   validate: {
    //     isIn: levelOpts,
    //   },
    // },
    [fields.public_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.title]: {
      type: DataTypes.STRING(150),
      defaultValue: null,
    },
    // [fields.status]: {
    //   type: DataTypes.INTEGER,
    //   defaultValue: 0,
    //   validate: {
    //     isIn: statusOpts,
    //   },
    // },
    // [fields.start_date]: {
    //   type: DataTypes.DATEONLY,
    //   defaultValue: null,
    // },
    // [fields.end_date]: {
    //   type: DataTypes.DATEONLY,
    //   defaultValue: null,
    // },
    // [fields.start_time]: {
    //   type: DataTypes.STRING(10),
    //   defaultValue: null,
    // },
    // [fields.end_time]: {
    //   type: DataTypes.STRING(10),
    //   defaultValue: null,
    // },
    // [fields.description]: {
    //   type: DataTypes.TEXT,
    //   defaultValue: null,
    // },
    [fields.note]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Assignment = assignmentSchema;

  assignmentSchema.get = async ({ id, employeeModel }) => {
    try {
      const assignment = await Assignment.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.is_approve, 'is_approve'],
          [fields.is_display, 'is_display'],
          [fields.title, 'title'],
          // [fields.status, 'status'],
          // [fields.start_date, 'start_date'],
          // [fields.end_date, 'end_date'],
          // [fields.start_time, 'start_time'],
          // [fields.end_time, 'end_time'],
          // [fields.description, 'description'],
          [fields.note, 'note'],
          [fields.created_by, 'created_by'],
          [fields.created_at, 'created_at'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
        include: [
          {
            model: employeeModel,
            as: 'employee',
            attributes: [
              [employeeFields._id, '_id'],
              [employeeFields.first_name, 'first_name'],
              [employeeFields.last_name, 'last_name'],
              [employeeFields.email, 'email'],
              [employeeFields.phone, 'phone'],
            ],
          },
        ],
      });
      if (assignment) {
        return assignment;
      }
      throw new APIError({
        message: 'Assignment does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  assignmentSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    departmentModel,
    _id = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    if (_id && _id.length > 0) {
      _id = { [Op.or]: _id };
    } else if (_id && _id.length === 0) {
      _id = -1;
    }
    const count = await Assignment.countItem({
      [fields.deleted_at]: null,
      [fields._id]: _id,
    });
    const assignments = await Assignment.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.is_approve, 'is_approve'],
        [fields.approved_at, 'approved_at'],
        [fields.is_display, 'is_display'],
        [fields.public_at, 'public_at'],
        [fields.title, 'title'],
        // [fields.status, 'status'],
        // [fields.start_date, 'start_date'],
        // [fields.end_date, 'end_date'],
        // [fields.start_time, 'start_time'],
        // [fields.end_time, 'end_time'],
        // [fields.description, 'description'],
        [fields.note, 'note'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields._id]: _id,

      },
      include: [
        {
          model: employeeModel,
          as: 'employee',
          attributes: [
            [employeeFields._id, '_id'],
            [employeeFields.first_name, 'first_name'],
            [employeeFields.last_name, 'last_name'],
            [employeeFields.email, 'email'],
          ],
        }, {
          model: departmentModel,
          as: 'department',
          attributes: [
            [departmentFields._id, '_id'],
            [departmentFields.name, 'name'],
            [departmentFields.name_vn, 'name_vn'],
            [departmentFields.code, 'code'],
          ],
        },
      ],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: assignments };
  };

  assignmentSchema.countItem = async (query) => {
    const count = await Assignment.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  assignmentSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Assignment.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  assignmentSchema.remove = async ({ id, email }) => {
    try {
      const result = await Assignment.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  return assignmentSchema;
};

module.exports = {
  schema,
  fields,
  opts: {
    isApproveOpts,
    isDisplayOpts,
    // statusOpts,
  },
};
