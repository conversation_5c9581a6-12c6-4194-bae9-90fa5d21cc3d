// 20/2/24
const { Op } = require('sequelize');
const _ = require('lodash');
const moment = require('moment');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const departmentFields = (require('../../department/models/department.model')).fields;
const majorFields = (require('../../student/models/major.model')).fields;

const fields = {
  table: 'v950',
  _id: 'pv950',
  year: 'fh050',
  department: 'fn450',
  major: 'fn500',
  student_code: 'vv951',
  student_id_card: 'vv945',
  student_dob: 'vd946',
  student_email: 'vv947',
  student_name: 'vv948',
  student_name_en: 'vv949',
  registration_number: 'vv953',
  type: 'vn954',
  issuance_place: 'vv955',
  issuance_date: 'vd956',
  number: 'vv957',
  number_ttu: 'vv963',
  rank: 'vn958',
  status: 'vn951a',

  deleted_by: 'vl944',
  deleted_at: 'vl945',
  created_by: 'vl947',
  updated_by: 'vl949',
  updated_at: 'vl948',
  created_at: 'vl946',
};

const schema = (sequelize, DataTypes) => {
  const certificateSchema = sequelize.define('Certificate', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.year]: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    [fields.student_code]: {
      type: DataTypes.STRING(32),
      allowNull: false,
    },
    [fields.student_id_card]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.student_dob]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.student_email]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.student_name]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.student_name_en]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.registration_number]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.type]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.issuance_place]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.issuance_date]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.number]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.number_ttu]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.rank]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.status]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Certificate = certificateSchema;

  certificateSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    departmentModel,
    majorModel,
    number = {
      [Op.or]: [
        { [Op.not]: null },
        { [Op.is]: null },
      ],
    },
    type,
    student_dob,
    student_name_en,
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const removeVietnameseDiacritics = str => str.normalize('NFD').replace(/[̀-ͯ]/g, '')
      .replace(/Đ/g, 'D').replace(/đ/g, 'd');
    console.log('????????????????????', student_name_en, removeVietnameseDiacritics(student_name_en));
    const count = await Certificate.countItem({
      [fields.deleted_at]: null,
      [fields.status]: 1,
      [Op.or]: [
        { [fields.number]: number },
        { [fields.number_ttu]: number },
      ],
      [fields.type]: type,
      [fields.student_dob]: student_dob,
      [fields.student_name_en]: { [Op.like]: `%${removeVietnameseDiacritics(student_name_en)}%` },
    });
    const certificates = await Certificate.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.year, 'year'],
        [fields.student_code, 'student_code'],
        [fields.student_id_card, 'student_id_card'],
        [fields.student_dob, 'student_dob'],
        [fields.student_email, 'student_email'],
        [fields.student_name, 'student_name'],
        [fields.registration_number, 'registration_number'],
        [fields.type, 'type'],
        [fields.issuance_place, 'issuance_place'],
        [fields.issuance_date, 'issuance_date'],
        [fields.number, 'number'],
        [fields.number_ttu, 'number_ttu'],
        [fields.rank, 'rank'],
        [fields.status, 'status'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.status]: 1,
        [Op.or]: [
          { [fields.number]: number },
          { [fields.number_ttu]: number },
        ],
        [fields.type]: type,
        [fields.student_dob]: student_dob,
        [fields.student_name_en]: { [Op.like]: `%${removeVietnameseDiacritics(student_name_en)}%` },
      },
      include: [
        {
          model: departmentModel,
          as: 'department',
          attributes: [
            [departmentFields._id, '_id'],
            [departmentFields.name, 'name'],
            [departmentFields.name_vn, 'name_vn'],
            [departmentFields.code, 'code'],
          ],
        },
        {
          model: majorModel,
          as: 'major',
          attributes: [
            [majorFields._id, '_id'],
            [majorFields.name_vn, 'name_vn'],
          ],
        },
      ],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: certificates };
  };

  certificateSchema.countItem = async (query) => {
    const count = await Certificate.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  certificateSchema.remove = async ({ id, email }) => {
    try {
      const result = await Certificate.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  certificateSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Certificate.update({
        ...dbData,
        [fields.updated_at]: moment().format(),
      }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  certificateSchema.get = async ({ id }) => {
    try {
      const certificate = await Certificate.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.year, 'year'],
          [fields.student_code, 'student_code'],
          [fields.student_id_card, 'student_id_card'],
          [fields.student_dob, 'student_dob'],
          [fields.student_email, 'student_email'],
          [fields.student_name, 'student_name'],
          [fields.registration_number, 'registration_number'],
          [fields.type, 'type'],
          [fields.issuance_place, 'issuance_place'],
          [fields.issuance_date, 'issuance_date'],
          [fields.number, 'number'],
          [fields.rank, 'rank'],
          [fields.status, 'status'],
        ],
        where: {
          [fields.deleted_at]: null,
          [fields.status]: 1,
          [fields._id]: id,
        },
      });
      if (certificate) {
        return certificate;
      }

      throw new APIError({
        message: 'Certificate does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  return certificateSchema;
};

module.exports = {
  schema,
  fields,
};
