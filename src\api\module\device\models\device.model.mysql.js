// 12/9/2022
// @dnine
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

const fields = {
  table: 'a100',
  _id: 'pa100',
  type: 'av102',
  model: 'av103',
  name: 'av108',

  deleted_by: 'al144', // Email người xóa
  deleted_at: 'al145', // Thời gian xóa
  created_at: 'al146', // Thời gian tạo
  created_by: 'al147', // Email người tạo
  updated_at: 'al148', // Thời gian cập nhật
  updated_by: 'al149', // Email người cập nhật
};

const schema = (sequelize, DataTypes) => {
  const deviceSchema = sequelize.define('Device', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.type]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.model]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.name]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },

    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Device = deviceSchema;

  deviceSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    type,
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await Device.countItem({
      [fields.deleted_at]: null,
      [fields.type]: type,
    });
    const device = await Device.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.type, 'type'],
        [fields.model, 'model'],
        [fields.name, 'name'],
        [fields.created_at, 'created_at'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.type]: type,
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: device };
  };

  deviceSchema.countItem = async (query) => {
    const count = await Device.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  deviceSchema.get = async (id) => {
    try {
      const device = await Device.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.type, 'type'],
          [fields.model, 'model'],
          [fields.name, 'name'],
          [fields.created_at, 'created_at'],
          [fields.created_at, 'created_by'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      if (device) {
        return device;
      }
      throw new APIError({
        message: 'Device does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  return deviceSchema;
};

module.exports = {
  schema,
  fields,
};
