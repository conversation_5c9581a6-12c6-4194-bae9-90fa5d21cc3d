const Joi = require('joi');

module.exports = {
  // GET /v1/work/calender
  listWorkCalendars: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      month: Joi.number().min(1).max(12),
      year: Joi.number(),
      week: Joi.number().min(1),
    }),
  },

  // POST /v1/work/calender
  createWorkCalendar: {
    body: Joi.object({
      week: Joi.number().min(1).required(),
      month: Joi.number().min(1).max(12),
      year: Joi.number(),
      start_at: Joi.date().required(),
      end_at: Joi.date().required(),
    }),
  },

  // PATCH /v1/work/calender/:id
  updateWorkCalendar: {
    body: Joi.object({
      week: Joi.number().min(1),
      month: Joi.number().min(1).max(12),
      year: Joi.number(),
      start_at: Joi.date(),
      end_at: Joi.date(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/work/calender/:id
  deleteWorkCalendar: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

};
