// 21/2/2023
const Joi = require('joi');

module.exports = {

  // GET /v1/employee/timekeeping
  listTimekeeping: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
      employee: Joi.number().integer(),
      from: Joi.date(),
      to: Joi.date().greater(Joi.ref('from')),

    }),
  },

  // POST /v1/employee/timekeeping
  createTimekeeping: {
    body: Joi.object({
      employee: Joi.number().integer().required(),
      reason: Joi.string().required(),
      overtime_at: Joi.date().required(),
      type: Joi.number().integer(),
      duration: Joi.number().required(),
    }),
  },

  // PATCH /v1/employee/timekeeping/:id
  updateTimekeeping: {
    body: Joi.object({
      employee: Joi.number().integer(),
      reason: Joi.string(),
      overtime_at: Joi.date(),
      type: Joi.number().integer(),
      duration: Joi.number(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/employee/timekeeping/:id
  deleteTimekeeping: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
