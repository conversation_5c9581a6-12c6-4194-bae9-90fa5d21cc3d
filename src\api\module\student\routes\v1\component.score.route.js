// 22/9/2023
const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/component.score.controller');
const { authorize } = require('../../../../middlewares/auth');
const {
  createComponentScore,
  listComponentScores,
  updateComponentScore,
  deleteComponentScore,
} = require('../../validations/component.score.validation ');

const router = express.Router();

router.param('id', controller.load);

router
  .route('/')
  .get(authorize(), validate(listComponentScores), controller.list)
  .post(authorize(), validate(createComponentScore), controller.create);

router
  .route('/:id')
  .get(controller.get)
  .patch(authorize(), validate(updateComponentScore), controller.update)
  .delete(authorize(), validate(deleteComponentScore), controller.remove);


module.exports = router;
