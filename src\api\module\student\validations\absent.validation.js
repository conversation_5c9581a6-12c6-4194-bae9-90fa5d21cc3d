const Joi = require('joi');

module.exports = {

  // GET /v1/student/absent
  listAbsents: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      attendance: Joi.number().required(),
    }),
  },

  // POST /v1/student/absent
  createAbsent: {
    body: Joi.object({
      student: Joi.number().required(),
      class: Joi.number(),
      attendance: Joi.number(),
      is_permission: Joi.number().integer().valid(...[1, 2]).messages({
        'any.only': '1: có phép, 2: ko phép',
      }),
    }),
  },

  // PATCH /v1/student/absent/:id
  updateAttendance: {
    body: Joi.object({
      title: Joi.string(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/student/absent/:id
  deleteAttendance: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // GET v1/student/absent/list-absence
  listAbsence: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      year: Joi.number(),
      semester: Joi.number(),
      student: Joi.number(),
      _class: Joi.number(),
    }),
  },

  // GET v1/student/absent/student-absence
  studentAbsence: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      year: Joi.number(),
      semester: Joi.number(),
      student: Joi.number().required(),
    }),
  },
};
