const _ = require('lodash');
const { Op } = require('sequelize');
const moment = require('moment');
const employeeFields = require('./employee.model').fields;
const contractFields = require('../../contract/models/contract.model').fields;
const positionFields = require('../../department/models/position.model').fields;
const departmentFields = require('../../department/models/department.model').fields;
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

const fields = {
  table: 'm310',
  _id: 'pm310', // ID
  contract: 'fm300', // ID Hợp đồng
  employee: 'fm100', // ID Nhân viên / giảng viên
  department: 'fn450', // ID Khoa / phòng ban (bảng N450),
  position: 'fn400', // ID <PERSON>ứ<PERSON> v<PERSON> (bảng N400)

  deleted_by: 'ml344', // Email ngườ<PERSON> xóa
  deleted_at: 'ml345', // Thời gian <PERSON>a
  created_at: 'ml346', // Thời gian tạo
  created_by: 'ml347', // Email người tạo
  updated_at: 'ml348', // Thời gian cập nhật
  updated_by: 'ml349', // Email người cập nhật
};

const schema = (sequelize, DataTypes) => {
  const employeePositionSchema = sequelize.define('EmployeePosition', {
    [fields._id]: {
      // ID
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },

    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const EmployeePosition = employeePositionSchema;

  employeePositionSchema.create = async (data) => {
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });
    const employeePosition = EmployeePosition.build({ ...dbData });
    const saved = await employeePosition.save();
    return saved;
  };

  employeePositionSchema.countItem = async (query) => {
    const count = await EmployeePosition.count({
      where: { ...query },
    });
    return count;
  };

  employeePositionSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    departmentModel,
    positionModel,
    contractModel,
    employee = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await EmployeePosition.countItem({
      [fields.deleted_at]: null,
      [fields.employee]: employee,
    });
    const employeePosition = await EmployeePosition.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.created_by, 'created_by'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.employee]: employee,
      },
      include: [
        {
          model: employeeModel,
          as: 'employee',
          attributes: [
            [employeeFields._id, '_id'],
            [employeeFields.first_name, 'first_name'],
            [employeeFields.last_name, 'last_name'],
            [employeeFields.email, 'email'],
          ],
        },
        {
          model: contractModel,
          as: 'contract',
          attributes: [
            [contractFields._id, '_id'],
          ],
        },
        {
          model: departmentModel,
          as: 'department',
          attributes: [
            [departmentFields._id, '_id'],
            [departmentFields.name, 'name'],
            [departmentFields.name_vn, 'name_vn'],
            [departmentFields.type, 'type'],
          ],
        },
        {
          model: positionModel,
          as: 'position',
          attributes: [
            [positionFields._id, '_id'],
            [positionFields.name, 'name'],
            [positionFields.name_vn, 'name_vn'],
          ],
        },
      ],

      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: employeePosition };
  };

  employeePositionSchema.listByEmployee = async ({
    order_by = fields._id,
    order_way = 'desc',
    departmentModel,
    positionModel,
    employee,
  }) => {
    const employeePosition = await EmployeePosition.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.created_by, 'created_by'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.employee]: employee,
      },
      include: [
        {
          model: departmentModel,
          as: 'department',
          attributes: [
            [departmentFields._id, '_id'],
            [departmentFields.name, 'name'],
            [departmentFields.name_vn, 'name_vn'],
            [departmentFields.type, 'type'],
          ],
        },
        {
          model: positionModel,
          as: 'position',
          attributes: [
            [positionFields._id, '_id'],
            [positionFields.name, 'name'],
            [positionFields.name_vn, 'name_vn'],
          ],
        },
      ],
      order: [
        [order_by, order_way],
      ],
    });
    return employeePosition;
  };

  employeePositionSchema.get = async ({
    id,
    employeeModel,
    departmentModel,
    positionModel,
    contractModel,
  }) => {
    try {
      const position = await EmployeePosition.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.created_by, 'created_by'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
        include: [
          {
            model: employeeModel,
            as: 'employee',
            attributes: [
              [employeeFields._id, '_id'],
              [employeeFields.first_name, 'first_name'],
              [employeeFields.last_name, 'last_name'],
              [employeeFields.email, 'email'],
            ],
          },
          {
            model: contractModel,
            as: 'contract',
            attributes: [
              [contractFields._id, '_id'],
            ],
          },
          {
            model: departmentModel,
            as: 'department',
            attributes: [
              [departmentFields._id, '_id'],
              [departmentFields.name, 'name'],
              [departmentFields.name_vn, 'name_vn'],
              [departmentFields.type, 'type'],
            ],
          },
          {
            model: positionModel,
            as: 'position',
            attributes: [
              [positionFields._id, '_id'],
              [positionFields.name, 'name'],
              [positionFields.name_vn, 'name_vn'],
            ],
          },
        ],
      });
      if (position) {
        return position;
      }
      throw new APIError({
        message: 'Position does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  employeePositionSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await EmployeePosition.update({
        ...dbData,
        [fields.updated_at]: moment().format(),
      }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  employeePositionSchema.remove = async ({ id, email }) => {
    try {
      const result = await EmployeePosition.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  return employeePositionSchema;
};

module.exports = {
  schema,
  fields,
};
