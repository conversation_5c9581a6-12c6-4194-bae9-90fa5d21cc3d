// 4/11/2021
const _ = require('lodash');
const { Op } = require('sequelize');
const moment = require('moment');
const employeeFields = (require('../../employee/models/employee.model')).fields;
const carFields = (require('./car.model')).fields;
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

const fields = {
  table: 'r350',
  _id: 'pr350',
  car: 'fr300', // ID Phương tiện di chuyển
  driver: 'fm100', // ID Tài xế (= ID Nhân viên)
  employee: 'fm100r', // ID Ngườ<PERSON> mướn (= ID Nhân viên)
  is_approved: 'rn351', // Trạng thái xét duyệt
  approved_at: 'rd351', // <PERSON><PERSON><PERSON> x<PERSON>t duy<PERSON>t
  borrowed_at: 'rd355', // <PERSON><PERSON><PERSON> mướ<PERSON>
  returned_at: 'rd355r', // <PERSON><PERSON><PERSON> trả
  borrow_time: 'rd356', // Gi<PERSON> mướn
  return_time: 'rd356r', // Gi<PERSON> trả
  start_at: 'rv357', // Nơi xuất phát
  end_at: 'rv357r', // Nơi kết thúc
  reason: 'rv358', // Lý do mướn
  description: 'rl338', // Mô tả

  deleted_by: 'rl344', // Email người xóa
  deleted_at: 'rl345', // Thời gian xóa
  created_by: 'rl347', // Email người tạo
  updated_by: 'rl349', // Email người cập nhật
  updated_at: 'rl348',
  created_at: 'rl346',
};

const schema = (sequelize, DataTypes) => {
  const carScheduleSchema = sequelize.define('CarSchedule', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.is_approved]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.approved_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.borrowed_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.returned_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.borrow_time]: {
      type: DataTypes.STRING(10),
      defaultValue: null,
    },
    [fields.return_time]: {
      type: DataTypes.STRING(10),
      defaultValue: null,
    },
    [fields.reason]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.start_at]: {
      type: DataTypes.STRING(256),
      defaultValue: null,
    },
    [fields.end_at]: {
      type: DataTypes.STRING(256),
      defaultValue: null,
    },
    [fields.description]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const CarSchedule = carScheduleSchema;

  carScheduleSchema.get = async ({ id, employeeModel }) => {
    try {
      const contract = await CarSchedule.findOne({
        attributes: [
          [fields._id, '_id'],

        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
        include: [
          {
            model: employeeModel,
            as: 'driver',
            attributes: [
              [employeeFields._id, '_id'],

            ],
          },
        ],
      });
      if (contract) {
        return contract;
      }
      throw new APIError({
        message: 'Car schedule does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  carScheduleSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    carModel,
    employee = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await CarSchedule.countItem({
      [fields.deleted_at]: null,
      [fields.employee]: employee,
    });
    const carSchedule = await CarSchedule.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.borrowed_at, 'borrowed_at'],
        [fields.returned_at, 'returned_at'],
        [fields.borrow_time, 'borrow_time'],
        [fields.return_time, 'return_time'],
        [fields.is_approved, 'is_approved'],
        [fields.approved_at, 'approved_at'],
        [fields.description, 'description'],
        [fields.reason, 'reason'],
        [fields.start_at, 'start_at'],
        [fields.end_at, 'end_at'],
        [fields.created_by, 'created_by'],
        [fields.created_at, 'created_at'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.employee]: employee,
      },
      include: [
        {
          model: employeeModel,
          as: 'employee',
          attributes: [
            [employeeFields._id, '_id'],
            [employeeFields.first_name, 'first_name'],
            [employeeFields.last_name, 'last_name'],
            [employeeFields.email, 'email'],
            [employeeFields.phone, 'phone'],
          ],
        },
        {
          model: carModel,
          as: 'car',
          attributes: [
            [carFields._id, '_id'],
            [carFields.name, 'name'],
            [carFields.seat, 'seat'],
            [carFields.license_plate, 'license_plate'],
            [carFields.code, 'code'],
          ],
          include: {
            model: employeeModel,
            as: 'driver',
            attributes: [
              [employeeFields._id, '_id'],
              [employeeFields.first_name, 'first_name'],
              [employeeFields.last_name, 'last_name'],
              [employeeFields.email, 'email'],
              [employeeFields.phone, 'phone'],
            ],
          },
        },
      ],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: carSchedule };
  };

  carScheduleSchema.checkAvailable = async ({ borrowed_at, returned_at }) => {
    const carSchedule = await CarSchedule.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.borrowed_at, 'borrowed_at'],
        [fields.returned_at, 'returned_at'],
        [fields.is_approved, 'is_approved'],
        [fields.car, 'car'],
      ],
      where: {
        [fields.deleted_at]: null,
        // [Op.or]: {
        //   [Op.and]: [{ [fields.returned_at]: { [Op.gte]: borrowed_at } },
        //     { [fields.borrowed_at]: { [Op.lte]: borrowed_at } }],
        //   [Op.and]: [{ [fields.returned_at]: { [Op.gte]: returned_at } },
        //     { [fields.borrowed_at]: { [Op.lte]: returned_at } }],
        // },
        [Op.or]: [
          { [fields.borrowed_at]: { [Op.between]: [borrowed_at, returned_at] } },
          { [fields.returned_at]: { [Op.between]: [borrowed_at, returned_at] } },
        ],
      },
    });
    return { data: carSchedule };
  };

  carScheduleSchema.countItem = async (query) => {
    const count = await CarSchedule.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  carScheduleSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await CarSchedule.update({
        ...dbData,
        [fields.updated_at]: moment().format(),
      }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  carScheduleSchema.remove = async ({ id, email }) => {
    try {
      const result = await CarSchedule.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  return carScheduleSchema;
};

module.exports = {
  schema,
  fields,
};
