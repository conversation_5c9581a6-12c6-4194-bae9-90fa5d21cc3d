/* eslint-disable no-await-in-loop */
// 13/6/2023
const WorkReport = require('../models/work.report.model');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');
const db = require('../../../../config/mysql');

const Employee = db.employee;
const Department = db.department;
const UserPermission = db.userPermission;

// const httpStatus = require('http-status');
// const APIError = require('../../../utils/APIError');

exports.load = async (req, res, next, id) => {
  try {
    const workReport = await WorkReport.get(id);
    req.locals = { workReport };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.get = async (req, res) => {
  let report = req.locals.workReport.transform();
  const employee = await Employee.get({ id: report.employee, departmentModel: Department });
  const employee_info = {
    _id: employee.dataValues._id,
    last_name: employee.dataValues.last_name,
    first_name: employee.dataValues.first_name,
    email: employee.dataValues.email,
  };
  report = Object.assign(report, { employee_info });
  jsonSuccess(report, req, res);
};

exports.create = async (req, res, next) => {
  try {
    req.body.employee = req.user._id;
    req.body.created_by = req.user.email;
    req.body.department = req.user.department;

    const workReport = new WorkReport(req.body);
    const saved = await workReport.save();
    const employee = await Employee.get({ id: saved.employee, departmentModel: Department });
    console.log('??????????????????', employee.dataValues);

    jsonSuccess(saved.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = (req, res, next) => {
  req.body.updated_by = req.user.email;
  const workReport = Object.assign(req.locals.workReport, req.body);

  workReport.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

exports.list = async (req, res, next) => {
  try {
    req.query.employee = req.user._id;
    const count = await WorkReport.count(req.query);
    const reports = await WorkReport.list(req.query);
    const transformed = reports.map(type => type.transform());
    for (let i = 0; i < transformed.length; i += 1) {
      const item = transformed[i];
      const employee = await Employee.get({ id: item.employee, departmentModel: Department });
      const employee_info = {
        _id: employee.dataValues._id,
        last_name: employee.dataValues.last_name,
        first_name: employee.dataValues.first_name,
        email: employee.dataValues.email,
      };
      transformed[i] = Object.assign(transformed[i], { employee_info });
    }

    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.manage = async (req, res, next) => {
  try {
    const headers = await UserPermission.listDepartmentHead(req.user.department);
    const departmentList = [];
    if (headers.length > 0) {
      for (let i = 0; i < headers.length; i += 1) {
        const item = headers[i].dataValues;
        const indexOf = item.permission_ui_function_code.lastIndexOf('_');
        const department = item.permission_ui_function_code.slice(
          indexOf + 1,
          item.permission_ui_function_code.length,
        );
        departmentList.push(parseInt(department, 10));
      }
    }
    req.query.department = departmentList;
    const count = await WorkReport.count(req.query);
    const reports = await WorkReport.list(req.query);
    const transformed = reports.map(type => type.transform());
    for (let i = 0; i < transformed.length; i += 1) {
      const item = transformed[i];
      const employee = await Employee.get({ id: item.employee, departmentModel: Department });
      const employee_info = {
        _id: employee.dataValues._id,
        last_name: employee.dataValues.last_name,
        first_name: employee.dataValues.first_name,
        email: employee.dataValues.email,
      };
      transformed[i] = Object.assign(transformed[i], { employee_info });
    }

    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = (req, res, next) => {
  const { workReport } = req.locals;

  workReport.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};
