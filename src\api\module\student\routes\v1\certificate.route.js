// 23/2/24
const express = require('express');
const controller = require('../../controllers/certificate.controller');
const { validate } = require('express-validation');

const {
  list,
} = require('../../validations/certificate.validation');

const router = express.Router();

router.param('id', controller.load);

router
  .route('/')
  .get(validate(list), controller.list);

router
  .route('/:id')
  .get(controller.get);

module.exports = router;
