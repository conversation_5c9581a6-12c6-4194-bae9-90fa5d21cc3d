// const mongoose = require('mongoose');
// const httpStatus = require('http-status');
// const { omitBy, isNil } = require('lodash');
// const APIError = require('../../../utils/APIError');

// const sexOpts = ['female', 'male', 'other'];


// /**
//  * workforce Schema
//  * @private
//  */
// const workforceSchema = new mongoose.Schema({
//   position: {
//     type: String,
//     required: true,
//   },
//   quantity: {
//     type: Number,
//     default: 1,
//   },
//   report: {
//     type: String,
//     trim: true,
//   },
//   salary_range: {
//     type: Number,
//   },
//   sex: {
//     type: String,
//     enum: sexOpts,
//   },
//   deadline: {
//     type: Date,
//   },
//   reason: {
//     type: String,
//   },
//   age: {
//     type: String,
//   },
//   education: {
//     type: String,
//   },
//   note: {
//     type: String,
//   },
//   job_description: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'JobDescription',
//   },
//   created_by: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'User',
//   },
//   updated_by: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'User',
//   },
// }, {
//   timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
// });

// /**
//  * Methods
//  */
// workforceSchema.method({
//   transform() {
//     const transformed = {};
//     const fields = [
//       '_id',
//       'position',
//       'quantity',
//       'report',
//       'salary_range',
//       'sex',
//       'reason',
//       'age',
//       'education',
//       'note',
//       'job_description',
//       'created_at',
//     ];
//     fields.forEach((field) => {
//       transformed[field] = this[field];
//     });

//     return transformed;
//   },
// });

// /**
//  * Statics
//  */
// workforceSchema.statics = {
//   sexOpts,
//   /**
//    * Get workforce
//    *
//    * @param {ObjectId} id - The objectId of workforce.
//    * @returns {Promise<Workforce, APIError>}
//    */
//   async get(id) {
//     try {
//       let workforce;

//       if (mongoose.Types.ObjectId.isValid(id)) {
//         workforce = await this.findById(id).exec();
//       }
//       if (workforce) {
//         return workforce;
//       }

//       throw new APIError({
//         message: 'Workforce does not exist',
//         status: httpStatus.NOT_FOUND,
//       });
//     } catch (error) {
//       throw error;
//     }
//   },

//   /**
//    * List workforce in descending order of 'createdAt' timestamp.
//    *
//    * @param {number} skip - Number of workforce to be skipped.
//    * @param {number} limit - Limit number of workforce to be returned.
//    * @returns {Promise<User[]>}
//    */
//   list({
//     page = 1,
//     perPage = 30,
//     sort,
//     deadline,
//   }) {
//     page = parseInt(page, 10);
//     perPage = parseInt(perPage, 10);
//     const options = omitBy({
//       deadline,
//     }, isNil);
//     const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
//     const result = this.find(options).sort(sortOpts);
//     if (perPage > -1) {
//       result.skip(perPage * (page - 1)).limit(perPage);
//     }
//     return result.exec();
//   },

//   /**
//    * Count workforce.
//    * @returns {Promise<Number>}
//    */
//   async count({
//     deadline,
//   }) {
//     const options = omitBy({
//       deadline,
//     }, isNil);
//     return this.find(options).count();
//   },
// };

// /**
//  * @typedef Workforce
//  */
// module.exports = mongoose.model('Workforce', workforceSchema);
