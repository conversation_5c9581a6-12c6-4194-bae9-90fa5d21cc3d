const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const { omitBy, isNil } = require('lodash');

const fields = {
  table: 'q300', // "DS giao diện quyềnInterfaces"
  _id: 'pq300', // ID Giao diện quyền
  code: 'qv301', // Tên code giao diện quyền
  name: 'qv302', // Tên giao diện quyền

  deleted_at: 'ql145', // Thời gian xóa
  created_at: 'ql146', // Thời gian tạo
  created_by: 'ql147', // <PERSON>ail người tạo
  updated_at: 'ql148', // Thời gian cập nhật
  updated_by: 'ql149', // Email người cập nhật
};
const primaryKey = 'pq300';
const defaultSort = 'ql146';
const attributes = [
  [fields._id, '_id'],
  [fields.code, 'code'],
  [fields.name, 'name'],
  [fields.created_at, 'created_at'],
  [fields.deleted_at, 'deleted_at'],
];

const schema = (sequelize, DataTypes) => {
  const userPermissionInterfaceSchema = sequelize.define('UserPermissionInterface', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.code]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.name]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const PermissionInterface = userPermissionInterfaceSchema;

  userPermissionInterfaceSchema.get = async (id) => {
    try {
      const permissionInterface = await PermissionInterface.findOne({
        attributes,
        where: {
          [primaryKey]: id,
        },
      });
      if (permissionInterface) {
        return permissionInterface;
      }
      throw new APIError({
        message: 'Permission interface does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  userPermissionInterfaceSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = defaultSort,
    order_way = 'desc',
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const list = await PermissionInterface.findAll({
      attributes,
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return list;
  };

  userPermissionInterfaceSchema.countItem = async () => {
    const count = await PermissionInterface.count();
    return count;
  };

  userPermissionInterfaceSchema.insert = async (data) => {
    const newData = omitBy({
      [fields.code]: data.code,
      [fields.name]: data.name,

    }, isNil);
    const permissionInterface = await PermissionInterface.create(newData);
    return permissionInterface;
  };

  userPermissionInterfaceSchema.patch = async ({ data, _id }) => {
    const newPermissionInterface = omitBy({
      [fields.code]: data.code,
      [fields.name]: data.name,
      [fields.deleted_at]: data.deleted_at,
    }, isNil);
    const permissionInterface = await PermissionInterface.update(newPermissionInterface, {
      where: {
        [primaryKey]: _id,
      },
    });
    return permissionInterface;
  };

  return userPermissionInterfaceSchema;
};

module.exports = {
  schema,
};
