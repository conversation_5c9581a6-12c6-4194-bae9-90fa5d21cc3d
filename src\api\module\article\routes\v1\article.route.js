const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/article.controller');
const {
  authorize,
  // ADMIN,
} = require('../../../../middlewares/auth');
const {
  listArticles,
  createArticle,
  updateArticle,
  deleteArticle,
} = require('../../validations/article.validation');

const router = express.Router();

router.param('id', controller.load);

router
  .route('/')
  .get(authorize(), validate(listArticles), controller.list)
  .post(authorize(), validate(createArticle), controller.create);

router
  .route('/schedule')
  .get(validate(listArticles), controller.schedule);

router
  .route('/public')
  .get(validate(listArticles), controller.public);

router
  .route('/import-from-excel')
  .post(authorize(), controller.importFromExcel);

router
  .route('/:id')
  .get(authorize(), controller.get)
  .patch(authorize(), validate(updateArticle), controller.update)
  .delete(authorize(), validate(deleteArticle), controller.remove);

module.exports = router;
