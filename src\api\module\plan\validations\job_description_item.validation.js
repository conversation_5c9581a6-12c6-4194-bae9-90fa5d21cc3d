const Joi = require('joi');

module.exports = {

  // GET /v1/plan/job-description-item
  listJobDescriptionItems: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
    }),
  },

  // POST /v1/plan/job-description-item
  createJobDescriptionItem: {
    body: Joi.object({
      job_description: Joi.string().required(),
      job_output: Joi.string().allow('').optional(),
      is_active: Joi.bool(),
    }),
  },

  // PATCH /v1/plan/job-description-item/:id
  updateJobDescriptionItem: {
    body: Joi.object({
      job_description: Joi.string(),
      job_output: Joi.string().allow('').optional(),
      is_active: Joi.bool(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/plan/job-description-item/:id
  deleteJobDescriptionItem: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

};
