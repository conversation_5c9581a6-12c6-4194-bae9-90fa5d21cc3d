/* eslint-disable no-await-in-loop */
const moment = require('moment');
const CronJob = require('cron').CronJob;
const db = require('./mysql');
const { birthdayMail, reportRemindMail } = require('../api/services/mailer');
const { birthdayMailVar } = require('../config/vars');

const Employee = db.employee;
const Department = db.department;

const birthdayEmailSend = new CronJob(
  `0 ${birthdayMailVar.minute} ${birthdayMailVar.hour} * * *`,
  // 0h ngày 21 mỗi tháng
  (async () => {
    const day = moment().format('DD');
    const month = moment().format('MM');

    const birthdays = await Employee.birthdayToday({ date: `${month}-${day}` });
    for (let i = 0; i < birthdays.length; i += 1) {
      const item = birthdays[i].dataValues;

      await birthdayMail({
        firstName: item.first_name,
        lastName: item.last_name,
        day,
        month,
      });
    }
  }),
  null,
  false,
  'Asia/Ho_Chi_<PERSON>',
);

const job = new CronJob(
  '0 0 0 21 * *',
  // 0h ngày 21 mỗi tháng
  (async () => {
    console.log('Added');
    await Employee.addDays({});

    const now = new Date();
    const nowYear = now.getFullYear();
    const employees = await Employee.list({
      departmentModel: Department,
      is_lecturer: 0,
      perPage: -1,
      work_status: 0,
      is_both_lecturer_employee: 1,
    });

    for (let i = 0; i < employees.total; i += 1) {
      const item = employees.data[i].dataValues;
      if (item.work_start_at) {
        const work_start_at = new Date(item.work_start_at);
        if (nowYear - work_start_at.getFullYear() >= 3) {
          const bonusDays = Math.floor((nowYear - work_start_at.getFullYear()) / 3);
          await Employee.patch({
            id: item._id,
            data: {
              leave_balance_days: item.leave_balance_days + bonusDays,
            },
          });
        }
      }
    }
  }),
  null,
  false,
  'Asia/Ho_Chi_Minh',
);

const workReportRemind = new CronJob(
  '0 30 9 * * fri',
  (async () => {
    const employees = await Employee.list({
      dept: 12,
      work_status: 0,
      departmentModel: Department,
    });
    const arr = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
    for (let i = 0; i < employees.data.length; i += 1) {
      const item = employees.data[i].dataValues;
      if (arr.includes(item.email)) {
        await reportRemindMail({ email: item.email, name: `${item.last_name} ${item.first_name}`, date: moment().format('[ngày] DD [tháng] MM [năm] YYYY') });
      }
    }
  }),
  null,
  false,
  'Asia/Ho_Chi_Minh',
);

exports.start = () => job.start();
exports.birthdayEmailSend = () => birthdayEmailSend.start();
exports.workReportRemind = () => workReportRemind.start();
