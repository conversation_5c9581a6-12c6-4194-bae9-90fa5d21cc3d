
const { handler: errorH<PERSON><PERSON> } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const JobDescription = require('../models/job_description.model');

/**
 * Load jobDescription and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const jobDescription = await JobDescription.get(id);
    req.locals = { jobDescription };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get jobDescription
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.jobDescription, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Create new jobDescription
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user._id;
    const jobDescription = new JobDescription(req.body);
    const saved = await jobDescription.save();

    jsonSuccess(saved.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing jobDescription
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const jobDescription = Object.assign(req.locals.jobDescription, req.body);

  jobDescription.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get jobDescription list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await JobDescription.count(req.query);
    const jobDescriptions = await JobDescription.list(req.query);
    const transformed = jobDescriptions.map(jobDescription => jobDescription.transform());
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete jobDescription
 * @public
 */
exports.remove = (req, res, next) => {
  const { jobDescription } = req.locals;

  jobDescription.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};

