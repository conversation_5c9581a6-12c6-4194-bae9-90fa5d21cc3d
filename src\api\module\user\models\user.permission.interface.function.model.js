
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
// const { omitBy, isNil } = require('lodash');
// const db = require('../../../../config/mysql');

// const PermissionInterface = db.userPermissionInterfaceFunction;

const fields = {
  table: 'q400', // "DS chức năng giao diện quyền Function Interfaces"
  _id: 'pq400', // ID Chức năng giao diện quyền
  permission_interface: 'fq300', // ID Giao diện quyền
  code: 'qv401', // Tên code chức năng giao diện quyền
  name: 'qv402', // Tên chức năng giao diện quyền

  deleted_at: 'ql145', // Thời gian x<PERSON>
  created_at: 'ql146', // Thời gian t<PERSON>o
  created_by: 'ql147', // Email ng<PERSON>ờ<PERSON> t<PERSON>
  updated_at: 'ql148', // Thời gian cập nhật
  updated_by: 'ql149', // Email người cập nhật
};

const primaryKey = 'pq400';
// const defaultSort = 'ql146';
const attributes = [
  [fields._id, '_id'],
  // [fields.permission_interface, 'permission_interface'],
  [fields.code, 'code'],
  [fields.name, 'name'],
  [fields.created_at, 'created_at'],
  [fields.deleted_at, 'deleted_at'],
];

const schema = (sequelize, DataTypes) => {
  const userPermissionInterfaceSchema = sequelize.define('UserPermissionInterface', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.code]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.name]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const PermissionInterfaceFunction = userPermissionInterfaceSchema;

  userPermissionInterfaceSchema.get = async (id) => {
    try {
      const permissionInterfaceFunction = await PermissionInterfaceFunction.findOne({
        // include: [{
        //   model: PermissionInterface,
        //   as: 'permission_interface',
        // }],
        attributes,
        where: {
          [primaryKey]: id,
        },
      });
      if (permissionInterfaceFunction) {
        return permissionInterfaceFunction;
      }
      throw new APIError({
        message: 'Permission interface function does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };


  return userPermissionInterfaceSchema;
};

module.exports = {
  schema,
};
