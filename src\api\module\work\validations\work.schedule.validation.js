const Joi = require('joi');
const { opts } = require('../models/work.schedule.model');

module.exports = {
  // GET /v1/work/schedule
  listWorkSchedules: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
      year_id: Joi.number(),
      is_approved: Joi.number(),
      approved_at: Joi.date(),
      public_date: Joi.date(),
      content_department: Joi.string(),
    }),
  },

  // POST /v1/work/schedule
  createWorkSchedule: {
    body: Joi.object({
      is_approved: Joi.number().integer().valid(...opts.isApprovedOpts[0]),
      is_display: Joi.number().integer().valid(...opts.isDisplayOpts[0]),
      approved_at: Joi.date(),
      public_date: Joi.date(),
      work: Joi.string().allow(''),
      participant: Joi.string().allow(''),
      chairman: Joi.string().allow(''),
      content_department: Joi.string().allow(''),
      place: Joi.string().allow(''),
      start_date: Joi.date(),
      end_time: Joi.string().allow(''),
      start_time: Joi.string().allow(''),
      description: Joi.string().allow(''),
      note: Joi.string().allow(''),
    }),
  },

  // PATCH /v1/work/schedule/:id
  updateWorkSchedule: {
    body: Joi.object({
      is_approved: Joi.number().integer().valid(...opts.isApprovedOpts[0]),
      is_display: Joi.number().integer().valid(...opts.isDisplayOpts[0]),
      approved_at: Joi.date(),
      public_date: Joi.date(),
      work: Joi.string().allow(''),
      participant: Joi.string().allow(''),
      chairman: Joi.string().allow(''),
      content_department: Joi.string().allow(''),
      place: Joi.string().allow(''),
      start_date: Joi.date(),
      end_time: Joi.date(),
      description: Joi.string().allow(''),
      note: Joi.string().allow(''),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/work/schedule/:id
  deleteWorkSchedule: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
