const Joi = require('joi');
const JobDescription = require('../models/job_description.model');

module.exports = {

  // GET /v1/plan/job-description
  listJobDescriptions: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      is_active: Joi.bool(),
      department: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
    }),
  },

  // POST /v1/plan/job-description
  createJobDescription: {
    body: Joi.object({
      position: Joi.string().required(),
      job_code: Joi.string().allow('').optional(),
      department: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
      supervisor: Joi.string().allow('').optional(),
      address: Joi.string().allow('').optional(),
      description_items: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      purpose: Joi.string().allow('').optional(),
      sex: Joi.string().valid(...JobDescription.sexOpts),
      health: Joi.string().allow('').optional(),
      age: Joi.string().allow('').optional(),
      appearance: Joi.string().allow('').optional(),
      education: Joi.string().allow('').optional(),
      profession: Joi.string().allow('').optional(),
      language: Joi.string().allow('').optional(),
      computer_skill: Joi.string().allow('').optional(),
      experience: Joi.string().allow('').optional(),
      competence: Joi.string().allow('').optional(),
      skill: Joi.string().allow('').optional(),
      other_requirement: Joi.string().allow('').optional(),
      outside: Joi.string().allow('').optional(),
      inside: Joi.string().allow('').optional(),
      condition: Joi.string().allow('').optional(),
      is_active: Joi.bool(),
    }),
  },

  // PATCH /v1/plan/job-description/:id
  updateJobDescription: {
    body: Joi.object({
      position: Joi.string(),
      job_code: Joi.string().allow('').optional(),
      department: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      supervisor: Joi.string().allow('').optional(),
      address: Joi.string().allow('').optional(),
      description_items: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      purpose: Joi.string().allow('').optional(),
      sex: Joi.string().valid(...JobDescription.sexOpts),
      health: Joi.string().allow('').optional(),
      age: Joi.string().allow('').optional(),
      appearance: Joi.string().allow('').optional(),
      education: Joi.string().allow('').optional(),
      profession: Joi.string().allow('').optional(),
      language: Joi.string().allow('').optional(),
      computer_skill: Joi.string().allow('').optional(),
      experience: Joi.string().allow('').optional(),
      competence: Joi.string().allow('').optional(),
      skill: Joi.string().allow('').optional(),
      other_requirement: Joi.string().allow('').optional(),
      outside: Joi.string().allow('').optional(),
      inside: Joi.string().allow('').optional(),
      condition: Joi.string().allow('').optional(),
      is_active: Joi.bool(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/plan/job-description/:id
  deleteJobDescription: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

};
