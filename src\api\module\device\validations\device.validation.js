// 23/9/2022
// @dnine
const Joi = require('joi');

module.exports = {

  listDevices: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      sort: Joi.string(),
      is_borrow: Joi.boolean(),
      type: Joi.string(),
      is_faulty: Joi.boolean(),
    }),
  },

  createDevice: {
    body: Joi.object({
      name: Joi.string().required(),
      serial: Joi.string().required(),
      brand: Joi.string(),
      model: Joi.string(),
      type: Joi.string().required(),
      avatar: Joi.string().allow('').optional(),
      tag_id: Joi.string(),
    }),
  },

  updateDevice: {
    body: Joi.object({
      name: Joi.string(),
      serial: Joi.string(),
      brand: Joi.string(),
      type: Joi.string(),
      avatar: Joi.string(),
      model: Joi.string(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  faultyReportDevice: {
    body: Joi.object({
      faulty_description: Joi.string().allow('').optional().required(),
      is_faulty: Joi.boolean().required(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

};
