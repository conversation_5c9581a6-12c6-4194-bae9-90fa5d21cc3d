// 12/9/2022
// @dnine
const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/device.notebook.controller');
const {
  listDeviceNotebooks,
  createDeviceNotebook,
  updateDeviceNotebook,
  updateManyDeviceNotebook,
} = require('../../validations/device.notebook.validation');
const {
  authorize,
  ADMIN,
} = require('../../../../middlewares/auth');

const router = express.Router();

router.param('id', controller.load);

router
  .route('/')
  .get(validate(listDeviceNotebooks), controller.list)
  .post(authorize(ADMIN), validate(createDeviceNotebook), controller.create);

router
  .route('/update-many')
  .patch(authorize(ADMIN), validate(updateManyDeviceNotebook), controller.updateMany);

router
  .route('/report')
  .get(authorize(ADMIN), controller.report);

router
  .route('/fake-report')
  .get(authorize(ADMIN), controller.fakeReport);

router
  .route('/:id')
  .get(controller.get)
  .patch(authorize(ADMIN), validate(updateDeviceNotebook), controller.update)
  .delete(authorize(ADMIN), controller.remove);


module.exports = router;
