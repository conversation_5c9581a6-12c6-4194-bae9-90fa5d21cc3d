// 8/11/2021
const express = require('express');
// const { validate } = require('express-validation');
const controller = require('../../controllers/dashboard.controller');
const {
  authorize,
  // ADMIN,
} = require('../../../../middlewares/auth');
// const {
//   listAssignments,
//   createAssignment,
//   updateAssignment,
//   deleteAssignment,
// } = require('../../validations/assignment.validation');

const router = express.Router();

/**
 * Load plan when API with id route parameter is hit
 */
// router.param('id', controller.load);

router
  .route('/')
  /**
   * @api {get} v1/plan/plan List Posts
   * @apiDescription Get list
   * @apiVersion 1.0.0
   * @apiName ListPosts
   * @apiGroup Post
   * @apiPermission admin
   *
   * @apiHeader {String} Athorization  User's access token
   *
   * @apiParam  {Number{1-}}         [page=1]     List page
   * @apiParam  {Number{1-100}}      [perPage=1]  Per page
   * @apiParam  {String}             [title]      Title
   * @apiParam  {String}             [category]   category
   * @apiParam  {String}             [status]     status
   * @apiParam  {String}             [sort]       sort ex:{"id": -1}
   *
   * @apiSuccess {Number} total Total posts.
   * @apiSuccess {Object[]} docs List of posts.
   */
  // .get(authorize(), validate(listAssignments), controller.list)
  .get(authorize(), controller.dashboard);


module.exports = router;
