// 23/11/2023
const APIError = require('../../../utils/APIError');
const _ = require('lodash');
const httpStatus = require('http-status');
const { Op } = require('sequelize');
const employeeFields = (require('./employee.model')).fields;
const moment = require('moment');

const fields = {
  table: 'z100',
  _id: 'pz100',
  employee: 'fm100',
  name: 'zv102', // tên file
  directory: 'zv103', // địa chỉ file
  type: 'zn104', // mặc định 1

  deleted_by: 'zl144',
  deleted_at: 'zl145',
  created_by: 'zl147',
  updated_by: 'zl149',
  updated_at: 'zl148',
  created_at: 'zl146',
};

const schema = (sequelize, DataTypes) => {
  const employeeFileSchema = sequelize.define('EmployeeFile', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.employee]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.name]: {
      type: DataTypes.STRING(150),
      defaultValue: '',
    },
    [fields.directory]: {
      type: DataTypes.TEXT,
      defaultValue: '',
    },
    [fields.type]: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const EmployeeFile = employeeFileSchema;

  employeeFileSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    employee = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await EmployeeFile.countItem({
      [fields.deleted_at]: null,
      [fields.employee]: employee,
    });
    const file = await EmployeeFile.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.name, 'name'],
        [fields.directory, 'directory'],
        [fields.type, 'type'],
        [fields.created_by, 'created_by'],
        [fields.created_at, 'created_at'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.employee]: employee,
      },
      include: [{
        model: employeeModel,
        as: 'employee',
        attributes: [
          [employeeFields._id, '_id'],
          [employeeFields.first_name, 'first_name'],
          [employeeFields.last_name, 'last_name'],
          [employeeFields.email, 'email'],
        ],
      }],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: file };
  };

  employeeFileSchema.countItem = async (query) => {
    const count = await EmployeeFile.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  employeeFileSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await EmployeeFile.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  employeeFileSchema.remove = async ({ id, email }) => {
    try {
      const deleted = await EmployeeFile.update({
        [fields.deleted_by]: email,
        [fields.deleted_at]: moment().format(),
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return deleted;
    } catch (error) {
      throw error;
    }
  };

  employeeFileSchema.get = async ({ id, employeeModel }) => {
    try {
      const file = await EmployeeFile.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.name, 'name'],
          [fields.directory, 'directory'],
          [fields.type, 'type'],
          [fields.created_by, 'created_by'],
          [fields.created_at, 'created_at'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
        include: [{
          model: employeeModel,
          as: 'employee',
          attributes: [
            [employeeFields._id, '_id'],
            [employeeFields.first_name, 'first_name'],
            [employeeFields.last_name, 'last_name'],
            [employeeFields.email, 'email'],
          ],
        }],
      });
      if (file) {
        return file;
      }
      throw new APIError({
        message: 'Employee file does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  employeeFileSchema.create = async (data) => {
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });
    const file = EmployeeFile.build({ ...dbData });
    const saved = await file.save();
    return saved;
  };

  return EmployeeFile;
};

module.exports = {
  schema,
  fields,
};
