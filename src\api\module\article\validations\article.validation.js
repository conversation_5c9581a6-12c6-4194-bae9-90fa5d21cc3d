const Joi = require('joi');
const Article = require('../models/article.model');

module.exports = {

  // GET /v1/article/article
  listArticles: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      title: Joi.string(),
      slug: Joi.string(),
      status: Joi.string().valid(...Article.statusOpts),
      categories: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      type: Joi.string().valid(...Article.typeOpts),
      sort: Joi.string(),
      tags: Joi.string(),
      sign: Joi.string(),
      document_type: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      number: Joi.string(),
      folder: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      field: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      year: Joi.number(),
      public_date: Joi.date(),
      release_date: Joi.date(),
      original: Joi.string().valid(...Article.originalOpts),
      department: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      parent_folder: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
    }),
  },

  // POST /v1/article/article
  createArticle: {
    body: Joi.object({
      title: Joi.string().required(),
      info: Joi.string().allow('').optional(),
      categories: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)).min(1).required(),
      status: Joi.string().valid(...Article.statusOpts),
      avatar: Joi.string(),
      content: Joi.string().allow('').optional(),
      type: Joi.string().valid(...Article.typeOpts),
      original: Joi.string().valid(...Article.originalOpts),
      order: Joi.number(),
      tags: Joi.array().items(Joi.string()),
      number: Joi.string().allow('').optional(),
      folder: Joi.string().regex(/^[a-fA-F0-9]{24}$/).allow(''),
      document_type: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      field: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      department: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      sign: Joi.string().allow('').optional(),
      attachment: Joi.array().items(Joi.string().allow('').optional()),
      release_date: Joi.date(),
      public_date: Joi.date(),
      allow_users: Joi.array().items(Joi.number()).min(1),
    }),
  },

  // PATCH /v1/article/article/:id
  updateArticle: {
    body: Joi.object({
      title: Joi.string(),
      info: Joi.string().allow('').optional(),
      content: Joi.string().allow('').optional(),
      status: Joi.string().valid(...Article.statusOpts),
      original: Joi.string().valid(...Article.originalOpts),
      categories: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      type: Joi.string().valid(...Article.typeOpts),
      tags: Joi.array().items(Joi.string()),
      order: Joi.number().allow('').optional(),
      avatar: Joi.string().allow('').optional(),
      number: Joi.string().allow('').optional(),
      folder: Joi.string().regex(/^[a-fA-F0-9]{24}$/).allow(''),
      document_type: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      field: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      sign: Joi.string().allow('').optional(),
      attachment: Joi.array().items(Joi.string().allow('').optional()),
      release_date: Joi.date(),
      public_date: Joi.date(),
      department: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      allow_users: Joi.array().items(Joi.number()).min(1),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/article/article/:id
  deleteArticle: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

};
