// 22/9/2023
const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/calendar.controller');
const { authorize } = require('../../../../middlewares/auth');
const {
  createCalendar,
  listCalendars,
  updateCalendar,
  deleteCalendar,
} = require('../../validations/calendar.validation');

const router = express.Router();

router.param('id', controller.load);

router
  .route('/')
  .get(authorize(), validate(listCalendars), controller.list)
  .post(authorize(), validate(createCalendar), controller.create);

router
  .route('/now')
  .get(authorize(), controller.now);

router
  .route('/:id')
  .get(controller.get)
  .patch(authorize(), validate(updateCalendar), controller.update)
  .delete(authorize(), validate(deleteCalendar), controller.remove);


module.exports = router;
