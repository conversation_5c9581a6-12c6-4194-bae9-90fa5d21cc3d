const Joi = require('joi');
const { opts } = require('../models/contract.model');

module.exports = {

  // GET /v1/contract/income-agreement
  listIncomeAgreements: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
      deleted_at: Joi.string(),
      type: Joi.any(),
      term: Joi.any(),
      start_at: Joi.any(),
      end_at: Joi.any(),
      contract_id: Joi.string(),
      employee_name: Joi.string(),
    }),
  },

  // POST /v1/contract/income-agreement
  createIncomeAgreement: {
    body: Joi.object({
      contract: Joi.number().integer(),
      employee: Joi.number().integer(),
      position_represent: Joi.number().integer(),
      income_agreement_id: Joi.string().max(15),
      income_agreement_at: Joi.date(),
      start_at: Joi.date(),
      income_primary: Joi.number().integer(),
      income_other: Joi.number().integer(),
      allowance_responsibility: Joi.number().integer(),
      allowance_gas: Joi.number().integer(),
      allowance_concurrently: Joi.number().integer(),
      allowance_seniority: Joi.number().integer(),
      allowance_lunch: Joi.number().integer(),
      signed_at: Joi.date(),
    }),
  },

  // PATCH /v1/contract/income-agreement/:id
  updateIncomeAgreement: {
    body: Joi.object({
      employee: Joi.number().integer(),
      employee_represent: Joi.number().integer(),
      organization: Joi.string().allow(''),
      contract_id: Joi.string().max(32),
      address: Joi.string().allow(''),
      type: Joi.number().integer().valid(...opts.contractTypeOpts[0]),
      term: Joi.number().integer(),
      phone: Joi.string().max(128).allow(''),
      represent_position: Joi.string().max(128),
      qualification: Joi.string().allow(''),
      start_at: Joi.date(),
      end_at: Joi.date(),
      work_address: Joi.string().allow(''),
      other_address: Joi.string().allow(''),
      work_to_do: Joi.string().allow(''),
      work_time: Joi.string().allow(''),
      requirement: Joi.string().allow(''),
      vehicle: Joi.string().allow(''),
      gross: Joi.number(),
      gross_text: Joi.string().max(512).allow(''),
      gross_text_en: Joi.string().max(512).allow(''),
      payment_method: Joi.string().max(512).allow(''),
      contract_at: Joi.date(),
      signed_at: Joi.date(),
      visiting_faculty_theory_income: Joi.number(),
      visiting_faculty_practice_income: Joi.number(),
      visiting_faculty_hospital_income: Joi.number(),
      is_approved: Joi.number(),
      probationary_tern: Joi.number(),
      probationary_work: Joi.string().allow(''),
      deleted_at: Joi.allow(null),
      deleted_by: Joi.allow(null),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/contract/income-agreement/:id
  deleteIncomeAgreement: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
