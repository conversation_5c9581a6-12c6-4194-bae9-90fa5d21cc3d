// 8/6/2023
const mongoose = require('mongoose');
const httpStatus = require('http-status');
const { omitBy, isNil } = require('lodash');
const APIError = require('../../../utils/APIError');

/**
 * WorkCalendar Schema
 * @private
 */
const workCalender = new mongoose.Schema({
  week: {
    type: Number,
    require: true,
  },
  month: {
    type: Number,
    require: true,
  },
  year: {
    type: Number,
  },
  start_at: {
    type: Date,
    require: true,
  },
  end_at: {
    type: Date,
    require: true,
  },

  created_by: {
    type: String,
    default: '',
  },
  updated_by: {
    type: String,
    default: '',
  },
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
});

/**
 * Methods
 */
workCalender.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'week',
      'month',
      'year',
      'start_at',
      'end_at',
      'created_at',
    ];

    fields.forEach((field) => {
      transformed[field] = this[field];
    });

    return transformed;
  },
});

/**
 * Statics
 */
workCalender.statics = {
  /**
   * Get WorkCalendar
   *
   * @param {ObjectId} id - The objectId of WorkCalendar.
   * @returns {Promise<Post, APIError>}
   */
  async get(id) {
    try {
      let WorkCalendar;

      if (mongoose.Types.ObjectId.isValid(id)) {
        WorkCalendar = await this.findById(id)
          .exec();
      }
      if (WorkCalendar) {
        return WorkCalendar;
      }

      throw new APIError({
        message: 'WorkCalendar does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * List categories in descending order of 'createdAt' timestamp.
   *
   * @param {number} skip - Number of categories to be skipped.
   * @param {number} limit - Limit number of categories to be returned.
   * @returns {Promise<User[]>}
   */
  list({
    page = 1,
    perPage = 30,
    week,
    month,
    sort,
    year,
  }) {
    perPage = parseInt(perPage, 10);
    page = parseInt(page, 10);
    const options = omitBy({ week, month, year }, isNil);
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options)
      .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  },

  /**
   * Count radios.
   * @returns {Promise<Number>}
   */
  async count({
    week, month, year,
  }) {
    const options = omitBy({ week, month, year }, isNil);
    return this.find(options).count();
  },
};

/**
 * @typedef WorkCalendar
 */
module.exports = mongoose.model('WorkCalendar', workCalender);
