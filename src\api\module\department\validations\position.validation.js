const Joi = require('joi');

module.exports = {

  // GET /v1/department/position
  listPositions: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      name: Joi.string(),
      name_vn: Joi.string(),
      order_way: Joi.string().valid(...['asc', 'desc']),
      order_by: Joi.string(),
    }),
  },

  // POST /v1/department/position
  createPosition: {
    body: Joi.object({
      name: Joi.string().required(),
      is_approved: Joi.number(),
      name_vn: Joi.string(),
      code: Joi.string(),
    }),
  },

  // PATCH /v1/department/position/:id
  updatePosition: {
    body: Joi.object({
      name: Joi.string(),
      name_vn: Joi.string(),
      is_approved: Joi.number(),
      code: Joi.string(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/department/position/:id
  deletePosition: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
