const Joi = require('joi');

module.exports = {
  // POST /v1/auth/register
  register: {
    body: Joi.object({
      email: Joi.string().email().required(),
      role: Joi.string().required(),
      name: Joi.string().required().min(2).max(128),
      password: Joi.string().required().min(6).max(128),
    }),
  },

  // POST /v1/auth/login
  login: {
    body: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().required().max(128),
    }),
  },

  // POST /v1/auth/change-password
  changePassword: {
    body: Joi.object({
      password: Joi.string().required().max(128),
      newpassword: Joi.string().required().max(128),
    }),
  },

  changeWithoutPassword: {
    body: Joi.object({
      email: Joi.string().required().email(),
      token: Joi.string().min(18).max(18).required(),
      newpassword: Joi.string().required().max(128),
    }),
  },

  // POST /v1/users/forgot-password
  forgotPassword: {
    body: Joi.object({
      email: Joi.string().email().required(),
    }),
  },

  // POST /v1/auth/checkToken
  checkToken: {
    body: Joi.object({
      email: Joi.string().email().required(),
      refresh_token: Joi.string().required(),
    }),
  },

  // POST /v1/auth/facebook
  // POST /v1/auth/google
  oAuth: {
    body: Joi.object({
      access_token: Joi.string().required(),
    }),
  },

  // POST /v1/auth/refresh
  refresh: {
    body: Joi.object({
      email: Joi.string().email().required(),
      refreshToken: Joi.string().required(),
    }),
  },
};
