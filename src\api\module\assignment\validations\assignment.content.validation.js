// 9/11/2021
const Joi = require('joi');
const { opts } = require('../models/assignment.content.model');

module.exports = {

  // GET /v1/assignment/content
  listAssignmentContents: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
      assignment: Joi.number(),
    }),
  },

  // POST /v1/assignment/content
  createAssignmentContent: {
    body: Joi.object({
      assignment: Joi.number().integer().required(),
      employee_from: Joi.number().integer().required(),
      employee_to: Joi.number().integer().required(),
      note: Joi.string().allow(''),
      description: Joi.string().allow(''),
      is_approve: Joi.number().integer().valid(...opts.isApproveOpts[0]),
      is_display: Joi.number().integer().valid(...opts.isDisplayOpts[0]),
      title: Joi.string().required(),
      start_date: Joi.date(),
      end_date: Joi.date(),
      start_time: Joi.string(),
      end_time: Joi.string(),
    }),
  },

  // PATCH /v1/assignment/content/:id
  updateAssignmentContent: {
    body: Joi.object({
      department: Joi.number().integer(),
      employee: Joi.number().integer(),
      note: Joi.string().allow(''),
      description: Joi.string().allow(''),
      is_approve: Joi.number().integer().valid(...opts.isApproveOpts[0]),
      is_display: Joi.number().integer().valid(...opts.isDisplayOpts[0]),
      status: Joi.number().integer().valid(...opts.statusOpts[0]),
      title: Joi.string().allow(''),
      start_date: Joi.date(),
      end_date: Joi.date(),
      start_time: Joi.string().allow(''),
      end_time: Joi.string().allow(''),
      progress: Joi.number().integer().min(0).max(100),
      attached: Joi.string().allow(''),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/assignment/content/:id
  deleteAssignmentContent: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
