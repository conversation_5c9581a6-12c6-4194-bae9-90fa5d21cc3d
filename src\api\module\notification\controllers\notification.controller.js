// 1/12/2021
const db = require('../../../../config/mysql');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');

const Notification = db.notification;
const Employee = db.employee;

/**
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const notification = await Notification.get({ id, employeeModel: Employee });
    req.locals = { notification };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get notification
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.notification, req, res);
  } catch (error) {
    next(error);
  }
};

// Create and Save a new Notification
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    data.created_by = req.user.email;
    const notification = await Notification.create(data);
    jsonSuccess(notification, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    query.employee = req.user._id;
    const notifications = await Notification.list({ ...query, employeeModel: Employee });
    jsonSuccess(notifications, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await Notification.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await Notification.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.listAll = async (req, res, next) => {
  try {
    const { query } = req;
    const assignments = await Notification.list({
      ...query,
      employeeModel: Employee,
    });

    jsonSuccess(assignments, req, res);
  } catch (error) {
    next(error);
  }
};
