// 23/11/2021
const Jo<PERSON> = require('joi');

module.exports = {

  // GET /v1/article/folder
  listFolders: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      title: Joi.string(),
      parent: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      is_parent: Joi.bool(),
    }),
  },

  // POST /v1/article/folder
  createFolder: {
    body: Joi.object({
      title: Joi.string().required(),
      parent: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      order: Joi.number(),
    }),
  },

  // PATCH /v1/article/folder/:id
  updateFolder: {
    body: Joi.object({
      title: Joi.string().allow('').optional(),
      parent: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      order: Joi.number(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/article/folder/:id
  deleteFolder: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

};
