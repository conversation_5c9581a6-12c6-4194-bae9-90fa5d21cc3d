// 22/2/24

const fields = {
  table: 'n500',
  _id: 'pn500',
  name_vn: 'nv502_vn',

  deleted_by: 'nl544',
  deleted_at: 'nl545',
  created_by: 'nl547',
  updated_by: 'nl549',
  updated_at: 'nl548',
  created_at: 'nl546',
};

const schema = (sequelize, DataTypes) => {
  const majorSchema = sequelize.define('Certificate', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.name_vn]: {
      type: DataTypes.STRING(300),
      defaultValue: null,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  // const Certificate = majorSchema;

  // majorSchema.list = async ({
  //   page = 1,
  //   perPage = 30,
  //   order_by = fields._id,
  //   order_way = 'desc',
  //   number = {
  //     [Op.or]: [
  //       { [Op.not]: null },
  //       { [Op.is]: null },
  //     ],
  //   },
  //   registration_number = {
  //     [Op.or]: [
  //       { [Op.not]: null },
  //       { [Op.is]: null },
  //     ],
  //   },
  // }) => {
  //   page = parseInt(page, 10);
  //   perPage = parseInt(perPage, 10);
  //   let pagination = {};
  //   if (perPage > -1) {
  //     pagination = {
  //       offset: perPage * (page - 1),
  //       limit: perPage,
  //     };
  //   }

  //   const count = await Certificate.countItem({
  //     [fields.deleted_at]: null,
  //     [fields.status]: 1,
  //     [fields.registration_number]: registration_number,
  //     [fields.number]: number,
  //   });
  //   const classes = await Certificate.findAll({
  //     attributes: [
  //       [fields._id, '_id'],
  //       [fields.is_permission, 'is_permission'],
  //     ],
  //     where: {
  //       [fields.deleted_at]: null,
  //       [fields.status]: 1,
  //       [fields.registration_number]: registration_number,
  //     },
  //     include: [
  //       {
  //         model: studentModel,
  //         as: 'student',
  //         attributes: [
  //           [studentFields._id, '_id'],
  //           [studentFields.first_name, 'first_name'],
  //           [studentFields.last_name, 'last_name'],
  //           [studentFields.email, 'email'],
  //           [studentFields.avatar, 'avatar'],
  //         ],
  //       },
  //       {
  //         model: attendanceModel,
  //         as: 'attendance',
  //         attributes: [
  //           [attendanceFields._id, '_id'],
  //           [attendanceFields.year, 'year'],
  //           [attendanceFields.semester, 'semester'],
  //           [attendanceFields.code, 'code'],
  //           [attendanceFields.date, 'date'],
  //         ],
  //       },
  //     ],
  //     ...pagination,
  //     order: [
  //       [order_by, order_way],
  //     ],
  //   });
  //   return { total: count, data: classes };
  // };

  // majorSchema.countItem = async (query) => {
  //   const count = await Certificate.count({
  //     where: {
  //       ...query,
  //     },
  //   });
  //   return count;
  // };

  // majorSchema.remove = async ({ id, email }) => {
  //   try {
  //     const result = await Certificate.update({
  //       [fields.deleted_at]: moment().format(),
  //       [fields.deleted_by]: email,
  //     }, {
  //       where: {
  //         [fields._id]: id,
  //         [fields.deleted_at]: null,
  //       },
  //     });
  //     return result;
  //   } catch (error) {
  //     throw error;
  //   }
  // };

  // majorSchema.patch = async ({ id, data }) => {
  //   try {
  //     const dbData = {};
  //     _.forEach(data, (value, key) => {
  //       dbData[fields[key]] = value;
  //     });
  //     await Certificate.update({
  //       ...dbData,
  //       [fields.updated_at]: moment().format(),
  //     }, {
  //       where: {
  //         [fields._id]: id,
  //       },
  //     });
  //   } catch (error) {
  //     throw error;
  //   }
  // };

  // majorSchema.get = async ({ id, attendanceModel, classModel }) => {
  //   try {
  //     const certificate = await Certificate.findOne({
  //       attributes: [
  //         [fields._id, '_id'],
  //         [fields.is_permission, 'is_permission'],
  //       ],
  //       where: {
  //         [fields.deleted_at]: null,
  //         [fields._id]: id,
  //       },
  //       include: [
  //         {
  //           model: attendanceModel,
  //           as: 'attendance',
  //           attributes: [
  //             [attendanceFields._id, '_id'],
  //             [attendanceFields.created_by, 'created_by'],
  //           ],
  //           include: [
  //             {
  //               model: classModel,
  //               as: 'class',
  //               attributes: [
  //                 [classFields._id, '_id'],
  //                 [classFields.lecturer, 'lecturer'],
  //               ],
  //             },
  //           ],
  //         },
  //       ],
  //     });
  //     if (certificate) {
  //       return certificate;
  //     }

  //     throw new APIError({
  //       message: 'Certificate does not exist',
  //       status: httpStatus.NOT_FOUND,
  //     });
  //   } catch (error) {
  //     throw error;
  //   }
  // };

  return majorSchema;
};

module.exports = {
  schema,
  fields,
};
