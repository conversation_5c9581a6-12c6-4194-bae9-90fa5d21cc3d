const mongoose = require('mongoose');
const { omitBy, isNil } = require('lodash');

const statusOpts = ['sent', 'unsent'];
/**
 * Paycheck Schema
 * @private
 */
const paycheckSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      maxlength: 128,
      trim: true,
    },
    month: {
      type: Number,
      min: 0,
      max: 12,
    },
    year: {
      type: Number,
      min: 2020,
      max: 2100,
    },
    mail: {
      type: String,
      trim: true,
      require: true,
    },
    email: {
      type: String,
      match: /^\S+@\S+\.\S+$/,
      trim: true,
      lowercase: true,
    },
    status: {
      type: String,
      enum: statusOpts,
      default: 'unsent',
    },
    is_active: {
      type: Boolean,
      default: true,
    },
    created_by: {
      type: String,
      default: '',
    },
    updated_by: {
      type: String,
      default: '',
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  },
);

/**
 * Methods
 */
paycheckSchema.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'name',
      'month',
      'year',
      'mail',
      'email',
      'status',
      'is_active',
      'created_at',
    ];

    fields.forEach((field) => {
      if (field === 'categories' && this.categories.length > 0) {
        transformed.categories = this.categories;
        for (let i = 0; i < transformed.categories.length; i += 1) {
          transformed.categories[i].key =
            transformed.categories[i]._id.toString();
        }
      } else {
        transformed[field] = this[field];
      }
    });

    return transformed;
  },
});

/**
 * Statics
 */
paycheckSchema.statics = {
  statusOpts,

  /**
   * List payslips in descending order of 'created_at' timestamp.
   * @param {number} skip - Number of payslips to be skipped.
   * @param {number} limit - Limit number of payslips to be returned.
   * @returns {Promise<Paycheck[]>}
   */
  async list({
    page = 1,
    perPage = 30,
    sort,
    name,
    month,
    year,
    status,
    email,
  }) {
    try {
      perPage = parseInt(perPage, 10);
      page = parseInt(page, 10);
      const options = omitBy(
        {
          name: new RegExp(name || '', 'i'),
          month,
          year,
          status,
          email,
        },
        isNil,
      );
      const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
      const result = this.find(options).sort(sortOpts);
      if (perPage > -1) {
        result.skip(perPage * (page - 1)).limit(perPage);
      }
      return result.exec();
    } catch (error) {
      throw error;
    }
  },

  /**
   * Count payslips.
   * @returns {Promise<Number>}
   */
  async count({
    name,
    month,
    year,
    status,
    email,
  }) {
    const options = omitBy(
      {
        name: new RegExp(name || '', 'i'),
        month,
        year,
        status,
        email,
      },
      isNil,
    );
    return this.find(options).count().exec();
  },
};

/**
 * @typedef Paycheck
 */
module.exports = mongoose.model('Paycheck', paycheckSchema);
