// 8/6/2023
const WorkCalendar = require('../models/work.calendar.model');
// const _ = require('lodash');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

exports.load = async (req, res, next, id) => {
  try {
    const workCalendar = await WorkCalendar.get(id);
    req.locals = { workCalendar };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.get = (req, res) => {
  jsonSuccess(req.locals.workCalendar.transform(), req, res);
};

exports.create = async (req, res, next) => {
  try {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    if (!req.body.year) {
      req.body.year = year;
    }
    req.body.created_by = req.user._id;

    const calender = await WorkCalendar.findOne({
      week: req.body.week,
      month: req.body.month,
      year: req.body.year,
    }).exec();
    if (calender) {
      throw new APIError({
        message: 'WorkCalendar already exists',
        status: httpStatus.BAD_REQUEST,
      });
    } else {
      const workCalendar = new WorkCalendar(req.body);
      const saved = await workCalendar.save();
      jsonSuccess(saved.transform(), req, res);
    }
  } catch (error) {
    next(error);
  }
};

exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const workCalendar = Object.assign(req.locals.workCalendar, req.body);

  workCalendar.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

exports.list = async (req, res, next) => {
  try {
    const count = await WorkCalendar.count(req.query);
    const types = await WorkCalendar.list(req.query);
    const transformed = types.map(type => type.transform());

    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = (req, res, next) => {
  const { workCalendar } = req.locals;

  workCalendar.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};
