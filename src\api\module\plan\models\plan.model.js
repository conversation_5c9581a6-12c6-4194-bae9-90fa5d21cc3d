/* eslint-disable max-len */
// const mongoose = require('mongoose');
// const httpStatus = require('http-status');
// const { omitBy, isNil } = require('lodash');
// const APIError = require('../../../utils/APIError');

// /**
//  * plan Schema
//  * @private
//  */
// const planSchema = new mongoose.Schema({
//   year: {
//     type: Number,
//     required: true,
//   },
//   department: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'Department',
//   },
//   address: {
//     type: String,
//     trim: true,
//   },
//   note: {
//     type: String,
//     trim: true,
//   },
//   pay_roll: {
//     type: Number,
//   },
//   training_cost: {
//     type: Number,
//   },
//   other_cost: {
//     type: Number,
//   },
//   workforces: [{
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'Workforce',
//   }],
//   is_active: {
//     type: Boolean,
//     default: true,
//   },
//   created_by: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'User',
//   },
//   updated_by: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'User',
//   },
// }, {
//   timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
// });

// /**
//  * Methods
//  */
// planSchema.method({
//   transform() {
//     const transformed = {};
//     const fields = [
//       '_id',
//       'year',
//       'department',
//       'address',
//       'pay_roll',
//       'training_cost',
//       'other_cost',
//       'workforces',
//       'note',
//       'created_at',
//       'created_by',
//     ];
//     fields.forEach((field) => {
//       transformed[field] = this[field];
//     });

//     return transformed;
//   },
// });

// /**
//  * Statics
//  */
// planSchema.statics = {
//   /**
//    * Get plan
//    *
//    * @param {ObjectId} id - The objectId of plan.
//    * @returns {Promise<Plan, APIError>}
//    */
//   async get(id) {
//     try {
//       let plan;

//       if (mongoose.Types.ObjectId.isValid(id)) {
//         plan = await this.findById(id)
//           .populate('department', '_id name')
//           .populate({
//             path: 'workforces',
//             select: '_id quantity position report salary_range sex deadline reason job_description',
//             populate: {
//               path: 'job_description',
//               select: '_id department position',
//               populate: {
//                 path: 'department',
//                 select: '_id name',
//               },
//             },
//           })
//           .exec();
//       }
//       if (plan) {
//         return plan;
//       }

//       throw new APIError({
//         message: 'Plan does not exist',
//         status: httpStatus.NOT_FOUND,
//       });
//     } catch (error) {
//       throw error;
//     }
//   },

//   /**
//    * List plan in descending order of 'createdAt' timestamp.
//    *
//    * @param {number} skip - Number of plan to be skipped.
//    * @param {number} limit - Limit number of plan to be returned.
//    * @returns {Promise<User[]>}
//    */
//   list({
//     page = 1,
//     perPage = 30,
//     sort,
//     year,
//     // department,
//     // address,
//     pay_roll,
//     training_cost,
//     other_cost,
//     is_active = true,
//   }) {
//     page = parseInt(page, 10);
//     perPage = parseInt(perPage, 10);
//     const options = omitBy({
//       // address: new RegExp(address, 'i'),
//       // department: new RegExp(department, 'i'),
//       year,
//       pay_roll,
//       training_cost,
//       other_cost,
//       is_active,
//     }, isNil);
//     const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
//     const result = this.find(options)
//       .populate('department', '_id name')
//       .populate('created_by', '_id name')
//       .sort(sortOpts);
//     if (perPage > -1) {
//       result.skip(perPage * (page - 1)).limit(perPage);
//     }
//     return result.exec();
//   },

//   /**
//    * Count plan.
//    * @returns {Promise<Number>}
//    */
//   async count({
//     year,
//     // department,
//     // address,
//     pay_roll,
//     training_cost,
//     other_cost,
//     is_active = true,
//   }) {
//     const options = omitBy({
//       // address: new RegExp(address, 'i'),
//       // department: new RegExp(department, 'i'),
//       year,
//       pay_roll,
//       training_cost,
//       other_cost,
//       is_active,
//     }, isNil);
//     return this.find(options).count();
//   },
// };

// /**
//  * @typedef Plan
//  */
// module.exports = mongoose.model('Plan', planSchema);
