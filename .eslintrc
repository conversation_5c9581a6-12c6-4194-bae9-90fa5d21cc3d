{"rules": {"no-console": 0, "no-underscore-dangle": 0, "no-unused-vars": ["error", {"argsIgnorePattern": "next"}], "no-use-before-define": ["error", {"variables": false}], "camelcase": [0, {"properties": "never"}], "no-param-reassign": [1, {"props": false}], "no-multi-str": 0, "linebreak-style": 0, "prefer-destructuring": ["error", {"object": false, "array": false}]}, "env": {"node": true, "mocha": true}, "parserOptions": {"ecmaVersion": 8}, "extends": ["airbnb-base"]}