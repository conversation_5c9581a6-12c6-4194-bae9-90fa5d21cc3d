const JwtStrategy = require('passport-jwt').Strategy;
const { Op } = require('sequelize');
// const BearerStrategy = require('passport-http-bearer');
const { ExtractJwt } = require('passport-jwt');
const { jwtSecret } = require('./vars');
// const authProviders = require('../api/services/authProviders');
// const User = require('../api/module/user/models/user.model');
const Employee = (require('../config/mysql')).employee;
const UserPermissionList = (require('../config/mysql')).userPermissionList;
const Student = (require('../config/mysql')).student;

const jwtOptions = {
  secretOrKey: jwtSecret,
  jwtFromRequest: ExtractJwt.fromAuthHeaderWithScheme('Bearer'),
};

const jwt = async (payload, done) => {
  try {
    // nếu ko phải sv đăng nhập
    if (!payload.is_student) {
      const employee = await Employee.findById(payload.sub);
      if (employee) {
        const role = await UserPermissionList.findOne({
          where: {
            fm100: payload.sub,
            fq350: {
              [Op.or]: [1, 2, 3, 4, 5, 6, 7, 8],
            },
            ql145: null,
          },
        });
        if (role) {
          return done(null, { ...employee.dataValues, role: role.dataValues.fq350 });
        }
        return done(null, { ...employee.dataValues, role: 7 });
      }
    }
    // nếu là sv đăng nhập
    const student = await Student.findById(payload.sub);
    if (student) {
      return done(null, { ...student.dataValues, role: 0 });
    }
    return done(null, false);
  } catch (error) {
    return done(error, false);
  }
};

exports.jwt = new JwtStrategy(jwtOptions, jwt);
