const Joi = require('joi');
const { opts } = require('../models/employee.model');

module.exports = {

  // GET /v1/employee/employee
  listEmployees: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
      is_lecturer: Joi.any(),
      work_status: Joi.number().integer().valid(...opts.employeeWorkStatusOpts[0]).messages({
        'any.only': '0: <PERSON><PERSON> là<PERSON>, 1: Đã nghỉ',
      }),
      is_both_lecturer_employee: Joi.number().integer().valid(...[0, 1]).messages({
        'any.only': '0: <PERSON>h<PERSON>ng phải, 1: Vừa nhân viên vừa giảng viên',
      }),
      sex: Joi.string(),
      dob: Joi.any(),
      email: Joi.string(),
      name: <PERSON><PERSON>.string(),
      last_name: Joi.string(),
      first_name: Joi.string(),
      deleted_at: Joi.string(),
      dept: Joi.any(),
    }),
  },

  // POST /v1/employee/employee
  createEmployee: {
    body: Joi.object({
      is_foreign: Joi.number().integer().valid(...opts.employeeIsForeignOpts[0]).messages({
        'any.only': '0: trong nước, 1: ngoài nước',
      }),
      is_lecturer: Joi.number().integer().valid(...opts.employeeIsLecturerOpts[0]).messages({
        'any.only': '0: Nhân viên, 1: Giảng viên',
      }),
      last_name: Joi.string(),
      first_name: Joi.string(),
      dob: Joi.date(),
      sex: Joi.string().max(1),
      phone: Joi.string().max(128).allow(''),
      email: Joi.string().email().allow(''),
      email_personal: Joi.string().email().allow(''),
      identification_number: Joi.string().allow(''),
      work_start_at: Joi.date(),
      work_end_at: Joi.date(),
      address: Joi.string().allow(''),
      nationality: Joi.string().allow(''),
      race: Joi.string().allow(''),
      pob: Joi.string().allow(''),
      marital_status: Joi.string().allow(''),
      work_status: Joi.number().integer().valid(...opts.employeeWorkStatusOpts[0]).messages({
        'any.only': '0: Đang làm, 1: Đã nghỉ',
      }),
      experience: Joi.string().allow(''),
      education: Joi.string().allow(''),
      avatar: Joi.string().allow(''),
      pod: Joi.string().allow(''),
      department: Joi.number().integer(),
      major: Joi.string().allow(''),
      school: Joi.string().allow(''),
      academic_level: Joi.string().allow(''),
      dependent_number: Joi.number().integer(),
      bank_account: Joi.string().allow(''),
      bank_name: Joi.string().allow(''),
      tax_number: Joi.string().allow(''),
      social_number: Joi.string().allow(''),
      title: Joi.number().integer().valid(...opts.employeeTitleOpts[0]).messages({
        'any.only': '0: Không, 1: Cử nhân, 2: Thạc sỹ, 3: Tiến sỹ, 4: Tiến sỹ khoa học, 5: Chuyên khoa cấp 1, 6: Chuyên khoa cấp 2, 7: Giáo sư, 8: Phó giáo sư',
      }),
      bank_branch: Joi.string().allow(''),
      is_social_insurance: Joi.number().integer().valid(...opts.isSocialInsuranceOpts[0]).messages({
        'any.only': '0: Có tham gia, 1: Không còn/muốn tham gia',
      }),
      identification_date: Joi.date(),
      identification_place: Joi.string().allow(''),
      contract_type: Joi.number().integer().valid(...opts.contractTypeOpts[0]).messages({
        'any.only': '0: Full-time, 1: Part-time, 2: Thỉnh giảng, 3: Thử việc',
      }),
      address_current: Joi.string().allow(''),
      is_both_lecturer_employee: Joi.number().max(1).min(0),
      timekeeping_code: Joi.string(),
    }),
  },

  // PATCH /v1/employee/employee/:id
  updateEmployee: {
    body: Joi.object({
      is_foreign: Joi.number().integer().valid(...opts.employeeIsForeignOpts[0]).messages({
        'any.only': '0: trong nước, 1: ngoài nước',
      }),
      is_lecturer: Joi.number().integer().valid(...opts.employeeIsLecturerOpts[0]).messages({
        'any.only': '0: Nhân viên, 1: Giảng viên',
      }),
      last_name: Joi.string(),
      first_name: Joi.string(),
      dob: Joi.date(),
      sex: Joi.string().max(1),
      phone: Joi.string().max(128).allow(''),
      email: Joi.string().email().allow(''),
      email_personal: Joi.string().email().allow(''),
      identification_number: Joi.string().allow(''),
      work_start_at: Joi.date(),
      work_end_at: Joi.date(),
      address: Joi.string().allow(''),
      nationality: Joi.string().allow(''),
      race: Joi.string().allow(''),
      pob: Joi.string().allow(''),
      marital_status: Joi.string().allow(''),
      work_status: Joi.number().integer().valid(...opts.employeeWorkStatusOpts[0]).messages({
        'any.only': '0: Đang làm, 1: Đã nghỉ',
      }),
      mn120: Joi.number(),
      timekeeping_code: Joi.string(),
      experience: Joi.string().allow(''),
      education: Joi.string().allow(''),
      avatar: Joi.string().allow(''),
      mv122: Joi.string().allow(''),
      department: Joi.number().integer(),
      bank_account: Joi.string().allow(''),
      bank_name: Joi.string().allow(''),
      updated_by: Joi.string(),
      deleted_at: Joi.allow(null),
      deleted_by: Joi.allow(null),
      leave_balance_days: Joi.number().allow(''),
      bank_branch: Joi.string().allow(''),
      tax_number: Joi.string().allow(''),
      social_number: Joi.string().allow(''),
      title: Joi.number().integer().valid(...opts.employeeTitleOpts[0]).messages({
        'any.only': '0: Không, 1: Cử nhân, 2: Thạc sỹ, 3: Tiến sỹ, 4: Tiến sỹ khoa học, 5: Chuyên khoa cấp 1, 6: Chuyên khoa cấp 2, 7: Giáo sư, 8: Phó giáo sư',
      }),
      major: Joi.string().allow(''),
      school: Joi.string().allow(''),
      academic_level: Joi.string().allow(''),
      dependent_number: Joi.number().integer(),
      is_social_insurance: Joi.number().integer().valid(...opts.isSocialInsuranceOpts[0]).messages({
        'any.only': '0: Có tham gia, 1: Không còn/muốn tham gia',
      }),
      identification_date: Joi.date(),
      identification_place: Joi.string().allow(''),
      contract_type: Joi.number().integer().valid(...opts.contractTypeOpts[0]).messages({
        'any.only': '0: Full-time, 1: Part-time, 2: Thỉnh giảng, 3: Thử việc',
      }),
      address_current: Joi.string().allow(''),
      is_special: Joi.number(),
      is_both_lecturer_employee: Joi.number().max(1).min(0),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/employee/employee/:id
  deleteEmployee: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  updateProfileEmployee: {
    body: Joi.object({
      last_name: Joi.string(),
      first_name: Joi.string(),
      dob: Joi.date(),
      sex: Joi.string().max(1),
      phone: Joi.string().max(128).allow(''),
      identification_number: Joi.string().allow(''),
      address: Joi.string().allow(''),
      nationality: Joi.string().allow(''),
      race: Joi.string().allow(''),
      pob: Joi.string().allow(''),
      marital_status: Joi.string().allow(''),
      experience: Joi.string().allow(''),
      education: Joi.string().allow(''),
      avatar: Joi.string().allow(''),
      bank_account: Joi.string().allow(''),
      bank_name: Joi.string().allow(''),
      address_current: Joi.string().allow(''),
    }),
  },

};
