// 2/12/2022
const { Op } = require('sequelize');
const _ = require('lodash');
const studentFields = require('./student.model').fields;
const attendanceFields = require('./attendance.model').fields;
const classFields = require('./class.model').fields;
const moment = require('moment');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');


const fields = {
  table: 'b350',
  _id: 'pb350',
  class: 'fb200',
  attendance: 'fb320',
  student: 'fn100',
  is_permission: 'bn353', // nghỉ có phép hay ko

  deleted_by: 'bl344',
  deleted_at: 'bl345',
  created_by: 'bl347',
  updated_by: 'bl349',
  updated_at: 'bl348',
  created_at: 'bl346',
};

const schema = (sequelize, DataTypes) => {
  const absentSchema = sequelize.define('Absent', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.is_permission]: {
      type: DataTypes.INTEGER,
      defaultValue: 2,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Absent = absentSchema;

  absentSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    studentModel,
    attendanceModel,
    attendance,
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await Absent.countItem({
      [fields.deleted_at]: null,
      [fields.attendance]: attendance,
    });
    const classes = await Absent.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.is_permission, 'is_permission'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.attendance]: attendance,
      },
      include: [
        {
          model: studentModel,
          as: 'student',
          attributes: [
            [studentFields._id, '_id'],
            [studentFields.first_name, 'first_name'],
            [studentFields.last_name, 'last_name'],
            [studentFields.email, 'email'],
            [studentFields.avatar, 'avatar'],
          ],
        },
        {
          model: attendanceModel,
          as: 'attendance',
          attributes: [
            [attendanceFields._id, '_id'],
            [attendanceFields.year, 'year'],
            [attendanceFields.semester, 'semester'],
            [attendanceFields.code, 'code'],
            [attendanceFields.date, 'date'],
          ],
        },
      ],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: classes };
  };

  absentSchema.checkAbsent = async ({
    page = 1,
    perPage = -1,
    order_by = fields._id,
    order_way = 'desc',
    attendance,
    student,
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const absents = await Absent.findOne({
      attributes: [
        [fields._id, '_id'],
        [fields.is_permission, 'is_permission'],
        [fields.student, 'student'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.attendance]: attendance,
        [fields.student]: student,
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return absents;
  };

  absentSchema.countAbsentEachStudent = async ({ student, _class }) => {
    const count = await Absent.count({
      where: {
        [fields.deleted_at]: null,
        [fields.class]: _class,
        [fields.student]: student,
      },
    });
    return count;
  };

  absentSchema.countItem = async (query) => {
    const count = await Absent.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  absentSchema.remove = async ({ id, email }) => {
    try {
      const result = await Absent.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  absentSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Absent.update({
        ...dbData,
        [fields.updated_at]: moment().format(),
      }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  absentSchema.patchMany = async ({ where, data }) => {
    try {
      const absents = await Absent.update(
        { ...data },
        { where: { ...where } },
      );
      return absents;
    } catch (error) {
      throw error;
    }
  };

  absentSchema.listAbsence = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    studentModel,
    attendanceModel,
    year = { [Op.not]: null },
    semester = { [Op.not]: null },
    student,
    lecturer = { [Op.not]: null },
    _class = { [Op.not]: null },
  }) => {
    const group_by = [];
    const count_by = [];
    const first_order_by = [];
    if (!student) {
      student = { [Op.not]: null };
      group_by.push('student.pn100');
      count_by.push([sequelize.fn('COUNT', fields.student), 'number_of_absence']);
      first_order_by.push([sequelize.fn('COUNT', fields.student), order_way]);
    }
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const where = {
      [fields.deleted_at]: null,
    };
    const count = (await Absent.findAll({
      where: {
        ...where,
      },
      include: [
        {
          model: studentModel,
          as: 'student',
          attributes: [
            [studentFields._id, '_id'],
            [studentFields.first_name, 'first_name'],
            [studentFields.last_name, 'last_name'],
            [studentFields.email, 'email'],
            [studentFields.avatar, 'avatar'],
          ],
          where: {
            [studentFields._id]: student,
          },
        },
        {
          model: attendanceModel,
          as: 'attendance',
          attributes: [
            [attendanceFields._id, '_id'],
            [attendanceFields.year, 'year'],
            [attendanceFields.semester, 'semester'],
            [attendanceFields.code, 'code'],
            [attendanceFields.date, 'date'],
            [attendanceFields.class, 'class'],
          ],
          where: {
            [attendanceFields.semester]: semester,
            [attendanceFields.year]: year,
            [attendanceFields.class]: _class,
            [attendanceFields.lecturer]: lecturer,
          },
        },
      ],
      // group: ['student.pn100'],
      group: [...group_by],
    })).length;

    const absent = await Absent.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.is_permission, 'is_permission'],
        // [sequelize.fn('COUNT', fields.student), 'number_of_absence'],
        ...count_by,
      ],
      where: {
        ...where,
      },
      include: [
        {
          model: studentModel,
          as: 'student',
          attributes: [
            [studentFields._id, '_id'],
            [studentFields.first_name, 'first_name'],
            [studentFields.last_name, 'last_name'],
            [studentFields.email, 'email'],
            [studentFields.avatar, 'avatar'],
          ],
          where: {
            [studentFields._id]: student,
          },
        },
        {
          model: attendanceModel,
          as: 'attendance',
          attributes: [
            [attendanceFields._id, '_id'],
            [attendanceFields.year, 'year'],
            [attendanceFields.semester, 'semester'],
            [attendanceFields.code, 'code'],
            [attendanceFields.date, 'date'],
            [attendanceFields.class, 'class'],
            [attendanceFields.content, 'content'],
            [attendanceFields.date, 'date'],
          ],
          where: {
            [attendanceFields.semester]: semester,
            [attendanceFields.year]: year,
            [attendanceFields.class]: _class,
            [attendanceFields.lecturer]: lecturer,
          },
        },
      ],

      group: [...group_by],
      ...pagination,
      order: [
        // [sequelize.fn('COUNT', fields.student), order_way],
        ...first_order_by,
        [order_by, order_way],
      ],
    });
    return { total: count, data: absent };
  };

  absentSchema.get = async ({ id, attendanceModel, classModel }) => {
    try {
      const absent = await Absent.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.is_permission, 'is_permission'],
        ],
        where: {
          [fields.deleted_at]: null,
          [fields._id]: id,
        },
        include: [
          {
            model: attendanceModel,
            as: 'attendance',
            attributes: [
              [attendanceFields._id, '_id'],
              [attendanceFields.created_by, 'created_by'],
            ],
            include: [
              {
                model: classModel,
                as: 'class',
                attributes: [
                  [classFields._id, '_id'],
                  [classFields.lecturer, 'lecturer'],
                ],
              },
            ],
          },
        ],
      });
      if (absent) {
        return absent;
      }

      throw new APIError({
        message: 'Absent does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  absentSchema.studentAbsence = async ({
    attendanceModel,
    studentModel,
    classModel,
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    student,
    year = { [Op.not]: null },
    semester = { [Op.not]: null },
    lecturer = { [Op.not]: null },
  }) => {
    try {
      page = parseInt(page, 10);
      perPage = parseInt(perPage, 10);
      let pagination = {};
      if (perPage > -1) {
        pagination = {
          offset: perPage * (page - 1),
          limit: perPage,
        };
      }
      const where = {
        [fields.deleted_at]: null,
      };

      const absent = await Absent.findAll({
        attributes: [
          [fields._id, '_id'],
          [sequelize.fn('COUNT', 'attendance.fb200'), 'number_of_absence'],
        ],
        where: {
          ...where,
        },
        include: [
          {
            model: studentModel,
            as: 'student',
            attributes: [
              [studentFields._id, '_id'],
              [studentFields.first_name, 'first_name'],
              [studentFields.last_name, 'last_name'],
              [studentFields.email, 'email'],
              [studentFields.phone, 'phone'],
              [studentFields.avatar, 'avatar'],
            ],
            where: {
              [studentFields._id]: student,
            },
          },
          {
            model: attendanceModel,
            as: 'attendance',
            attributes: [
              // [attendanceFields._id, '_id'],
              [attendanceFields.year, 'year'],
              [attendanceFields.semester, 'semester'],
              [attendanceFields.lecturer, 'lecturer'],
              // [attendanceFields.date, 'date'],
              // [attendanceFields.class, 'class'],
            ],
            where: {
              [attendanceFields.semester]: semester,
              [attendanceFields.year]: year,
              [attendanceFields.lecturer]: lecturer,
            },
            include: [
              {
                model: classModel,
                as: 'class',
                attributes: [
                  [classFields._id, '_id'],
                  [classFields.name, 'name'],
                  [classFields.name_vn, 'name_vn'],
                  [classFields.code, 'code'],
                ],
              },
            ],
          },
        ],

        group: ['attendance.fb200'],
        ...pagination,
        order: [
          // [sequelize.fn('COUNT', fields.student), order_way],
          // ...first_order_by,
          [order_by, order_way],
        ],
      });

      return { total: absent.length, absent };
    } catch (error) {
      throw error;
    }
  };
  return absentSchema;
};

module.exports = {
  schema,
  fields,
};
