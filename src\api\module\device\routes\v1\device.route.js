// 12/9/2022
// @dnine
/* eslint-disable max-len */
const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/device.controller');
const {
  authorize,
  ADMIN,
} = require('../../../../middlewares/auth');
const {
  listDevices,
  createDevice,
  updateDevice,
  faultyReportDevice,
} = require('../../validations/device.validation');

const router = express.Router();

router.param('id', controller.load);

router
  .route('/')
  .get(validate(listDevices), controller.list)
  .post(authorize(ADMIN), validate(createDevice), controller.create);

router
  .route('/tag/:tagId')
  .get(controller.getByTag);

router
  .route('/qr-generate/')
  .post(authorize(ADMIN), controller.qrGenerate);

router
  .route('/faulty-report/:id')
  .patch(authorize(ADMIN), validate(faultyReportDevice), controller.faultyReport);

router
  .route('/:id')
  .get(controller.get)
  .delete(authorize(ADMIN), controller.remove)
  .patch(authorize(ADMIN), validate(updateDevice), controller.update);


module.exports = router;
