
const { handler: error<PERSON><PERSON><PERSON> } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const RecruitmentRequest = require('../models/recruitment_request.model');

/**
 * Load recruitment_request and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const recruitment_request = await RecruitmentRequest.get(id);
    req.locals = { recruitment_request };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get recruitment_request
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.recruitment_request.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Create new recruitment_request
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user._id;
    const recruitment_request = new RecruitmentRequest(req.body);
    const saved = await recruitment_request.save();

    jsonSuccess(saved.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing recruitment_request
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const recruitment_request = Object.assign(req.locals.recruitment_request, req.body);

  recruitment_request.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get recruitment_request list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await RecruitmentRequest.count(req.query);
    const plans = await RecruitmentRequest.list(req.query);
    const transformed = plans.map(recruitment_request => recruitment_request.transform());
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete recruitment_request
 * @public
 */
exports.remove = (req, res, next) => {
  const { recruitment_request } = req.locals;

  recruitment_request.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};

