// 13/05/2021
// Phòng ban / Khoa (Department / School)
const _ = require('lodash');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const { Op } = require('sequelize');
const moment = require('moment');

const fields = {
  table: 'n450',
  _id: 'pn450',
  is_approved: 'nn451', // Trạng thái xét duyệt
  approved_at: 'nd451', // Ngày xét duyệt
  is_publish: 'nn451p', // Trạng thái hiển thị (giao diện sinh viên)
  published_at: 'nd451p', // Ng<PERSON>y công bố
  name: 'nv452', // Tên
  name_vn: 'nv452_vn', // Tên theo tiếng Việt
  code: 'nv452cod', // Mã hiệu
  type: 'nn454', // Loại Type (0: Department, 1: School)
  deleted_by: 'nl444', // <PERSON>ail ng<PERSON> x<PERSON>a
  deleted_at: 'nl445', // Thời gian xóa
  created_by: 'nl447', // Email người tạo
  updated_by: 'nl449', // Email người cập nhật
  updated_at: 'nl448',
  created_at: 'nl446',
};
const schema = (sequelize, DataTypes) => {
  const departmentSchema = sequelize.define('Department', {
    [fields._id]: {
      // ID
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.is_approved]: {
      // Trạng thái xét duyệt
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    [fields.approved_at]: {
      // Ngày xét duyệt
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.is_publish]: {
      // Trạng thái hiển thị (giao diện sinh viên)
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    [fields.published_at]: {
      // Ngày công bố
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.name]: {
      // Tên
      type: DataTypes.TEXT,
      allowNull: false,
    },
    [fields.name_vn]: {
      // Tên theo tiếng Việt
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.code]: {
      // Mã hiệu
      type: DataTypes.STRING(32),
      defaultValue: null,
      unique: true,
    },
    [fields.type]: {
      // Loại
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    nl444: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    nl445: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    nl447: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    nl449: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: 'n450',
    createdAt: 'nl446',
    updatedAt: 'nl448',
  });

  const Department = departmentSchema;

  departmentSchema.get = async (id) => {
    try {
      const department = await Department.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.is_approved, 'is_approved'],
          [fields.is_publish, 'is_publish'],
          [fields.name, 'name'],
          [fields.name_vn, 'name_vn'],
          [fields.code, 'code'],
          [fields.type, 'type'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
          [fields.is_approved]: 1,
        },
      });
      if (department) {
        return department;
      }
      throw new APIError({
        message: 'Department does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  departmentSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    type = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await Department.countItem({
      [fields.deleted_at]: null,
      [fields.is_approved]: 1,
      [fields.type]: type,
    });
    const list = await Department.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.is_approved, 'is_approved'],
        [fields.is_publish, 'is_publish'],
        [fields.name, 'name'],
        [fields.name_vn, 'name_vn'],
        [fields.code, 'code'],
        [fields.type, 'type'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.is_approved]: 1,
        [fields.type]: type,
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: list };
  };

  departmentSchema.countItem = async (query) => {
    const count = await Department.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  departmentSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Department.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  departmentSchema.remove = async ({ id, email }) => {
    try {
      const result = await Department.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  return departmentSchema;
};

module.exports = {
  schema,
  fields,
};
