const _ = require('lodash');
const moment = require('moment');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const employeeFields = require('../../employee/models/employee.model').fields;
const contractFields = require('../../contract/models/contract.model').fields;

const fields = {
  table: 'm350', // Thỏa thuận thu nhập
  _id: 'pm350',
  contract: 'fm300', // ID Hợp đồng
  employee: 'fm100', // ID Nhân viên / giảng viên
  position_represent: 'fn400e', // ID Chức vụ của bên đại diện tổ chức
  income_agreement_id: 'mv352cod', // Mã hợp đồng
  income_agreement_at: 'md353', // Ng<PERSON>y tạo thỏa thuận
  start_at: 'md354', // <PERSON><PERSON><PERSON> bắt đầu c<PERSON> hiệu lự<PERSON>
  income_primary: 'mn355', // Lương chính (dùng để tính thâm niên và BHXH)
  income_other: 'mn357', // Thu nhập khác
  allowance_responsibility: 'mn356', // Phụ cấp trách nhiệm
  allowance_gas: 'mn358', // Phụ cấp xăng xe
  allowance_concurrently: 'mn359', // Phụ cấp kiêm nhiệm
  allowance_seniority: 'mn361', // Phụ cấp thâm niên
  allowance_lunch: 'mn362', // Phụ cấp tiền cơm (Không tính vào tổng thu nhập để tính thuế)
  income_total: 'mn363', // Thu nhập thực tế (sau trừ thuế, BHXH,...)
  signed_at: 'mn360', // Ngày ký thỏa thuận

  deleted_by: 'ml344', // Email người xóa
  deleted_at: 'ml345', // Thời gian xóa
  created_at: 'ml346', // Thời gian tạo
  created_by: 'ml347', // Email người tạo
  updated_at: 'ml348', // Thời gian cập nhật
  updated_by: 'ml349', // Email người cập nhật
};

const schema = (sequelize, DataTypes) => {
  const incomeAgreementSchema = sequelize.define('IncomeAgreement', {
    [fields._id]: {
      // ID Hợp đồng
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.income_agreement_id]: {
      // Mã hợp đồng
      type: DataTypes.STRING(15),
      allowNull: false,
    },
    [fields.income_agreement_at]: {
      // Ngày tạo thỏa thuận
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    [fields.start_at]: {
      // Ngày bắt đầu có hiệu lực
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.income_primary]: {
      // Lương chính (dùng để tính thâm niên và BHXH)
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.income_other]: {
      // Thu nhập khác
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.allowance_responsibility]: {
      // Phụ cấp trách nhiệm
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.allowance_gas]: {
      // Phụ cấp xăng xe
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.allowance_concurrently]: {
      // Phụ cấp kiêm nhiệm
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.allowance_seniority]: {
      // Phụ cấp thâm niên
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.allowance_lunch]: {
      // Phụ cấp tiền cơm
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.income_total]: {
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.signed_at]: {
      // Ngày ký thỏa thuận
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const IncomeAgreement = incomeAgreementSchema;

  incomeAgreementSchema.get = async ({ id, employeeModel, contractModel }) => {
    try {
      const incomeAgreement = await IncomeAgreement.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.income_primary, 'income_primary'],
          [fields.income_other, 'income_other'],
          [fields.allowance_responsibility, 'allowance_responsibility'],
          [fields.allowance_gas, 'allowance_gas'],
          [fields.allowance_concurrently, 'allowance_concurrently'],
          [fields.allowance_seniority, 'allowance_seniority'],
          [fields.allowance_lunch, 'allowance_lunch'],
          [fields.income_total, 'income_total'],
          [fields.signed_at, 'signed_at'],

          [fields.deleted_by, 'deleted_by'],
          [fields.deleted_at, 'deleted_at'],
          [fields.created_at, 'created_at'],
          [fields.created_by, 'created_by'],
          [fields.updated_at, 'updated_at'],
          [fields.updated_by, 'updated_by'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
        include: [
          {
            model: employeeModel,
            as: 'employee',
            attributes: [
              [employeeFields._id, '_id'],
              [employeeFields.first_name, 'first_name'],
              [employeeFields.last_name, 'last_name'],
              [employeeFields.email, 'email'],

            ],
          },
          {
            model: contractModel,
            as: 'contract',
            attributes: [
              [contractFields._id, '_id'],
              [contractFields.contract_id, 'contract_id'],
              [contractFields.start_at, 'start_at'],
              [contractFields.end_at, 'end_at'],

            ],
          },
        ],
      });
      if (incomeAgreement) {
        return incomeAgreement;
      }
      throw new APIError({
        message: 'Employee does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  incomeAgreementSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    contractModel,
  }) => {
    try {
      page = parseInt(page, 10);
      perPage = parseInt(perPage, 10);
      let pagination = {};
      if (perPage > -1) {
        pagination = {
          offset: perPage * (page - 1),
          limit: perPage,
        };
      }
      const count = await IncomeAgreement.countItem({
        [fields.deleted_at]: null,
        // [fields.employee]: employee,
      });
      const incomeAgreement = await IncomeAgreement.findAll({
        attributes: [
          [fields._id, '_id'],
          [fields.income_primary, 'income_primary'],
          [fields.income_other, 'income_other'],
          [fields.allowance_responsibility, 'allowance_responsibility'],
          [fields.allowance_gas, 'allowance_gas'],
          [fields.allowance_concurrently, 'allowance_concurrently'],
          [fields.allowance_seniority, 'allowance_seniority'],
          [fields.allowance_lunch, 'allowance_lunch'],
          [fields.income_total, 'income_total'],
          [fields.signed_at, 'signed_at'],
          [fields.created_at, 'created_at'],
          [fields.created_by, 'created_by'],
        ],
        where: {
          [fields.deleted_at]: null,
        },
        include: [
          {
            model: employeeModel,
            as: 'employee',
            attributes: [
              [employeeFields._id, '_id'],
              [employeeFields.first_name, 'first_name'],
              [employeeFields.last_name, 'last_name'],
              [employeeFields.email, 'email'],
            ],
          },
          {
            model: contractModel,
            as: 'contract',
            attributes: [
              [contractFields._id, '_id'],
              [contractFields.contract_id, 'contract_id'],
              [contractFields.start_at, 'start_at'],
              [contractFields.end_at, 'end_at'],

            ],
          },
        ],
        ...pagination,
        order: [
          [order_by, order_way],
        ],
      });
      return { total: count, data: incomeAgreement };
    } catch (error) {
      throw error;
    }
  };

  incomeAgreementSchema.countItem = async ({ query }) => {
    const count = await IncomeAgreement.count({
      query: {
        ...query,
      },
    });
    return count;
  };

  incomeAgreementSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await IncomeAgreement.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  incomeAgreementSchema.remove = async ({ id, userId }) => {
    try {
      const result = await IncomeAgreement.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: userId,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  return incomeAgreementSchema;
};

module.exports = {
  schema,
  fields,
};
