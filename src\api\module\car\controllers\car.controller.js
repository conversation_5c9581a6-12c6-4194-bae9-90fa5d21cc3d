// 2/11/2021
const _ = require('lodash');
const db = require('../../../../config/mysql');
const { fields } = require('../models/car.model');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');

const Car = db.car;
const Employee = db.employee;
const CarSchedule = db.carSchedule;

/**
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const car = await Car.get({ id, employeeModel: Employee });
    req.locals = { car };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get car
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.car, req, res);
  } catch (error) {
    next(error);
  }
};

// Create and Save a new Car
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    data.created_by = req.user.email;
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });

    const car = Car.build({
      ...dbData,
    });
    const saved = await car.save();
    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    if (query.borrowed_at && query.returned_at) {
      let unavailableCar = [];
      const carList = [];
      const carSchedule = await CarSchedule.checkAvailable({
        borrowed_at: query.borrowed_at,
        returned_at: query.returned_at,
      });
      if (carSchedule.data.length > 0) {
        for (let i = 0; i < carSchedule.data.length; i += 1) {
          const item = carSchedule.data[i].dataValues;
          unavailableCar.push(item.car);
        }
      }
      unavailableCar = [...new Set(unavailableCar)];
      const cars = await Car.list({
        ...query,
        employeeModel: Employee,
      });
      for (let i = 0; i < cars.data.length; i += 1) {
        const item = cars.data[i].dataValues;
        if (unavailableCar.includes(item._id)) {
          carList.push({ ...item, is_available: false });
        } else {
          carList.push({ ...item, is_available: true });
        }
      }
      jsonSuccess({ total: cars.total, data: carList }, req, res);
    } else {
      const cars = await Car.list({
        ...query,
        employeeModel: Employee,
      });
      jsonSuccess(cars, req, res);
    }
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await Car.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await Car.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};
