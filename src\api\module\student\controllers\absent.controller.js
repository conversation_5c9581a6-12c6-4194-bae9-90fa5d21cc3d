/* eslint-disable no-await-in-loop */
// 2/12/2022
const _ = require('lodash');
const db = require('../../../../config/mysql');
const { fields } = require('../models/absent.model');
const classFields = require('../models/class.model').fields;
const enrollFields = require('../models/enroll.model').fields;
const { success: jsonSuccess } = require('../../../middlewares/success');
const AttendancePermission = require('../models/attendance.permission.model');
const httpStatus = require('http-status');
const moment = require('moment');
const APIError = require('../../../utils/APIError');
const { registrarEmails, secretaryEmails } = require('../../../../config/vars');

const Absent = db.absent;
const Class = db.class;
const Student = db.student;
const Attendance = db.attendance;
const Enroll = db.enroll;

// Create and Save a new Absent
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const { user } = req;
    const checkEnroll = await Enroll.countItem({
      [enrollFields.class]: data.class,
      [enrollFields.student]: data.student,

    });
    if (checkEnroll === 0) {
      throw new APIError({
        message: 'Student not found',
        status: httpStatus.NOT_FOUND,
      });
    }
    let checkPermission = 0;
    if (user.role === 0) {
      checkPermission = await AttendancePermission.count({
        _class: data.class,
        student: user.email,
      });
    } else {
      checkPermission = await Class.countItem({
        [classFields.lecturer]: user._id,
        [classFields._id]: data.class,
      });
    }
    if (checkPermission > 0) {
      const _class = await Class.get(data.class);
      data.code = _class.dataValues.code;
      data.lecturer = _class.dataValues.lecturer;
      data.year = _class.dataValues.year;
      data.semester = _class.dataValues.semester;
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });

      const absent = Absent.build({
        ...dbData,
      });
      const saved = await absent.save();
      jsonSuccess(saved, req, res);
    } else {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.BAD_REQUEST,
      });
    }
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    const absent = await Absent.list({
      ...query,
      studentModel: Student,
      attendanceModel: Attendance,
    });

    jsonSuccess(absent, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const data = req.body;
    const { user } = req;
    let is_permission = false;
    const absent = await Absent.get({
      id: req.params.id,
      attendanceModel: Attendance,
      classModel: Class,
    });
    const attendance = absent.dataValues.attendance;
    const lecturer = attendance.dataValues.class.dataValues.lecturer;
    const created_by = attendance.dataValues.created_by;
    if (user.email === created_by || user._id === lecturer) {
      is_permission = true;
    }
    if (is_permission) {
      data.updated_by = user.email;
      data.updated_at = moment().format();
      await Absent.patch({
        id: req.params.id,
        data: req.body,
      });
    } else {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.BAD_REQUEST,
      });
    }

    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const { user } = req;
    let is_permission = false;
    let result = {};
    const absent = await Absent.get({
      id: req.params.id,
      attendanceModel: Attendance,
      classModel: Class,
    });
    const attendance = absent.dataValues.attendance;
    const lecturer = attendance.dataValues.class.dataValues.lecturer;
    const created_by = attendance.dataValues.created_by;
    if (user.email === created_by || user._id === lecturer) {
      is_permission = true;
    }
    if (is_permission) {
      result = await Absent.remove({
        id: req.params.id,
        email: req.user.email,
      });
    } else {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.BAD_REQUEST,
      });
    }

    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.listAbsence = async (req, res, next) => {
  try {
    const { query, user } = req;
    if (!registrarEmails.includes(user.email) && !secretaryEmails.includes(user.email)) {
      query.lecturer = user._id;
    }
    const absent = await Absent.listAbsence({
      ...query,
      studentModel: Student,
      attendanceModel: Attendance,
    });

    jsonSuccess(absent, req, res);
  } catch (error) {
    next(error);
  }
};

exports.studentAbsence = async (req, res, next) => {
  try {
    const { query, user } = req;
    if (!registrarEmails.includes(user.email) && !secretaryEmails.includes(user.email)) {
      query.lecturer = user._id;
    }
    const absent = await Absent.studentAbsence({
      ...query,
      studentModel: Student,
      attendanceModel: Attendance,
      classModel: Class,
    });

    jsonSuccess(absent, req, res);
  } catch (error) {
    next(error);
  }
};

exports.optimizeData = async (req, res, next) => {
  try {
    const absents = await Absent.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.is_permission, 'is_permission'],
        [fields.student, 'student'],
        [fields.class, 'class'],
        [fields.attendance, 'attendance'],
      ],
      where: {
        [fields.deleted_at]: null,
      },
    });
    let yes = 0;
    let no = 0;
    for (let i = 0; i < absents.length; i += 1) {
      const item = absents[i].dataValues;
      const attendance = await Attendance.findAll({
        attributes: [
          ['pb320', '_id'],
          ['bl345', 'deleted_at'],
        ],
        where: {
          pb320: item.attendance,
          bl345: null,
        },
      });
      if (attendance.length > 0) {
        yes += 1;
      } else {
        no += 1;
        const result = await Absent.update({
          bl345: moment().format(),
          bl344: req.user.email,
        }, {
          where: {
            pb350: item._id,
          },
        });
        console.log('><<<<<<<<<', result);
      }
    }
    jsonSuccess({
      total: absents.length, absents, yes, no,
    }, req, res);
  } catch (error) {
    next(error);
  }
};
