// 1/12/2021
const Joi = require('joi');
const { opts } = require('../models/notification.model');

module.exports = {

  // GET /v1/notification/notification
  listNotifications: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
      type: Joi.number().integer().valid(...opts.typeOpts[0]),
      status: Joi.number().valid(...opts.statusOpts[0]),
    }),
  },

  // POST /v1/notification/notification
  createNotification: {
    body: Joi.object({
      employee: Joi.number().integer().required(),
      content: Joi.string().required(),
      type: Joi.number().integer().valid(...opts.typeOpts[0]).required(),
    }),
  },

  // PATCH /v1/notification/notification/:id
  updateNotification: {
    body: Joi.object({
      driver: Joi.number().integer(),
      name: Joi.string(),
      license_plate: Joi.string(),
      seat: Joi.number().integer(),
      code: Joi.string(),
      description: Joi.string(),
      status: Joi.string().valid(...opts.statusOpts[0]),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/notification/notification/:id
  deleteNotification: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
