// 8/11/2021
const APIError = require('../../../utils/APIError');
const { Op } = require('sequelize');
const httpStatus = require('http-status');
const moment = require('moment');
const _ = require('lodash');
const employeeFields = (require('../../employee/models/employee.model')).fields;
const assignmentFields = (require('../../assignment/models/assignment.model')).fields;

const isApproveOpts = [[0, 1]]; // Approval status (0: Disapprove, 1: Approve)
const isDisplayOpts = [[0, 1]];// Publication status (1: Public)
const statusOpts = [[0, 1, 2, 3, 4]];
// 0: chưa nhận, 1: đang làm, 2: hoàn thành, 3: đã hủy, 4: quá cmn hạn
const fields = {
  table: 't650',
  _id: 'pt650',
  assignment: 'ft600', // ID Công việc
  employee_from: 'fm100', // ID Nhân viên / Giảng viên gửi
  employee_to: 'fm100r', // ID Nhân viên / Giảng viên nhận
  is_approve: 'tn651', // Trạng thái xét duyệt
  approved_at: 'td651', // Ngày xét duyệt
  is_display: 'tn651p', // Trạng thái hiển thị
  public_at: 'td651p', // Ngày công bố
  title: 'tv652', // Công việc

  status: 'tn654', // Kết quả
  start_date: 'td657', // Thời gian bắt đầu
  end_date: 'td657r', // Thời gian kết thúc dự kiện
  start_time: 'tv658', // Giờ bắt đầu
  end_time: 'tv658r', // Giờ kết thúc dự kiến
  description: 'tl638', // Mô tả
  note: 'tl639', // Ghi chú
  attached: 'tl642', // file đính kèm
  progress: 'tn653', // % hoàn thành
  // order: 'tl640', // Thứ tự ưu tiên

  deleted_by: 'tl644', // Email người xóa
  deleted_at: 'tl645', // Thời gian xóa
  created_by: 'tl647', // Email người tạo
  updated_by: 'tl649', // Email người cập nhật
  updated_at: 'tl648',
  created_at: 'tl646',
};

const schema = (sequelize, DataTypes) => {
  const assignmentContentSchema = sequelize.define('AssignmentContent', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.is_approve]: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      validate: {
        isIn: isApproveOpts,
      },
    },
    [fields.approved_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.is_display]: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      validate: {
        isIn: isDisplayOpts,
      },
    },
    [fields.public_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.title]: {
      type: DataTypes.STRING(150),
      defaultValue: null,
    },
    [fields.status]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: statusOpts,
      },
    },
    [fields.start_date]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.end_date]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.start_time]: {
      type: DataTypes.STRING(10),
      defaultValue: null,
    },
    [fields.end_time]: {
      type: DataTypes.STRING(10),
      defaultValue: null,
    },
    [fields.description]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.note]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.attached]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.progress]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const AssignmentContent = assignmentContentSchema;

  assignmentContentSchema.get = async ({
    id,
    employeeModel,
    assignmentModel,
    employee = { [Op.not]: null },
  }) => {
    try {
      const assignment = await AssignmentContent.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.is_approve, 'is_approve'],
          [fields.title, 'title'],
          [fields.status, 'status'],
          [fields.start_date, 'start_date'],
          [fields.end_date, 'end_date'],
          [fields.start_time, 'start_time'],
          [fields.end_time, 'end_time'],
          [fields.description, 'description'],
          [fields.note, 'note'],
          [fields.attached, 'attached'],
          [fields.progress, 'progress'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
          [`$employee_from.${employeeFields._id}$`]: employee,
          [`$employee_to.${employeeFields._id}$`]: employee,

        },
        include: [
          {
            model: employeeModel,
            as: 'employee_from',
            attributes: [
              [employeeFields._id, '_id'],
              [employeeFields.first_name, 'first_name'],
              [employeeFields.last_name, 'last_name'],
              [employeeFields.email, 'email'],
            ],
          },
          {
            model: employeeModel,
            as: 'employee_to',
            attributes: [
              [employeeFields._id, '_id'],
              [employeeFields.first_name, 'first_name'],
              [employeeFields.last_name, 'last_name'],
              [employeeFields.email, 'email'],
            ],
          },
          {
            model: assignmentModel,
            as: 'assignment',
            attributes: [
              [assignmentFields._id, '_id'],
              [assignmentFields.title, 'title'],
            ],
          },
        ],
      });
      if (assignment) {
        return assignment;
      }
      throw new APIError({
        message: 'AssignmentContent does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  assignmentContentSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    assignmentModel,
    assignment = { [Op.not]: null },
    employee = { [Op.not]: null },
    status = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await AssignmentContent.countItem({
      query: {
        [fields.deleted_at]: null,
        [fields.assignment]: assignment,
        [fields.status]: status,
        [Op.or]: [
          { [`$employee_from.${employeeFields._id}$`]: employee }, //  $employee_from.pm100$
          { [`$employee_to.${employeeFields._id}$`]: employee }, // $employee_to.pm100$
        ],
      },
      employeeModel,
    });
    const assignments = await AssignmentContent.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.is_approve, 'is_approve'],
        [fields.title, 'title'],
        [fields.status, 'status'],
        [fields.start_date, 'start_date'],
        [fields.end_date, 'end_date'],
        [fields.start_time, 'start_time'],
        [fields.end_time, 'end_time'],
        [fields.description, 'description'],
        [fields.note, 'note'],
        [fields.attached, 'attached'],
        [fields.progress, 'progress'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.assignment]: assignment,
        [fields.status]: status,
        [Op.or]: [
          { [`$employee_from.${employeeFields._id}$`]: employee },
          { [`$employee_to.${employeeFields._id}$`]: employee },
        ],
      },
      include: [
        {
          model: employeeModel,
          as: 'employee_from',
          attributes: [
            [employeeFields._id, '_id'],
            [employeeFields.first_name, 'first_name'],
            [employeeFields.last_name, 'last_name'],
            [employeeFields.email, 'email'],
          ],
        },
        {
          model: employeeModel,
          as: 'employee_to',
          attributes: [
            [employeeFields._id, '_id'],
            [employeeFields.first_name, 'first_name'],
            [employeeFields.last_name, 'last_name'],
            [employeeFields.email, 'email'],
          ],
        },
        {
          model: assignmentModel,
          as: 'assignment',
          attributes: [
            [assignmentFields._id, '_id'],
            [assignmentFields.title, 'title'],
          ],
        },
      ],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: assignments };
  };

  assignmentContentSchema.listByEmployeeTo = async ({
    page = 1,
    perPage = -1,
    order_by = fields._id,
    order_way = 'desc',
    employee,
    assignment,
  }) => {
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }

    const assignmentContents = await AssignmentContent.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.is_approve, 'is_approve'],
        [fields.title, 'title'],
        [fields.status, 'status'],
        [fields.start_date, 'start_date'],
        [fields.end_date, 'end_date'],
        [fields.start_time, 'start_time'],
        [fields.end_time, 'end_time'],
        [fields.description, 'description'],
        [fields.note, 'note'],
        [fields.attached, 'attached'],
        [fields.progress, 'progress'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.assignment]: assignment,
        [fields.employee_to]: employee,
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return assignmentContents;
  };

  assignmentContentSchema.countItem = async ({ query, employeeModel }) => {
    const count = await AssignmentContent.count({
      where: {
        ...query,
      },
      include: [
        {
          model: employeeModel,
          as: 'employee_from',
        },
        {
          model: employeeModel,
          as: 'employee_to',
        },
      ],
    });
    return count;
  };

  assignmentContentSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await AssignmentContent.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  assignmentContentSchema.remove = async ({ id, email }) => {
    try {
      const result = await AssignmentContent.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  return assignmentContentSchema;
};

module.exports = {
  schema,
  fields,
  opts: {
    isApproveOpts,
    isDisplayOpts,
    statusOpts,
  },
};
