// const mongoose = require('mongoose');
// const httpStatus = require('http-status');
// const { omitBy, isNil } = require('lodash');
// const APIError = require('../../../utils/APIError');

// /**
//  * jobDescriptionItem Schema
//  * @private
//  */
// const jobDescriptionItemSchema = new mongoose.Schema({
//   job_description: {
//     type: String,
//     trim: true,
//   },
//   job_output: {
//     type: String,
//     trim: true,
//   },
//   is_active: {
//     type: Boolean,
//     default: true,
//   },
//   created_by: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'User',
//   },
//   updated_by: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'User',
//   },
// }, {
//   timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
// });

// /**
//  * Methods
//  */
// jobDescriptionItemSchema.method({
//   transform() {
//     const transformed = {};
//     const fields = [
//       '_id',
//       'job_description',
//       'job_output',
//       'is_active',
//       'created_at',
//     ];
//     fields.forEach((field) => {
//       transformed[field] = this[field];
//     });

//     return transformed;
//   },
// });

// /**
//  * Statics
//  */
// jobDescriptionItemSchema.statics = {
//   /**
//    * Get jobDescriptionItem
//    *
//    * @param {ObjectId} id - The objectId of jobDescriptionItem.
//    * @returns {Promise<JobDescriptionItem, APIError>}
//    */
//   async get(id) {
//     try {
//       let jobDescriptionItem;

//       if (mongoose.Types.ObjectId.isValid(id)) {
//         jobDescriptionItem = await this.findById(id).exec();
//       }
//       if (jobDescriptionItem) {
//         return jobDescriptionItem;
//       }

//       throw new APIError({
//         message: 'JobDescriptionItem does not exist',
//         status: httpStatus.NOT_FOUND,
//       });
//     } catch (error) {
//       throw error;
//     }
//   },

//   /**
//    * List jobDescriptionItem in descending order of 'createdAt' timestamp.
//    *
//    * @param {number} skip - Number of jobDescriptionItem to be skipped.
//    * @param {number} limit - Limit number of jobDescriptionItem to be returned.
//    * @returns {Promise<User[]>}
//    */
//   list({
//     page = 1,
//     perPage = 30,
//     sort,
//   }) {
//     page = parseInt(page, 10);
//     perPage = parseInt(perPage, 10);
//     const options = omitBy({
//     }, isNil);
//     const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
//     const result = this.find(options)
//       .populate('department', '_id name')
//       .sort(sortOpts);
//     if (perPage > -1) {
//       result.skip(perPage * (page - 1)).limit(perPage);
//     }
//     return result.exec();
//   },

//   /**
//    * Count jobDescriptionItem.
//    * @returns {Promise<Number>}
//    */
//   async count({
//     year,
//   }) {
//     const options = omitBy({
//       year,
//     }, isNil);
//     return this.find(options).count();
//   },
// };

// /**
//  * @typedef JobDescriptionItem
//  */
// module.exports = mongoose.model('JobDescriptionItem', jobDescriptionItemSchema);
