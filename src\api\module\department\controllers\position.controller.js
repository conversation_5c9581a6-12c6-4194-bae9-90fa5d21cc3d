
const _ = require('lodash');
const { handler: error<PERSON><PERSON><PERSON> } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const db = require('../../../../config/mysql');
const { fields } = require('../models/position.model');

const Position = db.position;

/**
 * Load position and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const position = await Position.get(id);
    req.locals = { position };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get position
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    let { position } = req.locals;
    position = position.dataValues;
    jsonSuccess(position, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Get position list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const positions = await Position.list(req.query);
    jsonSuccess(positions, req, res);
  } catch (error) {
    next(error);
  }
};

exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });
    const position = Position.build({
      ...dbData,
    });
    const saved = await position.save();
    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await Position.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await Position.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};
