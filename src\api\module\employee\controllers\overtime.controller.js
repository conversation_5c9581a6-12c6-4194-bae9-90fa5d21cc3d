const _ = require('lodash');
const db = require('../../../../config/mysql');
const { fields } = require('../models/overtime.model');
const { success: jsonSuccess } = require('../../../middlewares/success');

const Overtime = db.overtime;
const Employee = db.employee;

// Create and Save a new Overtime
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });

    const overtime = Overtime.build({
      ...dbData,
    });
    const savedLeave = await overtime.save();
    jsonSuccess(savedLeave, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    const overtime = await Overtime.list({ ...query, employeeModel: Employee });

    jsonSuccess(overtime, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await Overtime.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};
