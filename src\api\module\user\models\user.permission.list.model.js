const fields = {
  table: 'q200', // DS quyền cá nhân
  _id: 'pq200', // ID Nhóm quyền của từng nhân viên / giảng viên
  employee: 'fm100', // ID sinh viên / nhân viên / giảng viên
  permission_group: 'fq350', // ID Nhóm quyền

  deleted_at: 'ql145', // Thời gian xóa
  created_at: 'ql146', // Thời gian tạo
  created_by: 'ql147', // <PERSON>ail người tạo
  updated_at: 'ql148', // Thời gian cập nhật
  updated_by: 'ql149', // Email người cập nhật
};

const schema = (sequelize, DataTypes) => {
  const userPermissionListSchema = sequelize.define('UserPermissionList', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });
  return userPermissionListSchema;
};

module.exports = {
  schema,
  fields,
};
