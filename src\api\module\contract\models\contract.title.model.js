const fields = {
  table: 'm310', // ID
  contract: 'fm300', // ID Hợp đồng
  department: 'fn450', // ID Khoa / phòng ban
  position: 'fn400', // ID Chức vụ

  deleted_by: 'ml344', // Email người xóa
  deleted_at: 'ml345', // Thời gian xóa
  created_at: 'ml346', // Thời gian tạo
  created_by: 'ml347', // Email người tạo
  updated_at: 'ml348', // Thời gian cập nhật
  updated_by: 'ml349', // Email người cập nhật
};
const schema = (sequelize, DataTypes) => {
  const contractTitleSchema = sequelize.define('ContractTitle', {
    [fields._id]: {
      // ID Chi tiết
      type: DataTypes.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.contract]: {
      // ID Hợp đồng
      type: DataTypes.INTEGER(11),
      allowNull: false,
    },
    [fields.department]: {
      // ID Khoa / phòng ban (bảng N450)
      type: DataTypes.INTEGER(11),
      allowNull: false,
    },
    [fields.position]: {
      // ID Chức vụ (bảng N400)
      type: DataTypes.INTEGER(11),
      allowNull: false,
    },
    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  return contractTitleSchema;
};

module.exports = {
  schema,
  fields,
};
