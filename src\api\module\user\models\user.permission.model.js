// danh sách quyền cá nhân, chủ yếu là quyền duyệt nghỉ phép
const { Op } = require('sequelize');
const _ = require('lodash');
const moment = require('moment');
const employeeFields = (require('../../employee/models/employee.model')).fields;

// const FUNCTION_CODE = 'FUNC_LEAVE_ALLOW_';
const FUNCTION_CODE = 'FUNC_DEPARTMENT_HEAD_';
const fields = {
  table: 'q175', // DS quyền cá nhân
  _id: 'pq175', // ID Quyền cá nhân
  employee: 'fm100', // ID sinh viên / nhân viên / giảng viên
  permission_interface: 'fq300', // ID Giao diện quyền
  permission_group: 'fq350', // ID Nhóm quyền
  permission_ui_function: 'fq400', // ID Chức năng giao diện quyền
  is_super_admin: 'qn176', // "<PERSON><PERSON>h dấu super admin(0: Không phải SA, 1: Là SA)"
  name: 'qv177', // Tên nhóm quyền
  permission_ui_function_code: 'qv178', // Tên code chức năng giao diện quyền
  permission_ui_function_name: 'qv179', // Tên chức năng giao diện quyền

  deleted_at: 'ql145', // Thời gian xóa
  created_at: 'ql146', // Thời gian tạo
  created_by: 'ql147', // Email người tạo
  updated_at: 'ql148', // Thời gian cập nhật
  updated_by: 'ql149', // Email người cập nhật
  deleted_by: 'ql150', // Email người xóa
};
const isSuperAdminOpts = [[0, 1]];

const schema = (sequelize, DataTypes) => {
  const userPermissionSchema = sequelize.define('UserUserPermission', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.is_super_admin]: {
      type: DataTypes.INTEGER,
      defaultValue: null,
      validate: {
        isIn: isSuperAdminOpts,
      },
    },
    [fields.name]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.permission_ui_function_code]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.permission_ui_function_name]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const UserPermission = userPermissionSchema;

  userPermissionSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    employee = { [Op.not]: null },
    permission_ui_function_code = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }

    const count = await UserPermission.countItem({
      [fields.deleted_at]: null,
      [fields.employee]: employee,
      [fields.permission_ui_function_code]: permission_ui_function_code,
    });
    const userPermission = await UserPermission.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.permission_ui_function_code, 'permission_ui_function_code'],
        [fields.permission_ui_function_name, 'permission_ui_function_name'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.employee]: employee,
        [fields.permission_ui_function_code]: permission_ui_function_code,

      },
      include: {
        model: employeeModel,
        as: 'employee',
        attributes: [
          [employeeFields._id, '_id'],
          [employeeFields.first_name, 'first_name'],
          [employeeFields.last_name, 'last_name'],
          [employeeFields.email, 'email'],
        ],
      },
      ...pagination,
      order: [[order_by, order_way]],
    });
    return { total: count, data: userPermission };
  };

  userPermissionSchema.countItem = async (query) => {
    const count = await UserPermission.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  userPermissionSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await UserPermission.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  userPermissionSchema.remove = async ({ id, employee }) => {
    try {
      await UserPermission.update({
        [fields.deleted_by]: employee,
        [fields.deleted_at]: moment().format(),
      }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  userPermissionSchema.checkDepartmentHead = async (employee) => {
    try {
      const permission = await UserPermission.findAll({
        attributes: [
          [fields._id, '_id'],
          [fields.permission_ui_function_code, 'permission_ui_function_code'],
          [fields.permission_ui_function_name, 'permission_ui_function_name'],
          [fields.employee, 'employee'],
        ],
        where: {
          [fields.employee]: employee,
          [fields.deleted_at]: null,
          [fields.permission_ui_function_code]: { [Op.startsWith]: FUNCTION_CODE },
        },

      });
      return permission;
    } catch (error) {
      throw error;
    }
  };

  userPermissionSchema.listDepartmentHead = async (department) => {
    const headers = await UserPermission.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.permission_ui_function_code, 'permission_ui_function_code'],
        [fields.permission_ui_function_name, 'permission_ui_function_name'],
        [fields.employee, 'employee'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.permission_ui_function_code]: { [Op.startsWith]: FUNCTION_CODE },
        [fields.permission_ui_function_code]: { [Op.endsWith]: department },
      },
    });

    return headers;
  };

  return userPermissionSchema;
};

module.exports = {
  schema,
  fields,
};
