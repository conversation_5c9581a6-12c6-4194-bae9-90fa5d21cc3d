// 25/11/2022
const _ = require('lodash');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

const fields = {
  table: 'n100',
  _id: 'pn100', // ID
  student_id: 'nv101', // Student ID = 2 số cuối <PERSON> học + 2 số <PERSON> khoa + 3 số cuối mã số hồ sơ
  student_type: 'nn101', // Type (0: TTU Student, 1: Other)
  last_name: 'nv102', // Last name (+ Middle name)
  first_name: 'nv103', // First name
  dob: 'nd104', // Date of birth
  sex: 'nv105', // Sex (M: Male, F: Female)
  email: 'nv106', // Email (TTU)
  phone: 'nv107', // Phone number,
  address: 'nv108', // Permanent address
  status: 'nn110', // Learning status (0: Studying, 1: Dropped out, 2: Graduated, 3: Transferred)
  avatar: 'nv111', // Avatar URL address
  identity_number: 'nv115', // Số chứng minh nhân dân
  year: 'fh050',
  department: 'fn450',
  major: 'fn500',

  deleted_by: 'nl144',
  deleted_at: 'nl145',
  created_by: 'nl147',
  updated_by: 'nl149',
  updated_at: 'nl148',
  created_at: 'nl146',
};
const typeOpts = [[0, 1, 2]];

const schema = (sequelize, DataTypes) => {
  const studentSchema = sequelize.define('Student', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.student_id]: {
      type: DataTypes.STRING(32),
      allowNull: false,
    },
    [fields.student_type]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.last_name]: {
      type: DataTypes.STRING(280),
      allowNull: false,
    },
    [fields.first_name]: {
      type: DataTypes.STRING(128),
      allowNull: false,
    },
    [fields.dob]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.sex]: {
      type: DataTypes.STRING(1),
      defaultValue: null,
    },
    [fields.email]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.phone]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.address]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.status]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.avatar]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.identity_number]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Student = studentSchema;

  studentSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await Student.countItem({
      [fields.deleted_at]: null,
    });
    const students = await Student.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.student_id, 'student_id'],
        [fields.status, 'status'],
        [fields.last_name, 'last_name'],
        [fields.first_name, 'first_name'],
        [fields.dob, 'dob'],
        [fields.sex, 'sex'],
        [fields.email, 'email'],
        [fields.phone, 'phone'],
        [fields.avatar, 'avatar'],
        [fields.year, 'year'],
        [fields.department, 'department'],
        [fields.major, 'major'],
      ],
      where: {
        [fields.deleted_at]: null,
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: students };
  };

  studentSchema.countItem = async (query) => {
    const count = await Student.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  studentSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Student.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  studentSchema.getByEmail = async (email) => {
    try {
      const student = Student.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.first_name, 'first_name'],
          [fields.last_name, 'last_name'],
          [fields.email, 'email'],
        ],
        where: {
          [fields.email]: email,
          [fields.deleted_at]: null,
        },
      });
      if (student) {
        return student;
      }
      throw new APIError({
        message: 'Employee does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  studentSchema.findById = async (id) => {
    const student = await Student.findOne({
      attributes: [
        [fields._id, '_id'],
        [fields.first_name, 'first_name'],
        [fields.last_name, 'last_name'],
        [fields.email, 'email'],
        [fields.department, 'department'],
        [fields.phone, 'phone'],
        [fields.avatar, 'avatar'],
        [fields.year, 'year'],
        [fields.sex, 'sex'],
        [fields.dob, 'dob'],
        [fields.address, 'address'],
      ],
      where: {
        [fields._id]: id,
      },
    });
    return student;
  };

  return studentSchema;
};

module.exports = {
  schema,
  fields,
  typeOpts,
  opts: {
    typeOpts,
  },
};
