// 4/11/2021
const _ = require('lodash');
const db = require('../../../../config/mysql');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { fields } = require('../models/car.schedule.model');

const Car = db.car;
const Employee = db.employee;
const CarSchedule = db.carSchedule;

/**
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const carSchedule = await CarSchedule.get({
      id,
      employeeModel: Employee,
      carModel: Car,
    });
    req.locals = { carSchedule };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    data.created_by = req.user.email;
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });

    const carSchedule = CarSchedule.build({
      ...dbData,
    });
    const saved = await carSchedule.save();
    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    const carSchedules = await CarSchedule.list({
      ...query,
      employeeModel: Employee,
      carModel: Car,
    });

    jsonSuccess(carSchedules, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await CarSchedule.patch({
      id: req.params.id,
      data: {
        ...req.body,
        updated_by: req.user.email,
      },
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await CarSchedule.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};
