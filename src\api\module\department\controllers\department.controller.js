
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const db = require('../../../../config/mysql');
const { fields } = require('../models/department.model');
const _ = require('lodash');

const Department = db.department;
const Employee = db.employee;

/**
 * Load department and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const department = await Department.get(id);
    req.locals = { department };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get department
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    let { department } = req.locals;
    department = department.dataValues;
    const employees = await Employee.list({
      perPage: -1,
      dept: department._id,
      departmentModel: Department,
      work_status: 0,
    });
    jsonSuccess({ department: req.locals.department, employees }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Get department list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const departments = await Department.list(req.query);
    jsonSuccess(departments, req, res);
    // const departments = await Department.get(5);
    // jsonSuccess({ docs: departments }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });
    const department = Department.build({
      ...dbData,
    });
    const saved = await department.save();
    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await Department.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await Department.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};
