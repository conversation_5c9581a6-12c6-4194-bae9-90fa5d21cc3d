// 22/9/2023
const APIError = require('../../../utils/APIError');
const httpStatus = require('http-status');
const Calendar = require('../models/calendar.model');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { registrarEmails } = require('../../../../config/vars');


exports.load = async (req, res, next, id) => {
  try {
    const calendar = await Calendar.get(id);
    req.locals = { calendar };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.get = (req, res) => {
  jsonSuccess(req.locals.calendar.transform(), req, res);
};

exports.create = async (req, res, next) => {
  try {
    const { user } = req;
    if (!registrarEmails.includes(user.email)) {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.BAD_REQUEST,
      });
    }
    req.body.created_by = user.email;
    const calendar = new Calendar(req.body);
    const saved = await calendar.save();
    jsonSuccess(saved.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = (req, res, next) => {
  const { user } = req;
  if (!registrarEmails.includes(user.email)) {
    throw new APIError({
      message: 'Don\'t have permission',
      status: httpStatus.BAD_REQUEST,
    });
  }
  req.body.updated_by = user.email;
  const calendar = Object.assign(req.locals.calendar, req.body);

  calendar.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

exports.list = async (req, res, next) => {
  try {
    const types = await Calendar.list(req.query);
    const transformed = types.map(type => type.transform());

    jsonSuccess({ docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const { user } = req;
    if (!registrarEmails.includes(user.email)) {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.BAD_REQUEST,
      });
    }
    const { calendar } = req.locals;
    await calendar.remove();
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.now = async (req, res, next) => {
  try {
    const now = await Calendar.now(Date.now());
    jsonSuccess(now.transform(), req, res);
  } catch (error) {
    next(error);
  }
};
