const { omit } = require('lodash');
const httpStatus = require('http-status');
const multer = require('multer');
const path = require('path');
const fse = require('fs-extra');
const { uploadDir } = require('../../../../config/vars');
// const ObjectID = require('mongodb').ObjectID;

const User = require('../models/user.model');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const APIError = require('../../../utils/APIError');
/**
 * Load user and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const user = await User.get(id);
    req.locals = { user };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get user
 * @public
 */
exports.get = async (req, res) => {
  jsonSuccess(req.locals.user.transform(), req, res);
};

/**
 * Get logged in user info
 * @public
 */
exports.loggedIn = (req, res) => res.json(req.user.transform());

/**
 * Create new user
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    if (req.user.role !== 'admin') {
      if (req.body.role === 'admin') {
        throw new APIError({
          message: 'User does not create admin permissions ',
          status: httpStatus.NOT_FOUND,
        });
      }
    }

    const user = new User(req.body);
    const savedUser = await user.save();

    jsonSuccess(savedUser.transform(), req, res);
  } catch (error) {
    next(User.checkDuplicateEmail(error));
  }
};

/**
 * Replace existing user
 * @public
 */
exports.replace = async (req, res, next) => {
  try {
    const { user } = req.locals;
    const newUser = new User(req.body);
    const ommitRole = user.role !== 'admin' ? 'role' : '';
    const newUserObject = omit(newUser.toObject(), '_id', ommitRole);

    await user.update(newUserObject, { override: true, upsert: true });
    const savedUser = await User.findById(user._id);

    jsonSuccess(savedUser.transform(), req, res);
  } catch (error) {
    next(User.checkDuplicateEmail(error));
  }
};

/**
 * Update existing user
 * @public
 */
exports.update = (req, res, next) => {
  const ommitRole = req.locals.user.role !== 'admin' ? 'role' : '';
  const updatedUser = omit(req.body, ommitRole);
  const user = Object.assign(req.locals.user, updatedUser);
  if (req.user.role !== 'admin') {
    if (req.body.role === 'admin') {
      throw new APIError({
        message: 'User does not update admin permissions ',
        status: httpStatus.NOT_FOUND,
      });
    }
  }
  // const user = Object.assign(req.locals.user, req.body);
  user.save()
    .then(savedUser => jsonSuccess(savedUser.transform(), req, res))
    .catch(e => next(User.checkDuplicateEmail(e)));
};

/**
 * Get user list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const users = await User.list(req.query);
    const count = await User.count(req.query);
    const transformedUsers = users.map(user => user.transform());

    jsonSuccess({ total: count, docs: transformedUsers }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete user
 * @public
 */
exports.remove = (req, res, next) => {
  const { user } = req.locals;

  user.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};

exports.uploadAvatar = async (req, res, next) => {
  // const userID = req.user._id;

  // const userObjectID = new ObjectID(userID);
  const filepath = path.resolve(`${uploadDir.userAvatar}`);
  fse.ensureDirSync(filepath);

  const func = multer.diskStorage({
    // multers disk storage settings
    destination: (rq, file, cb) => {
      cb(null, uploadDir.userAvatar);
    },
    filename: (rq, file, cb) => {
      const datetimestamp = Date.now();
      cb(null, `${file.fieldname}-${datetimestamp}.${file.originalname.split('.')[file.originalname.split('.').length - 1]}`);
    },
  });

  const upload = multer({
    storage: func,
  }).single('file');

  upload(req, res, async (err) => {
    if (err) {
      return res.json({ error_code: 1, err_desc: err });
    }
    if (req.file) {
      await User.updateOne({
        // _id: userObjectID,
      }, {
        $set: { avatar: `product/${req.file.filename}` },
      });
    }
    req.file.filename = `product/${req.file.filename}`;
    return res.send(jsonSuccess(req.file, req, res));
  });
  return [];
};
