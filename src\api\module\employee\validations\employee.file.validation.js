// 28/11/2023
const Jo<PERSON> = require('joi');

module.exports = {

  // GET /v1/employee/file
  listEmployeeFiles: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
      employee: Joi.number().integer(),
    }),
  },

  // POST /v1/employee/file
  createEmployeeFile: Joi.object({
    employee: Joi.number().integer().required(),
    name: Joi.string().required(),
  }),

  // PATCH /v1/employee/file/:id
  updateEmployeeFile: {
    body: Joi.object({
      name: Joi.string(),
      directory: Joi.string(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/employee/file/:id
  deleteEmployeeFile: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
