// 20/11/2022
const mongoose = require('mongoose');
const { omitBy, isNil } = require('lodash');
const APIError = require('../../../utils/APIError');
const httpStatus = require('http-status');

const attendancePermission = new mongoose.Schema({
  class: {
    type: Number,
    require: true,
  },
  student: {
    type: String,
    require: true,
  },
  lecturer: {
    type: String,
    require: true,
  },
  created_by: {
    type: String,
    default: '',
  },
  updated_by: {
    type: String,
    default: '',
  },
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
});

attendancePermission.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'class',
      'student',
      'lecturer',
      'created_by',
      'created_at',
    ];

    fields.forEach((field) => {
      transformed[field] = this[field];
    });

    return transformed;
  },
});

attendancePermission.statics = {
  /**
   * Get ArticleField
   *
   * @param {ObjectId} id - The objectId of ArticleField.
   * @returns {Promise<Post, APIError>}
   */
  async get(id) {
    try {
      let ArticleField;

      if (mongoose.Types.ObjectId.isValid(id)) {
        ArticleField = await this.findById(id)
          .exec();
      }
      if (ArticleField) {
        return ArticleField;
      }

      throw new APIError({
        message: 'Attendance permission does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * List categories in descending order of 'createdAt' timestamp.
   *
   * @param {number} skip - Number of categories to be skipped.
   * @param {number} limit - Limit number of categories to be returned.
   * @returns {Promise<User[]>}
   */
  list({
    page = 1, perPage = 30, sort, _class, student, lecturer,
  }) {
    perPage = parseInt(perPage, 10);
    page = parseInt(page, 10);
    const options = omitBy({ class: _class, student, lecturer }, isNil);
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options)
      .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  },

  /**
   * Count radios.
   * @returns {Promise<Number>}
   */
  async count({
    _class, student, lecturer,
  }) {
    const options = omitBy({ class: _class, student, lecturer }, isNil);
    return this.find(options).count();
  },
};

module.exports = mongoose.model('AttendancePermission', attendancePermission);
