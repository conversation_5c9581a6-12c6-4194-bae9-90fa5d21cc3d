// 7/7/2021
const fields = {
  table: 'm400',
  _id: 'pm400', // ID
  employee: 'fm100', // ID Nhân viên / giảng viên
  work_day: 'mn402', // Ngày công
  income: 'mn403', // S<PERSON> tiền thực nhận
  income_at: 'mv404', // Tháng năm nhận

  deleted_by: 'ml444', // Email người xóa
  deleted_at: 'ml445', // Thời gian xóa
  created_by: 'ml447', // Email người tạo
  updated_by: 'ml449', // Email người cập nhật
  updated_at: 'ml448',
  created_at: 'ml446',
};

const schema = (sequelize, DataTypes) => {
  const incomeHistorySchema = sequelize.define('IncomeHistory', {
    [fields._id]: {
      // ID
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.work_day]: {
      // Ngày công
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.income]: {
      // Số tiền thực nhận
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.income_at]: {
      // Tháng năm nhận
      type: DataTypes.DATE,
      defaultValue: null,
    },

    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });
  const IncomeHistory = incomeHistorySchema;
  console.log(IncomeHistory);
};

module.exports = {
  schema,
  fields,
};
