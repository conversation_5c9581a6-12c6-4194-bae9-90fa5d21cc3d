// 2/12/2022
const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/absent.controller');
const { authorize, ADMIN } = require('../../../../middlewares/auth');
const {
  listAbsents,
  createAbsent,
  listAbsence,
  studentAbsence,
} = require('../../validations/absent.validation');

const router = express.Router();

/**
 * Load post when API with id route parameter is hit
 */
// router.param('id', controller.load);


router
  .route('/')
  .get(authorize(), validate(listAbsents), controller.list)
  .post(authorize(), validate(createAbsent), controller.create);

router
  .route('/list-absence')
  .get(authorize(), validate(listAbsence), controller.listAbsence);
router
  .route('/student-absence')
  .get(authorize(), validate(studentAbsence), controller.studentAbsence);

router
  .route('/optimize-data')
  .get(authorize(ADMIN), controller.optimizeData);

router
  .route('/:id')
  .patch(authorize(), controller.update)
  .delete(authorize(), controller.remove);


module.exports = router;
