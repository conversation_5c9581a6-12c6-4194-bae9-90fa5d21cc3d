// 8/6/2023
const mongoose = require('mongoose');
const httpStatus = require('http-status');
const { omitBy, isNil } = require('lodash');
const APIError = require('../../../utils/APIError');

/**
 * WorkReport Schema
 * @private
 */
const workReport = new mongoose.Schema({
  employee: {
    type: Number,
    require: true,
  },
  department: {
    type: Number,
    require: true,
  },
  employee_info: {
    type: Object,
  },
  calendar: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'WorkCalendar',
  },
  work_this_week: {
    type: String,
    default: '',
    trim: true,
  },
  work_mow: {
    type: String,
    default: '',
    trim: true,
  },
  work_next_week: {
    type: String,
    default: '',
    trim: true,
  },
  problem: {
    type: String,
    default: '',
    trim: true,
  },
  suggestion: {
    type: String,
    default: '',
    trim: true,
  },
  is_active: {
    type: Boolean,
    default: true,
  },

  created_by: {
    type: String,
    default: '',
  },
  updated_by: {
    type: String,
    default: '',
  },
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
});

/**
 * Methods
 */
workReport.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'employee',
      'employee_info',
      'department',
      'calendar',
      'work_this_week',
      'work_mow',
      'work_next_week',
      'problem',
      'suggestion',
      'is_active',
      'created_at',
    ];

    fields.forEach((field) => {
      transformed[field] = this[field];
    });

    return transformed;
  },
});

/**
 * Statics
 */
workReport.statics = {
  /**
   * Get WorkReport
   *
   * @param {ObjectId} id - The objectId of WorkReport.
   * @returns {Promise<Post, APIError>}
   */
  async get(id) {
    try {
      let WorkReport;

      if (mongoose.Types.ObjectId.isValid(id)) {
        WorkReport = await this.findById(id)
          .populate('calendar', '_id week month year start_at end_at')
          .exec();
      }
      if (WorkReport) {
        return WorkReport;
      }

      throw new APIError({
        message: 'WorkReport does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * List categories in descending order of 'createdAt' timestamp.
   *
   * @param {number} skip - Number of categories to be skipped.
   * @param {number} limit - Limit number of categories to be returned.
   * @returns {Promise<User[]>}
   */
  list({
    page = 1,
    perPage = 30,
    sort,
    calendar,
    employee,
    department,
  }) {
    perPage = parseInt(perPage, 10);
    page = parseInt(page, 10);
    const options = omitBy({
      calendar,
      employee,
    }, isNil);
    if (department) {
      options.department = { $in: department };
    }
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options)
      .populate('calendar', '_id week month year start_at end_at')
      .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  },

  /**
   * Count radios.
   * @returns {Promise<Number>}
   */
  async count({
    calendar, employee, department,
  }) {
    const options = omitBy({
      calendar,
      employee,
    }, isNil);
    if (department) {
      options.department = { $in: department };
    }
    return this.find(options)
      .populate('calendar', '_id week month year start_at end_at')
      .count();
  },
};

/**
 * @typedef WorkReport
 */
module.exports = mongoose.model('WorkReport', workReport);
