const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/article.field.controller');
const { authorize, ADMIN } = require('../../../../middlewares/auth');
const {
  listArticle<PERSON>ields,
  createArt<PERSON><PERSON>ield,
  update<PERSON>rt<PERSON><PERSON>ield,
  deleteArticle<PERSON>ield,
} = require('../../validations/article.field.validation');

const router = express.Router();

/**
 * Load post when API with id route parameter is hit
 */
router.param('id', controller.load);


router
  .route('/')
  /**
   * @api {get} v1/post-category List Categories
   * @apiDescription Get list
   * @apiVersion 1.0.0
   * @apiName ListPostCategories
   * @apiGroup Post Category
   *
   * @apiParam  {Number{1-}}         [page=1]     List page
   * @apiParam  {Number{1-100}}      [perPage=1]  Per page
   * @apiParam  {String}             [title]      Title
   *
   * @apiSuccess {Object[]} categories List of post categories.
   */
  .get(validate(listArticleFields), controller.list)
  /**
   * @api {post} v1/post-category Create Category
   * @apiDescription Create a new category
   * @apiVersion 1.0.0
   * @apiName CreatePost
   * @apiGroup Post Category
   * @apiPermission admin
   *
   * @apiHeader {String} Athorization  User's access token
   *
   * @apiParam  {String}     title      title
   * @apiParam  {String}     info       info
   * @apiParam  {Number}      order      order
   *
   * @apiSuccess (Created 201) {String}  id         id
   * @apiSuccess (Created 201) {String}  title      title
   * @apiSuccess (Created 201) {String}  info       info
   * @apiSuccess (Created 201) {Number}  order      order
   * @apiSuccess (Created 201) {Date}    createdAt  Timestamp
   *
   * @apiError (Bad Request 400)   ValidationError  Some parameters may contain invalid values
   * @apiError (Unauthorized 401)  Unauthorized     Only authenticated users can create the data
   * @apiError (Forbidden 403)     Forbidden        Only admins can create the data
   */
  .post(authorize(ADMIN), validate(createArticleField), controller.create);


router
  .route('/:id')
  /**
   * @api {get} v1/post-category/:id Get Category
   * @apiDescription Get category information
   * @apiVersion 1.0.0
   * @apiName GetPost
   * @apiGroup Post Category
   *
   * @apiSuccess {String}  id         id
   * @apiSuccess {String}  title      title
   * @apiSuccess {String}  info       info
   * @apiSuccess {Number}  order      order
   * @apiSuccess {Boolean} is_active  is_active
   * @apiSuccess {Date}    createdAt  Timestamp
   *
   * @apiError (Not Found 404)    NotFound     Post does not exist
   */
  .get(authorize(), controller.get)
  /**
   * @api {patch} v1/post-category/:id Update Category
   * @apiDescription Update some fields of a category document
   * @apiVersion 1.0.0
   * @apiName UpdatePost
   * @apiGroup Post Category
   * @apiPermission admin
   *
   * @apiHeader {String} Athorization  User's access token
   *
   * @apiParam  {String}     title      title
   * @apiParam  {String}     info       info
   * @apiParam  {Number}      order      order
   * @apiParam  {Boolean}    is_active  is_active
   *
   * @apiSuccess {String}  id         id
   * @apiSuccess {String}  title      title
   * @apiSuccess {String}  info       info
   * @apiSuccess {Number}  order      order
   * @apiSuccess {Boolean} is_active  is_active
   * @apiSuccess {Date}    createdAt  Timestamp
   *
   * @apiError (Bad Request 400)  ValidationError  Some parameters may contain invalid values
   * @apiError (Unauthorized 401) Unauthorized Only authenticated users can modify the data
   * @apiError (Forbidden 403)    Forbidden    Only admins can modify the data
   * @apiError (Not Found 404)    NotFound     Post does not exist
   */
  .patch(authorize(ADMIN), validate(updateArticleField), controller.update)
  /**
   * @api {delete} v1/post-category/:id Delete Category
   * @apiDescription Delete a category
   * @apiVersion 1.0.0
   * @apiName DeletePost
   * @apiGroup Post Category
   * @apiPermission admin
   *
   * @apiHeader {String} Athorization  User's access token
   *
   * @apiSuccess (No Content 204)  Successfully deleted
   *
   * @apiError (Unauthorized 401) Unauthorized  Only authenticated users can delete the data
   * @apiError (Forbidden 403)    Forbidden     Only admins can delete the data
   * @apiError (Not Found 404)    NotFound      Post does not exist
   */
  .delete(authorize(ADMIN), validate(deleteArticleField), controller.remove);


module.exports = router;
