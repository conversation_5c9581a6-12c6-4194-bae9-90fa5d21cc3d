// 13/2/2023
const { Op } = require('sequelize');
const employeeFields = (require('./employee.model')).fields;
const moment = require('moment');

const fields = {
  table: 'm470',
  _id: 'pm470', // ID
  employee: 'fm100', // ID Nhân viên / giảng viên
  date: 'md472', // <PERSON><PERSON><PERSON> tháng năm chấm công
  start_at: 'mv473', // Giờ vào
  end_at: 'mv474', // Giờ ra

  deleted_by: 'ml444',
  deleted_at: 'ml445',
  created_by: 'ml447',
  updated_by: 'ml449',
  updated_at: 'ml448',
  created_at: 'ml446',
};

const schema = (sequelize, DataTypes) => {
  const timekeepingSchema = sequelize.define('Timekeeping', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.date]: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    [fields.start_at]: {
      type: DataTypes.STRING(10),
      defaultValue: '0',
    },
    [fields.end_at]: {
      type: DataTypes.STRING(10),
      defaultValue: '0',
    },

    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Timekeeping = timekeepingSchema;

  timekeepingSchema.countItem = async (query) => {
    const count = await Timekeeping.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  timekeepingSchema.list = async ({
    page = 1,
    perPage = -1,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    employee = { [Op.not]: null },
    from = null,
    to = null,
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const where = {
      [fields.deleted_at]: null,
      [fields.employee]: employee,
    };
    if (from && to) {
      where[fields.date] = {
        [Op.between]: [Date.parse(from), Date.parse(to)],
      };
    }

    const count = await Timekeeping.countItem({
      ...where,
    });
    const timekeeping = await Timekeeping.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.date, 'date'],
        [fields.start_at, 'start_at'],
        [fields.end_at, 'end_at'],
      ],
      where: {
        ...where,
      },
      include: {
        model: employeeModel,
        as: 'employee',
        attributes: [
          [employeeFields._id, '_id'],
          [employeeFields.first_name, 'first_name'],
          [employeeFields.last_name, 'last_name'],
        ],
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: timekeeping };
  };

  timekeepingSchema.checkExists = async ({ employee, date }) => {
    try {
      const query = {
        [fields.employee]: employee,
        [fields.date]: Date.parse(date),
        [fields.deleted_at]: null,
      };
      const count = await Timekeeping.count({
        where: {
          ...query,
        },
      });
      return count;
    } catch (error) {
      throw error;
    }
  };

  timekeepingSchema.remove = async ({ id, email }) => {
    try {
      const result = await Timekeeping.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  return timekeepingSchema;
};

module.exports = {
  schema,
  fields,
};
