// 9/11/2021
const _ = require('lodash');
const db = require('../../../../config/mysql');
const { fields } = require('../models/assignment.content.model');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

const Assignment = db.assignment;
const Employee = db.employee;
const AssignmentContent = db.assignmentContent;
const Notification = db.notification;

/**
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const assignmentContent = await AssignmentContent.get({
      id,
      employeeModel: Employee,
      assignmentModel: Assignment,
    });
    req.locals = { assignmentContent };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const { user } = req;
    data.created_by = req.user.email;
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });

    const assignmentContent = AssignmentContent.build({
      ...dbData,
    });
    const saved = await assignmentContent.save();
    const notificationData = {
      employee: assignmentContent[fields.employee_to],
      content: `<b>${req.user.last_name} ${req.user.first_name}</b> đã thêm công việc <b>${assignmentContent[fields.title]}</b> cho bạn`,
      type: 'assignment',
      created_by: user.email,
    };
    const notification = await Notification.create({ ...notificationData });
    jsonSuccess({ assignment: saved, notification }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    const assignmentContents = await AssignmentContent.list({
      ...query,
      employeeModel: Employee,
      assignmentModel: Assignment,
      employee: req.user._id,
    });

    jsonSuccess(assignmentContents, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const assignmentContent = req.locals.assignmentContent.dataValues;
    const { user } = req;
    if (assignmentContent.employee_from.dataValues._id === user._id
      || assignmentContent.employee_to.dataValues._id === user._id) {
      if (req.body.status === 2) {
        req.body.progress = 100;
      }
      if (req.body.status && req.body.status !== assignmentContent.status) {
        const notificationData = {
          employee: assignmentContent.employee_to.dataValues._id,
          content: `<b>${user.last_name} ${user.first_name}</b> đã thay đổi trạng thái công việc <b>${assignmentContent.title}</b>`,
          type: 'assignment',
        };
        await Notification.create({ ...notificationData });
      }
      await AssignmentContent.patch({
        id: req.params.id,
        data: {
          ...req.body,
          updated_by: req.user.email,
        },
      });
    } else {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.FORBIDDEN,
      });
    }

    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await AssignmentContent.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};
