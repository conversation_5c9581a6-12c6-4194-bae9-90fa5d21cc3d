// 5/1/2022
/* eslint-disable no-await-in-loop */
const db = require('../../../../config/mysql');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const { rolesVar } = require('../../../../config/vars');
const { workdayVar } = require('../../../../config/vars');

const Workday = db.workday;
const Employee = db.employee;
const Department = db.department;

/**
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const workday = await Workday.get({ id, employeeModel: Employee });
    req.locals = { workday };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get workday
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    const { workday } = req.locals;
    const { user } = req;
    const employee = workday.dataValues.employee.dataValues._id;
    if (employee === user._id || rolesVar.admin.includes(user.role)) {
      jsonSuccess(req.locals.workday, req, res);
    } else {
      throw new APIError({
        message: 'You don\'t permission',
        status: httpStatus.BAD_REQUEST,
      });
    }
  } catch (error) {
    next(error);
  }
};

// Create and Save a new Workday
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const workday = await Workday.create(data);
    jsonSuccess(workday, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    query.employee = req.user._id;
    const workday = await Workday.list({ ...query, employeeModel: Employee });

    jsonSuccess(workday, req, res);
  } catch (error) {
    next(error);
  }
};

exports.listAll = async (req, res, next) => {
  try {
    const { query } = req;
    const workday = await Workday.list({ ...query, employeeModel: Employee });

    jsonSuccess(workday, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await Workday.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

// tính toán ngày công mỗi tháng
exports.autoCalculate = async (req, res, next) => {
  try {
    const now = new Date(req.body.now) || new Date();
    const nowMonth = now.getMonth();
    const nowYear = now.getFullYear();
    const nowDate = now.getDate();
    if (nowDate === 21) {
      const dayMilliseconds = 1000 * 60 * 60 * 24; // số ms 1 ngày
      const start = new Date(nowYear, nowMonth, workdayVar.start);
      const finish = new Date(nowYear, nowMonth + 1, workdayVar.finish);
      const differentDays = ((finish - start) / dayMilliseconds) + 1;
      let sundays = 0;
      let startClone = new Date(start);

      while (startClone <= finish) {
        const day = startClone.getDay();
        if (day === 0) {
          sundays += 1;
        }
        startClone = new Date(+startClone + dayMilliseconds);
      }
      const month_workday = differentDays - sundays;
      const employees = await Employee.list({
        departmentModel: Department,
        is_lecturer: 0,
        perPage: -1,
        work_status: 0,
      });
      for (let i = 0; i < employees.total; i += 1) {
        const item = employees.data[i].dataValues._id;

        await Workday.findOrCreate({
          where: {
            employee: item,
            start_date: start,
          },
          data: {
            employee: item,
            start_date: start,
            month_workday,
            total_workday: 0,
          },
        });
      }
      jsonSuccess({
        month_workday,
        differentDays,
        sundays,
      }, req, res);
    } else {
      throw new APIError({
        message: 'Go back to sleep, Samurai',
        status: httpStatus.BAD_REQUEST,
      });
    }
  } catch (error) {
    next(error);
  }
};
