const { success: jsonSuccess } = require('../../../middlewares/success');
const Paycheck = require('../models/paycheck.model');

/**
 * Get paycheck list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const { email } = req.user;
    const paychecks = await Paycheck.list({ ...req.query, email, status: 'sent' });
    const count = await Paycheck.count({ ...req.query, email, status: 'sent' });
    const transformed = paychecks.map(paycheck => paycheck.transform());
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

