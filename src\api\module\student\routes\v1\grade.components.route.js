// 27/9/2023
const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/grade.components.controller');
const { authorize } = require('../../../../middlewares/auth');
const {
  createGradeComponent,
  listGradeComponents,
  updateGradeComponent,
  deleteGradeComponent,
} = require('../../validations/grade.components.validation');

const router = express.Router();

router.param('id', controller.load);

router
  .route('/')
  .get(authorize(), validate(listGradeComponents), controller.list)
  .post(authorize(), validate(createGradeComponent), controller.create);

router
  .route('/:id')
  .get(controller.get)
  .patch(authorize(), validate(updateGradeComponent), controller.update)
  .delete(authorize(), validate(deleteGradeComponent), controller.remove);


module.exports = router;
