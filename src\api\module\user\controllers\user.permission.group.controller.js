// 19/02/2021
const moment = require('moment-timezone');
const { handler: error<PERSON><PERSON><PERSON> } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const db = require('../../../../config/mysql');

const PermissionGroup = db.userPermissionGroup;

/**
 * Load userPermissionGroup and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const userPermissionGroup = await PermissionGroup.get(id);
    req.locals = { userPermissionGroup };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get userPermissionGroup
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.userPermissionGroup, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Create new userPermissionGroup
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    const saved = await PermissionGroup.insert(req.body);

    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing userPermissionGroup
 * @public
 */
exports.update = async (req, res, next) => {
  try {
    await PermissionGroup.patch({
      data: req.body,
      _id: req.params.id,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Get userPermissionGroup list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await PermissionGroup.countItem(req.query);
    const userPermissionInterfaces = await PermissionGroup.list(req.query);
    jsonSuccess({ total: count, docs: userPermissionInterfaces }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete userPermissionGroup
 * @public
 */
exports.remove = async (req, res, next) => {
  try {
    await PermissionGroup.patch({
      data: {
        deleted_at: moment(),
      },
      _id: req.params.id,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

