// 27/01/2021
// DS tài khoản
const httpStatus = require('http-status');
const moment = require('moment-timezone');
const { Op } = require('sequelize');
const jwt = require('jwt-simple');
const md5 = require('md5');
const APIError = require('../../../utils/APIError');
const Employee = (require('../../employee/models/employee.model')).schema;
const employeeFields = (require('../../employee/models/employee.model')).fields;
const { jwtSecret, jwtExpirationInterval, rolesVar } = require('../../../../config/vars');

const ADMIN = rolesVar.admin.map(element => parseInt(element, 10));
const HR_DEPT = rolesVar.hr_dept.map(element => parseInt(element, 10));
const isChangePasswordOpts = [[0, 1]]; // 0: chưa đổi mật khẩu. 1: đã đổi mật khẩu
const isActiveOpts = [[0, 1]]; // (0: <PERSON><PERSON><PERSON> k<PERSON>ch hoạt, 1: Đã kích hoạt)
const userTypedOpts = [['N100', 'M100']];// (N100: Sinh viên, M100: Nhân viên / giảng viên)"

const fields = {
  table: 'q100',
  employee: 'fn100', // ID sinh viên / nhân viên / giảng viên
  email: 'qv101', // Email sinh viên / nhân viên / giảng viên
  password: 'qv102', // Mật khẩu đăng nhập đã mã hóa
  is_active: 'qn103', // "Đánh dấu đã kích hoạt tài khoản   (0: Chưa kích hoạt, 1: Đã kích hoạt)"
  user_type: 'qv104', // "Phân biệt table N100 và table M100 (N100: Sinh viên, M100: Nhân viên / giảng viên)"
  password_plain: 'qv105', // Mật khẩu đăng nhập ko che full hd
  is_change_password: 'qn106',
  deleted_at: 'ql145', // Thời gian xóa
  created_at: 'ql146', // Thời gian tạo
  created_by: 'ql147', // Email người tạo
  updated_at: 'ql148', // Thời gian cập nhật
  updated_by: 'ql149', // Email người cập nhật
};

const schema = (sequelize, DataTypes) => {
  const userSchema = sequelize.define('User', {
    [fields.email]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.password]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.is_active]: {
      type: DataTypes.INTEGER,
      defaultValue: null,
      validate: {
        isIn: isActiveOpts,
      },
    },
    [fields.user_type]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
      validate: {
        isIn: userTypedOpts,
      },
    },
    [fields.password_plain]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.is_change_password]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: isChangePasswordOpts,
      },
    },
    [fields.deleted_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });
  userSchema.removeAttribute('id');

  const User = userSchema;
  const employee = Employee(sequelize, DataTypes);

  userSchema.findAndGenerateToken = async ({
    body,
    permissionModel,
    userPermissionModel,
    studentModel,
  }) => {
    const Permission = permissionModel;
    const { email, password } = body;
    if (!email) throw new APIError({ status: httpStatus.BAD_REQUEST, message: 'An email is required to generate a token' });

    const user = await User.findOne({
      attributes: [
        [fields.email, 'email'],
        [fields.user_type, 'user_type'],
        [fields.is_active, 'is_active'],
      ],
      where: {
        [fields.email]: email,
        [fields.password]: md5(password),
        [fields.deleted_at]: null,
      },
      include: {
        model: employee,
        as: 'employee',
        attributes: [
          [employeeFields._id, '_id'],
          [employeeFields.is_lecturer, 'is_lecturer'],
          [employeeFields.first_name, 'first_name'],
          [employeeFields.last_name, 'last_name'],
          [employeeFields.dob, 'dob'],
          [employeeFields.sex, 'sex'],
          [employeeFields.department, 'department'],
        ],
      },
    });
    if (!user) {
      throw new APIError({
        status: httpStatus.BAD_REQUEST,
        message: 'Wrong email or password',
      });
    } else if (user.dataValues.user_type.toUpperCase() !== 'M100') { // sinh viên đăng nhập
      const Student = studentModel;
      const student = await Student.getByEmail(email);
      // employee = student
      Object.assign(user.dataValues, { employee: student.dataValues });
      const token = User.genToken({ id: student.dataValues._id, is_student: true });
      const now = new Date();
      const add = jwtExpirationInterval * 60000;
      const expiry_date = new Date(now.getTime() + add);
      return {
        token,
        user,
        student,
        expiry_date,
        is_admin: false,
        is_post: false,
        is_hr: false,
        is_student: true,
      };
    }
    const _id = user.dataValues.employee.dataValues._id;

    const permission = await Permission.findOne({
      where: {
        fm100: _id,
        fq350: {
          [Op.or]: ADMIN,
        },
        ql145: null,
      },
    });

    const permissionHr = await Permission.findOne({
      where: {
        fm100: _id,
        fq350: {
          [Op.or]: HR_DEPT,
        },
        ql145: null,
      },
    });

    const permissionPost = await userPermissionModel.findOne({
      where: {
        fm100: _id,
        ql145: null,
        qv178: { [Op.startsWith]: 'FUNC_POST_DOCUMENT_' },
      },
    });

    const token = User.genToken({ id: user.dataValues.employee.dataValues._id });
    const now = new Date();
    const add = jwtExpirationInterval * 60000;
    const expiry_date = new Date(now.getTime() + add);
    return {
      token,
      user,
      is_admin: !!permission,
      is_post: !!permissionPost,
      is_hr: !!permissionHr,
      expiry_date,
    };
  };

  userSchema.genToken = ({ id, is_student = false }) => {
    const playload = {
      exp: moment().add(jwtExpirationInterval, 'minutes').unix(),
      iat: moment().unix(),
      sub: id,
      is_student,
    };
    return jwt.encode(playload, jwtSecret);
  };

  userSchema.updatePassword = async ({ email, newPassword, password }) => {
    const updated = await User.update({
      [fields.password_plain]: newPassword,
      [fields.password]: md5(newPassword),
    }, {
      where: {
        [fields.email]: email,
        [fields.password_plain]: password,
      },
    });
    return updated;
  };

  userSchema.updatePasswordWithoutPassword = async ({ email, newPassword }) => {
    const updated = await User.update({
      [fields.password_plain]: newPassword,
      [fields.password]: md5(newPassword),
    }, {
      where: {
        [fields.email]: email,
      },
    });
    return updated;
  };

  userSchema.get = async (email) => {
    const user = await User.findOne({
      attributes: [
        [fields.email, 'email'],
        [fields.is_active, 'is_active'],
        [fields.password_plain, 'password_plain'],
        [fields.deleted_at, 'deleted_at'],
      ],
      where: {
        [fields.email]: email,
        [fields.deleted_at]: null,
      },
    });

    if (user) {
      return user;
    }
    throw new APIError({
      status: httpStatus.BAD_REQUEST,
      message: 'Not found user',
    });
  };

  return userSchema;
};

module.exports = {
  schema,
  opts: {
    isChangePasswordOpts,
    isActiveOpts,
    userTypedOpts,
  },
  fields,
};
