const Joi = require('joi');

module.exports = {

  // GET /v1/article/type
  listArticleTypes: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      title: Joi.string(),
      sort: Joi.string(),
    }),
  },

  // POST /v1/article/type
  createArticleType: {
    body: Joi.object({
      title: Joi.string().required(),
    }),
  },

  // PATCH /v1/article/type/:id
  updateArticleType: {
    body: Joi.object({
      title: Joi.string(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/article/type/:id
  deleteArticleType: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },
};
