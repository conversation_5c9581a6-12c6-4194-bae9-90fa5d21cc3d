{"name": "project-hr", "version": "1.0.0", "description": "core", "author": "Dat <<EMAIL>>", "main": "src/index.js", "private": false, "license": "MIT", "engines": {"node": ">=7.6", "yarn": "*"}, "scripts": {"precommit": "yarn lint", "prestart": "yarn docs", "start": "pm2 start ./src/index.js --name project-hr", "dev": "nodemon ./src/index.js", "lint": "eslint **/*.js --ignore-path .gitignore --ignore-pattern internals/scripts", "lint:fix": "yarn lint -- --fix", "lint:watch": "yarn lint -- --watch", "test": "cross-env NODE_ENV=test nyc --reporter=html --reporter=text mocha --timeout 20000 --recursive src/api/tests", "test:unit": "cross-env NODE_ENV=test mocha src/api/tests/unit", "test:integration": "cross-env NODE_ENV=test mocha --timeout 20000 src/api/tests/integration", "test:watch": "cross-env NODE_ENV=test mocha --watch src/api/tests/unit", "coverage": "nyc report --reporter=text-lcov | coveralls", "postcoverage": "opn coverage/lcov-report/index.html", "validate": "yarn lint && yarn test", "postpublish": "git push --tags", "deploy": "sh ./deploy.sh", "docs": "apidoc -i src -o docs", "postdocs": "opn docs/index.html", "docker:start": "cross-env NODE_ENV=production pm2-docker start ./src/index.js", "docker:prod": "docker-compose -f docker-compose.yml -f docker-compose.prod.yml up", "docker:dev": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up", "docker:test": "docker-compose -f docker-compose.yml -f docker-compose.test.yml up --abort-on-container-exit"}, "repository": {"type": "git", "url": "https://github.com/dn1ne/project-hr"}, "keywords": ["express", "node", "node.js", "mongodb", "mongoose", "passport", "es6", "es7", "es8", "es2017", "mocha", "istanbul", "nyc", "eslint", "Travis CI", "coveralls", "REST", "API", "boilerplate", "generator", "starter project"], "dependencies": {"acl": "^0.4.11", "axios": "^0.16.2", "bcryptjs": "2.4.3", "bluebird": "^3.5.0", "body-parser": "^1.17.0", "compression": "^1.6.2", "cors": "^2.8.3", "cron": "^2.1.0", "cross-env": "^5.0.1", "dotenv-safe": "^6.1.0", "express": "^4.17.1", "express-fileupload": "^0.4.0", "express-validation": "^3.0.6", "fs-extra": "^7.0.1", "googleapis": "105", "helmet": "^3.5.0", "html-pdf-node": "^1.0.8", "http-status": "^1.0.1", "joi": "^17.2.1", "jwt-simple": "0.5.1", "lodash": "^4.17.4", "md5": "^2.3.0", "method-override": "^2.3.8", "moment": "^2.29.1", "moment-timezone": "^0.5.13", "mongodb": "^5.5.0", "mongoose": "^6.0.0", "morgan": "^1.8.1", "multer": "^1.3.0", "mysql2": "^2.2.5", "nodemailer": "^6.6.5", "passport": "^0.4.0", "passport-http-bearer": "^1.0.1", "passport-jwt": "^3.0.0", "pm2": "^2.4.6", "qrcode": "^1.5.4", "request": "^2.83.0", "sequelize": "^6.4.0", "sharp": "^0.25.4", "slug": "^0.9.1", "translate-google": "^1.5.0", "uuid": "^3.1.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"apidoc": "^0.17.5", "chai": "^4.1.0", "chai-as-promised": "^7.1.1", "coveralls": "^3.0.0", "eslint": "^4.2.0", "eslint-config-airbnb-base": "^12.0.1", "eslint-plugin-import": "^2.2.0", "husky": "^0.14.3", "mocha": "^3.3.0", "nodemon": "^1.18.6", "nyc": "^11.0.3", "opn-cli": "^3.1.0", "sinon": "^4.0.1", "sinon-chai": "^2.10.0", "supertest": "^3.0.0"}}