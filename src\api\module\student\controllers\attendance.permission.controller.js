// 30/11/2022
const APIError = require('../../../utils/APIError');
const httpStatus = require('http-status');
const AttendancePermission = require('../models/attendance.permission.model');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const db = require('../../../../config/mysql');
const classField = require('../models/class.model').fields;

const Class = db.class;
/**
 * Load article field and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const attendancePermission = await AttendancePermission.get(id);
    req.locals = { attendancePermission };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get article field
 * @public
 */
exports.get = (req, res) => {
  jsonSuccess(req.locals.attendancePermission.transform(), req, res);
};

/**
 * Create new article field
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user.email;
    const classCount = await Class.countItem({
      [classField._id]: req.body.class,
      [classField.lecturer]: req.user._id,
    });
    if (classCount === 0) {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.BAD_REQUEST,
      });
    }
    const checkExist = await AttendancePermission.count({
      _class: req.body.class,
      student: req.body.student,
    });
    if (checkExist > 0) {
      throw new APIError({
        message: 'Attendance Permission already exists',
        status: httpStatus.BAD_REQUEST,
      });
    }

    const attendancePermission = new AttendancePermission(req.body);
    const saved = await attendancePermission.save();
    jsonSuccess(saved.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing article field
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const attendancePermission = Object.assign(req.locals.attendancePermission, req.body);

  attendancePermission.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get article field list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await AttendancePermission.count(req.query);
    const types = await AttendancePermission.list(req.query);
    const transformed = types.map(type => type.transform());

    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete article field
 * @public
 */

exports.remove = async (req, res, next) => {
  try {
    const { attendancePermission } = req.locals;
    const classCount = await Class.countItem({
      [classField._id]: attendancePermission.class,
      [classField.lecturer]: req.user._id,
    });
    if (classCount === 0) {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.BAD_REQUEST,
      });
    }
    await attendancePermission.remove();
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};
