const db = require('../../../../config/mysql');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');

const Contract = db.contract;
const { fields } = require('../models/contract.model');

/**
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const contract = await Contract.get(id);
    req.locals = { contract };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get contract
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.contract, req, res);
  } catch (error) {
    next(error);
  }
};

// Create and Save a new Contract
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const { user } = req;
    const contract = Contract.build({
      [fields.employee]: data.employee,
      [fields.employee_represent]: data.employee_represent,
      [fields.organization]: data.organization,
      [fields.contract_id]: data.contract_id,
      [fields.address]: data.address,
      [fields.type]: data.type,
      [fields.term]: data.term,
      [fields.phone]: data.phone,
      [fields.represent_position]: data.represent_position,
      [fields.qualification]: data.qualification,
      [fields.start_at]: data.start_at,
      [fields.end_at]: data.end_at,
      [fields.work_address]: data.work_address,
      [fields.other_address]: data.other_address,
      [fields.work_to_do]: data.work_to_do,
      [fields.work_time]: data.work_time,
      [fields.requirement]: data.requirement,
      [fields.vehicle]: data.vehicle,
      [fields.gross]: data.gross,
      [fields.gross_text]: data.gross_text,
      [fields.payment_method]: data.payment_method,
      [fields.contract_created_at]: data.contract_created_at,
      [fields.sign_at]: data.sign_at,
      [fields.attach]: data.attach,
      [fields.created_by]: user.email,
      // [fields.updated_by]: user._id,
    });
    const savedContract = await contract.save();
    jsonSuccess(savedContract, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    const contracts = await Contract.list(query);
    jsonSuccess(contracts, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await Contract.remove({
      id: req.params.id,
      userId: req.user._id,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await Contract.patch({
      id: req.params.id,
      data: {
        ...req.body,
        updated_by: req.user.email,
      },
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};
