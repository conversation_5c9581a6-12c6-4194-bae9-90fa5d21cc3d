const Joi = require('joi');
const RecruitmentRequest = require('../models/recruitment_request.model');

module.exports = {

  // GET /v1/plan/recruitment_request
  listRecruitmentRequests: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      name: Joi.string(),
      sort: Joi.string(),
      department: Joi.string(),
      year: Joi.number().min(2000).max(2999),
      is_active: Joi.bool(),
    }),
  },

  // POST /v1/plan/recruitment_request
  createRecruitmentRequest: {
    body: Joi.object({
      year: Joi.number(),
      department: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
      position: Joi.string().allow(''),
      workforces: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)).min(1).required(),
      demand: Joi.string().allow('').valid(...RecruitmentRequest.demandOpts),
    }),
  },

  // PATCH /v1/plan/recruitment_request/:id
  updateRecruitmentRequest: {
    body: Joi.object({
      year: Joi.number(),
      department: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      workforces: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)).min(1),
      demand: Joi.string().allow('').valid(...RecruitmentRequest.demandOpts),
      position: Joi.string().allow(''),
      is_active: Joi.bool(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/plan/recruitment_request/:id
  deleteRecruitmentRequest: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

};
