// 27/11/2024
const _ = require('lodash');
const moment = require('moment');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const db = require('../../../../config/mysql');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { fields } = require('../models/grade.components.model');

const GradeComponent = db.gradeComponents;
const Class = db.class;

exports.load = async (req, res, next, id) => {
  try {
    const gradeComponent = await GradeComponent.get({ id });
    req.locals = { gradeComponent };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.get = (req, res) => {
  jsonSuccess(req.locals.gradeComponent, req, res);
};

exports.create = async (req, res, next) => {
  try {
    const { user, body } = req;
    const _class = await Class.get(body.class);
    if (_class.dataValues.lecturer !== req.user._id) {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.FORBIDDEN,
      });
    }
    req.body.created_by = user.email;
    const dbData = {};
    _.forEach(body, (value, key) => {
      dbData[fields[key]] = value;
    });
    const gradeComponent = GradeComponent.build({
      ...dbData,
    });
    const saved = await gradeComponent.save();
    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const _class = await Class.get(req.locals.gradeComponent.dataValues.class);
    if (_class.dataValues.lecturer !== req.user._id) {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.FORBIDDEN,
      });
    }
    const { user } = req;
    const data = req.body;
    data.updated_by = user.email;
    data.updated_at = moment().format();
    await GradeComponent.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const gradeComponent = await GradeComponent.list(req.query);
    jsonSuccess(gradeComponent, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const _class = await Class.get(req.locals.gradeComponent.dataValues.class);
    if (_class.dataValues.lecturer !== req.user._id) {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.FORBIDDEN,
      });
    }
    const result = await GradeComponent.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};
