const httpStatus = require('http-status');
const { Op } = require('sequelize');
const _ = require('lodash');
const moment = require('moment');
const APIError = require('../../../utils/APIError');
const employeeFields = (require('../../employee/models/employee.model')).fields;
const positionFields = (require('../../department/models/position.model')).fields;

const contractTypeOpts = [[0, 1, 2, 3]];
// Type (0: Hợp đồng lao động, 1: <PERSON>ợ<PERSON> đồng thỉnh giảng, 2: <PERSON>ợ<PERSON> đồng thử việc,
// 3: <PERSON>ợp đồng lao động bán thời gian, 4: <PERSON><PERSON><PERSON> đồng nghị)

const contractTermOpts = [[0, 1, 2, 3]];
// Period (0: Vô thời hạn (không xác định thời hạn), 1: <PERSON><PERSON> thờ<PERSON> hạn,
// 2: <PERSON><PERSON> thời hạn lần 2, 3: <PERSON><PERSON> thời hạn lần 3)

const defaultSort = 'pm300';
const fields = {
  table: 'm300',
  _id: 'pm300', // ID
  employee: 'fm100', // ID Nhân viên / giảng viên
  employee_represent: 'fm100e', // ID Nhân viên / giảng viên đại diện tổ chức
  position_represent: 'fn400e', // ID Chức vụ của bên đại diện tổ chức
  is_approved: 'mn301', // Trạng thái xét duyệt Approval status (0: Disapprove, 1: Approve)
  approved_at: 'md301', // Ngày xét duyệt
  contract_id: 'mv302cod', // Mã hợp đồng
  contract_at: 'md303', // Ngày tạo hợp đồng
  type: 'mn304', // Loại hợp đồng Type (0: Hợp đồng lao động, 1: Hợp đồng thỉnh giảng, 2: Hợp đồng thử việc, 3: Hợp đồng lao động bán thời gian, 4: Hợp đồng nghị)
  term: 'mn305', // Thời hạn hợp đồng,  (0: Vô thời hạn, 1: Có thời hạn, 2: Có thời hạn lần 2, 3: Có thời hạn lần 3)
  probationary_tern: 'mn306', // Thời gian thử việc (bao nhiêu tháng)
  probationary_work: 'mv307', // Công việc (ở hợp đồng thử việc)
  qualification: 'mv308', // Trình độ chuyên môn của người lao động
  start_at: 'md309', // Ngày bắt đầu có hiệu lực
  end_at: 'md309e', // Ngày chấm dứt hiệu lực
  work_address: 'mv310', // Địa điểm làm việc chính
  other_address: 'mv311', // Địa điểm làm việc khác
  work_to_do: 'mv312', // Công việc phải làm
  work_time: 'mv313', // Thời gian lao động
  requirement: 'mv314', // Trang thiết bị làm việc
  vehicle: 'mv315', // Phương tiện đi lại
  gross: 'mn316', // Thu nhập của người lao động
  gross_text: 'mv317', // Thu nhập viết bằng chữ
  gross_text_en: 'mv317_en', // Thu nhập viết bằng chữ tiếng Anh
  payment_method: 'mv319', // Hình thức trả lương
  signed_at: 'md320', // Ngày ký hợp đồng
  visiting_faculty_theory_income: 'mn321', // Thu nhập của giảng viên trên thời gian dạy lý thuyết
  visiting_faculty_practice_income: 'mn323', // Thu nhập của giảng viên trên thời gian dạy thực hành tại phòng Lab
  visiting_faculty_hospital_income: 'mn325', // Thu nhập của giảng viên trên thời gian dạy thực hành tại bệnh viện
  visiting_faculty_theory_minute: 'mn322', // Thời gian dạy lý thuyết (tính theo phút) để nhận lương
  visiting_faculty_practice_minute: 'mn325', // Thời gian dạy thực hành tại phòng Lab (tính theo phút) để nhận lương
  visiting_faculty_hospital_minute: 'mn325', // Thời gian dạy thực hành tại bệnh viện (tính theo phút) để nhận lương

  deleted_by: 'ml344', // Email người xóa
  deleted_at: 'ml345', // Thời gian xóa
  created_at: 'ml346', // Thời gian tạo
  created_by: 'ml347', // Email người tạo
  updated_at: 'ml348', // Thời gian cập nhật
  updated_by: 'ml349', // Email người cập nhật
};
const schema = (sequelize, DataTypes) => {
  const contractSchema = sequelize.define('Contract', {
    [fields._id]: {
      // ID Hợp đồng
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.contract_id]: {
      // Mã hợp đồng
      type: DataTypes.STRING(32),
      allowNull: false,
    },
    [fields.is_approved]: {
      // Trạng thái xét duyệt Approval status (0: Disapprove, 1: Approve)
      type: DataTypes.INTEGER(),
      defaultValue: 1,
    },
    [fields.approved_at]: {
      // Ngày xét duyệt
      type: DataTypes.DATEONLY(),
      defaultValue: null,
    },
    [fields.contract_at]: {
      // Ngày tạo hợp đồng
      type: DataTypes.DATEONLY(),
      defaultValue: null,
    },
    [fields.type]: {
      // Loại hợp đồng
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: contractTypeOpts,
      },
    },
    [fields.term]: {
      // Thời hạn hợp đồng
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: contractTermOpts,
      },
    },
    [fields.probationary_tern]: {
      // Thời gian thử việc (bao nhiêu tháng)
      type: DataTypes.INTEGER(),
      defaultValue: 0,
    },
    [fields.probationary_work]: {
      // Công việc (ở hợp đồng thử việc)
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.qualification]: {
      // Trình độ chuyên môn của người lao động
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.start_at]: {
      // Ngày bắt đầu có hiệu lực
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.end_at]: {
      // Ngày chấm dứt hiệu lực
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.work_address]: {
      // Địa điểm làm việc chính
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.other_address]: {
      // Địa điểm làm việc khác
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.work_to_do]: {
      // Công việc phải làm
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.work_time]: {
      // Thời gian lao động
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.requirement]: {
      // Trang thiết bị làm việc
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.vehicle]: {
      // Phương tiện đi lại
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.gross]: {
      // Thu nhập của người lao động
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.gross_text]: {
      // Thu nhập viết bằng chữ
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.gross_text_en]: {
      // Thu nhập viết bằng chữ tiếng Anh
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.payment_method]: {
      // Hình thức trả lương
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.signed_at]: {
      // Ngày ký hợp đồng
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.visiting_faculty_theory_income]: {
      // Thu nhập của giảng viên trên thời gian dạy lý thuyết
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.visiting_faculty_practice_income]: {
      // Thu nhập của giảng viên trên thời gian dạy thực hành tại phòng Lab
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.visiting_faculty_hospital_income]: {
      // Thu nhập của giảng viên trên thời gian dạy thực hành tại bệnh viện
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.visiting_faculty_theory_minute]: {
      // Thời gian dạy lý thuyết (tính theo phút) để nhận lương
      type: DataTypes.INTEGER,
      defaultValue: 50,
    },
    [fields.visiting_faculty_practice_minute]: {
      // Thời gian dạy thực hành tại phòng Lab (tính theo phút) để nhận lương
      type: DataTypes.INTEGER,
      defaultValue: 50,
    },
    [fields.visiting_faculty_hospital_minute]: {
      // Thời gian dạy thực hành tại bệnh viện (tính theo phút) để nhận lương
      type: DataTypes.INTEGER,
      defaultValue: 50,
    },

    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Contract = contractSchema;

  contractSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by,
    order_way = 'desc',
    deleted_at = null,
    type = { [Op.not]: null },
    term = { [Op.not]: null },
    start_at,
    end_at,
    contract_id,
    employee_name,
    employeeModel,
    employee = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    const options = {
      [fields.start_at]: start_at,
      [fields.end_at]: end_at,
    };
    if (deleted_at === 'not_null') {
      deleted_at = { [Op.not]: null };
    }

    if (type === 'all') {
      type = { [Op.not]: null };
    }

    if (term === 'all') {
      term = { [Op.not]: null };
    }

    if (!start_at || start_at === 'all') {
      delete options[fields.start_at];
    } else if (start_at) {
      start_at = { [Op.gte]: start_at };
    }

    if (!end_at || end_at === 'all') {
      delete options[fields.end_at];
    } else if (end_at) {
      end_at = { [Op.lte]: end_at };
    }

    if (!order_by || order_by === 'default') {
      order_by = defaultSort;
    } else {
      order_by = fields[order_by];
    }

    if (order_way === 'default') {
      order_way = 'desc';
    }

    if (!contract_id || contract_id === 'all') {
      contract_id = { [Op.not]: null };
    } else {
      contract_id = { [Op.substring]: contract_id };
    }

    if (!employee_name || employee_name === 'all') {
      employee_name = { [Op.not]: null };
    } else {
      employee_name = { [Op.in]: employee_name.split(' ') };
    }


    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await Contract.countItem({
      where: {
        [fields.deleted_at]: deleted_at,
        [fields.type]: type,
        [fields.term]: term,
        // [fields.start_at]: start_at,
        // [fields.end_at]: end_at,
        ...options,
        [fields.contract_id]: contract_id,
        [fields.employee]: employee,
      },
      employee_name,
      employeeModel,
    });
    const contracts = await Contract.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.contract_id, 'contract_id'],
        [fields.type, 'type'],
        [fields.term, 'term'],
        [fields.start_at, 'start_at'],
        [fields.end_at, 'end_at'],
      ],
      where: {
        [fields.deleted_at]: deleted_at,
        [fields.type]: type,
        [fields.term]: term,
        // [fields.start_at]: start_at,
        // [fields.end_at]: end_at,
        ...options,
        [fields.contract_id]: contract_id,
        [fields.employee]: employee,
      },
      include: [
        {
          model: employeeModel,
          as: 'employee',
          attributes: [
            [employeeFields._id, '_id'],
            [employeeFields.is_lecturer, 'is_lecturer'],
            [employeeFields.first_name, 'first_name'],
            [employeeFields.last_name, 'last_name'],
            [employeeFields.dob, 'dob'],
            [employeeFields.sex, 'sex'],
            [employeeFields.email, 'email'],
          ],
          where: {
            [Op.or]: {
              [employeeFields.first_name]: employee_name,
              [employeeFields.last_name]: employee_name,
            },
          },
        },
        {
          model: employeeModel,
          as: 'employee_represent',
          attributes: [
            [employeeFields._id, '_id'],
            [employeeFields.is_lecturer, 'is_lecturer'],
            [employeeFields.first_name, 'first_name'],
            [employeeFields.last_name, 'last_name'],
            [employeeFields.dob, 'dob'],
            [employeeFields.email, 'email'],
            [employeeFields.sex, 'sex'],
          ],
        },
      ],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: contracts };
  };

  contractSchema.countItem = async ({ where, employee_name, employeeModel }) => {
    const count = await Contract.count({
      where: {
        ...where,
      },
      include: [
        {
          model: employeeModel,
          as: 'employee',
          where: {
            [Op.or]: {
              [employeeFields.first_name]: employee_name,
              [employeeFields.last_name]: employee_name,
            },
          },
        },
      ],
    });
    return count;
  };

  contractSchema.get = async ({ id, employeeModel, positionModel }) => {
    try {
      const contract = await Contract.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.contract_id, 'contract_id'],
          [fields.is_approved, 'is_approved'],
          [fields.approved_at, 'approved_at'],
          [fields.type, 'type'],
          [fields.term, 'term'],
          [fields.probationary_tern, 'probationary_tern'],
          [fields.probationary_work, 'probationary_work'],
          [fields.qualification, 'qualification'],
          [fields.start_at, 'start_at'],
          [fields.end_at, 'end_at'],
          [fields.work_address, 'work_address'],
          [fields.other_address, 'other_address'],
          [fields.work_to_do, 'work_to_do'],
          [fields.work_time, 'work_time'],
          [fields.requirement, 'requirement'],
          [fields.vehicle, 'vehicle'],
          [fields.gross, 'gross'],
          [fields.gross_text, 'gross_text'],
          [fields.gross_text_en, 'gross_text_en'],
          [fields.payment_method, 'payment_method'],
          [fields.signed_at, 'signed_at'],
          [fields.contract_at, 'contract_at'],
          // [fields.position_represent, 'position_represent'],
          [fields.visiting_faculty_theory_income, 'visiting_faculty_theory_income'],
          [fields.visiting_faculty_hospital_income, 'visiting_faculty_theory_minute'],
          [fields.visiting_faculty_practice_income, 'visiting_faculty_practice_income'],
          [fields.visiting_faculty_hospital_income, 'visiting_faculty_practice_minute'],
          [fields.visiting_faculty_hospital_income, 'visiting_faculty_hospital_income'],
          [fields.visiting_faculty_hospital_income, 'visiting_faculty_hospital_minute'],
          [fields.deleted_by, 'deleted_by'],
          [fields.deleted_at, 'deleted_at'],
          [fields.created_at, 'created_at'],
          [fields.created_by, 'created_by'],
          [fields.updated_at, 'updated_at'],
          [fields.updated_by, 'updated_by'],
        ],
        include: [
          {
            model: employeeModel,
            as: 'employee',
            attributes: [
              [employeeFields._id, '_id'],
              [employeeFields.is_lecturer, 'is_lecturer'],
              [employeeFields.first_name, 'first_name'],
              [employeeFields.last_name, 'last_name'],
              [employeeFields.dob, 'dob'],
              [employeeFields.sex, 'sex'],
              [employeeFields.identification_number, 'identification_number'],
              [employeeFields.identification_date, 'identification_date'],
              [employeeFields.identification_place, 'identification_place'],
              [employeeFields.phone, 'phone'],
              [employeeFields.nationality, 'nationality'],
              [employeeFields.dob, 'dob'],
              [employeeFields.pob, 'pob'],
              [employeeFields.address, 'address'],
              [employeeFields.email, 'email'],
              [employeeFields.education, 'education'],
            ],
          },
          {
            model: employeeModel,
            as: 'employee_represent',
            attributes: [
              [employeeFields._id, '_id'],
              [employeeFields.is_lecturer, 'is_lecturer'],
              [employeeFields.first_name, 'first_name'],
              [employeeFields.last_name, 'last_name'],
              [employeeFields.dob, 'dob'],
              [employeeFields.sex, 'sex'],
              [employeeFields.identification_number, 'identification_number'],
              [employeeFields.identification_date, 'identification_date'],
              [employeeFields.identification_date, 'identification_place'],
              [employeeFields.phone, 'phone'],
              [employeeFields.nationality, 'nationality'],
              [employeeFields.dob, 'dob'],
              [employeeFields.pob, 'pob'],
              [employeeFields.address, 'address'],
            ],
          },
          {
            model: positionModel,
            as: 'position_represent',
            attributes: [
              [positionFields._id, '_id'],
              [positionFields.name_vn, 'name_vn'],
              [positionFields.name, 'name'],
              [positionFields.code, 'code'],
            ],
          },
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      if (contract) {
        return contract;
      }
      throw new APIError({
        message: 'Contract does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  contractSchema.remove = async ({ id, userId }) => {
    try {
      const result = await Contract.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: userId,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  contractSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Contract.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };
  return contractSchema;
};

module.exports = {
  schema,
  opts: {
    contractTypeOpts,
    contractTermOpts,
  },
  fields,
};
