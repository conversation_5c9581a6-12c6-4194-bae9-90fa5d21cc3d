const nodemailer = require('nodemailer');
const { google } = require('googleapis');
const { mail } = require('../../config/vars');
const { birthdayTemplate } = require('../templates/birthday.template');
const { workReportRemindTemplate } = require('../module/work/templates/work.report-remind.template');
const { workReportNotificationTemplate } = require('../module/work/templates/work.report-noti.template');

// Tạo OAuth2 client
const oAuth2Client = new google.auth.OAuth2(
  mail.clientId,
  mail.clientSecret,
  mail.redirectUri,
);

oAuth2Client.setCredentials({ refresh_token: mail.refreshToken });
const createTransport = async () => {
  const accessToken = await oAuth2Client.getAccessToken();
  const transport = {
    service: 'gmail',
    auth: {
      type: 'OAuth2',
      user: mail.senderEmail,
      clientId: mail.clientId,
      clientSecret: mail.clientSecret,
      refreshToken: mail.refreshToken,
      accessToken: accessToken.token ? accessToken.token : '',
    },
  };
  return transport;
};

const forgotPasswordTemplate = ({ email, password }) => ` <!DOCTYPE html>
<html>
<head>

  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title>Password Reset</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style type="text/css">
  /**
   * Google webfonts. Recommended to include the .woff version for cross-client compatibility.
   */
  @media screen {
    @font-face {
      font-family: 'Source Sans Pro';
      font-style: normal;
      font-weight: 400;
      src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v10/ODelI1aHBYDBqgeIAH2zlBM0YzuT7MdOe03otPbuUS0.woff) format('woff');
    }

    @font-face {
      font-family: 'Source Sans Pro';
      font-style: normal;
      font-weight: 700;
      src: local('Source Sans Pro Bold'), local('SourceSansPro-Bold'), url(https://fonts.gstatic.com/s/sourcesanspro/v10/toadOcfmlt9b38dHJxOBGFkQc6VGVFSmCnC_l7QZG60.woff) format('woff');
    }
  }

  /**
   * Avoid browser level font resizing.
   * 1. Windows Mobile
   * 2. iOS / OSX
   */
  body,
  table,
  td,
  a {
    -ms-text-size-adjust: 100%; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
  }

  /**
   * Remove extra space added to tables and cells in Outlook.
   */
  table,
  td {
    mso-table-rspace: 0pt;
    mso-table-lspace: 0pt;
  }

  /**
   * Better fluid images in Internet Explorer.
   */
  img {
    -ms-interpolation-mode: bicubic;
  }

  /**
   * Remove blue links for iOS devices.
   */
  a[x-apple-data-detectors] {
    font-family: inherit !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
    text-decoration: none !important;
  }

  /**
   * Fix centering issues in Android 4.4.
   */
  div[style*="margin: 16px 0;"] {
    margin: 0 !important;
  }

  body {
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /**
   * Collapse table borders to avoid space between cells.
   */
  table {
    border-collapse: collapse !important;
  }

  a {
    color: #34805b;
  }

  img {
    height: auto;
    line-height: 100%;
    text-decoration: none;
    border: 0;
    outline: none;
  }
  </style>

</head>
<body style="background-color: #e9ecef;">

  <!-- start preheader -->
  <div class="preheader" style="display: none; max-width: 0; max-height: 0; overflow: hidden; font-size: 1px; line-height: 1px; color: #fff; opacity: 0;">

  </div>
  <!-- end preheader -->

  <!-- start body -->
  <table border="0" cellpadding="0" cellspacing="0" width="100%">

    <!-- start logo -->
    <tr>
      <td align="center" bgcolor="#e9ecef">
        <!--[if (gte mso 9)|(IE)]>
        <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
        <tr>
        <td align="center" valign="top" width="600">
        <![endif]-->
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">
          <tr>
            <td align="center" valign="top" style="padding:24px;">
              <a href="https://ttu.edu.vn/" target="_blank" style="display: inline-block; text-decoration: none; color: #333;">
                <h1 style="margin: 0; font-size: 32px; font-weight: 700; letter-spacing: -1px; line-height: 1;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif;">
                  <!-- <img width="300" src="http://ttu.edu.vn/wp-content/uploads/2017/09/logo-ttuVN123.png"/> -->
                  Đại Học Tân Tạo
                </h1>
                <!-- <img src="./img/<EMAIL>" alt="Logo" border="0" width="48" style="display: block; width: 48px; max-width: 48px; min-width: 48px;"> -->
              </a>
            </td>
          </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
        </td>
        </tr>
        </table>
        <![endif]-->
      </td>
    </tr>
    <!-- end logo -->

    <!-- start hero -->
    <tr>
      <td align="center" bgcolor="#e9ecef">
        <!--[if (gte mso 9)|(IE)]>
        <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
        <tr>
        <td align="center" valign="top" width="600">
        <![endif]-->
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">
          <tr>
            <td align="center" bgcolor="#fafafa" style="padding: 36px 24px 0; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; border-top: 4px solid #34805b;">
              <img width="100%" src="http://lecturer.ttu.edu.vn/wp-content/uploads/2021/03/image-82-1.png"/>
              <h1 style="margin: 0; font-size: 25px; font-weight: 600; letter-spacing: -1px; line-height: 38px;">XÁC NHẬN QUÊN MẬT KHẨU?</h1>
            </td>
          </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
        </td>
        </tr>
        </table>
        <![endif]-->
      </td>
    </tr>
    <!-- end hero -->

    <!-- start copy block -->
    <tr>
      <td align="center" bgcolor="#e9ecef">
        <!--[if (gte mso 9)|(IE)]>
        <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
        <tr>
        <td align="center" valign="top" width="600">
        <![endif]-->
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">

          <!-- start copy -->
          <tr>
            <td align="left" bgcolor="#fafafa" style="padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">
            <p>
            <strong>Nếu bạn quên mật khẩu hãy bấm vào nút bên dưới để đặt lại mật khẩu, nếu không hãy bỏ qua email này</strong><br />
            </p>
            </td>
          </tr>
          <!-- end copy -->

          <!-- start button -->
          <tr>
            <td align="left" bgcolor="#fafafa">
              <table border="0" cellpadding="0" cellspacing="0" width="100%">
                <tr>
                  <td align="center" bgcolor="#fafafa" style="padding: 12px;">
                    <table border="0" cellpadding="0" cellspacing="0">
                      <tr>
                        <td align="center" bgcolor="#34805b" style="border-radius: 6px;">
                          <a href="https://internal.ttu.edu.vn/forgot-password/${email}/${password}" target="_blank" style="display: inline-block; padding: 16px 36px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px;">Đổi mật khẩu mới</a>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <!-- end button -->

          <!-- start copy -->
          <tr>
            <td align="left" bgcolor="#fafafa" style="padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">
              <p>Trân trọng!</p>
            </td>
          </tr>
          <tr>
          <blockquote><p>&nbsp;</p></blockquote>
<p><img src="http://my.tuyensinh.ttu.edu.vn/html/images/signature-mail-02_02.png" width="64" height="74" /></p>
<p><strong>TAN TAO UNIVERSITY</strong></p>
<p>
    Email: <EMAIL>  | Tel: +84(072) 3769216 | Fax: +84 (*************<br />
    Website :<a href="http://www.ttu.edu.vn" target="_blank" data-saferedirecturl="https://www.google.com/url?hl=vi&amp;q=http://www.ttu.edu.vn&amp;source=gmail&amp;ust=1490752366249000&amp;usg=AFQjCNE4-ed19GFDNOAIVHEYGeDTTzxltw"> <em>www.ttu.edu.vn</em></a><br />
    Tan Tao University Ave, Tan Duc ECity, Duc Hoa, Long An, Vietnam<br />
</p>
<p>
    <a href="https://www.facebook.com/tantaouniversity/?fref=ts" target="new">
        <img src="http://my.tuyensinh.ttu.edu.vn/html/images/signature-mail-02_10.png" width="30" height="29" alt="face" />
    </a>
    <a href="https://www.youtube.com/user/DHTANTAO"><img src="http://my.tuyensinh.ttu.edu.vn/html/images/signature-mail-02_14.png" width="32" height="30" alt="youtube" /></a>
    <a href="https://www.google.com/maps/place/%C4%90%E1%BA%A1i+H%E1%BB%8Dc+T%C3%A2n+T%E1%BA%A1o/@10.792483,106.4397433,17z/data=!3m1!4b1!4m5!3m4!1s0x310ad3f703fc2821:0xd03984a57f051ab8!8m2!3d10.792483!4d106.441932">
        <img src="http://my.tuyensinh.ttu.edu.vn/html/images/Maps-icon.png" width="31" height="30" alt="maps" />
    </a>
    <a href="./ttu.edu.vn"><img src="http://my.tuyensinh.ttu.edu.vn/html/images/website.png" width="30" height="30" alt="web" /></a>
</p>
<br/><hr/><p style='font-style: italic;'>Please read: This email and any files transmitted with it are confidential and intended solely for the use of the individual or entity to whom they are addressed. If you have received this email in error please notify the system manager. Please note that any views or opinions presented in this email are solely those of the author and do not necessarily represent those of Tan Tao University. Finally, the recipient should check this email and any attachments for the presence of viruses. Tan Tao University accepts no liability for any damage caused by any virus transmitted by this email.</p>
          </tr>
          <!-- end copy -->
        </table>
        <!--[if (gte mso 9)|(IE)]>
        </td>
        </tr>
        </table>
        <![endif]-->
      </td>
    </tr>
    <!-- end copy block -->

    <!-- start footer -->
    <tr>
      <td align="center" bgcolor="#e9ecef" style="padding: 24px;">
        <!--[if (gte mso 9)|(IE)]>
        <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
        <tr>
        <td align="center" valign="top" width="600">
        <![endif]-->
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">

          <!-- start permission -->
          <tr>
            <td align="center" bgcolor="#e9ecef" style="padding: 12px 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #666;">
              <p style="margin: 0;">Bạn nhận được email này vì chúng tôi đã nhận được yêu cầu đăng ký cho tài khoản email của bạn, Nếu bạn không yêu cầu đăng ký, có thể xóa email này một cách an toàn.</p>
            </td>
          </tr>
          <!-- end permission -->

          <!-- start unsubscribe -->
          <tr>
            <td align="center" bgcolor="#e9ecef" style="padding: 12px 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #666;">
              <p style="margin: 0;">© 2021 Tan Tao University. All rights reserved</p>
            </td>
          </tr>
          <!-- end unsubscribe -->

        </table>
        <!--[if (gte mso 9)|(IE)]>
        </td>
        </tr>
        </table>
        <![endif]-->
      </td>
    </tr>
    <!-- end footer -->

  </table>
  <!-- end body -->

</body>
</html>`;

exports.forgotPassword = async ({ email, password }) => {
  try {
    const smtpTransport = nodemailer.createTransport(await createTransport());
    const body = forgotPasswordTemplate({ email, password });
    const mailOptions = {
      from: 'TTU no-reply <<EMAIL>>', // sender address
      to: email, // list of receivers
      subject: 'TTU - XÁC NHẬN QUÊN MẬT KHẨU?', // Subject line
      text: '', // plaintext body
      html: body,
    };
    const res = await smtpTransport.sendMail(mailOptions);
    console.log('email sent:', res);
    return res;
  } catch (error) {
    console.log('error', error);
    return error;
  }
};

exports.birthdayMail = async ({
  firstName, lastName, day, month,
}) => {
  const smtpTransport = nodemailer.createTransport(await createTransport());

  const body = birthdayTemplate({
    firstName, lastName, day, month,
  });
  const mailOptions = {
    from: 'TTU no-reply <<EMAIL>>', // sender address
    to: ['<EMAIL>', '<EMAIL>'], // list of receivers
    // to: ['<EMAIL>'], // list of receivers
    subject: 'TTU - Chúc mừng sinh nhật', // Subject line
    text: '', // plaintext body
    html: body,
  };

  const res = await smtpTransport.sendMail(mailOptions);
  return res;
};

exports.reportRemindMail = async ({ email, name_staff, reportId }) => {
  const smtpTransport = nodemailer.createTransport(await createTransport());
  const body = workReportRemindTemplate({
    name_staff, reportId,
  });
  const mailOptions = {
    from: 'TTU no-reply <<EMAIL>>', // sender address
    to: email, // list of receivers
    subject: `TTU - Báo cáo tuần xxxxxx - ${name_staff}`, // Subject line
    text: '', // plaintext body
    html: body,
  };
  const res = await smtpTransport.sendMail(mailOptions);
  return res;
};

exports.reportNotificationMail = async ({ email, name, date }) => {
  const smtpTransport = nodemailer.createTransport(await createTransport());
  const body = workReportNotificationTemplate({
    name, date,
  });
  const mailOptions = {
    from: 'TTU no-reply <<EMAIL>>', // sender address
    to: email, // list of receivers
    subject: 'TTU - Nhắc báo cáo công việc tuần', // Subject line
    text: '', // plaintext body
    html: body,
  };
  const res = await smtpTransport.sendMail(mailOptions);
  return res;
};
