const Joi = require('joi');

module.exports = {

  // GET /v1/article/field
  listAttendances: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      lecturer: Joi.number(),
      _class: Joi.number(),
      semester: Joi.number(),
      year: Joi.number(),
    }),
  },

  // POST /v1/article/field
  createAttendance: {
    body: Joi.object({
      class: Joi.number(),
      note: Joi.string().allow('').optional(),
      date: Joi.date().required(),
      content: Joi.string().allow('').optional(),
    }),
  },

  // PATCH /v1/article/field/:id
  updateAttendance: {
    body: Joi.object({
      note: Joi.string().allow('').optional(),
      content: Joi.string().allow('').optional(),
      date: Joi.date(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/article/field/:id
  deleteAttendance: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },
};
