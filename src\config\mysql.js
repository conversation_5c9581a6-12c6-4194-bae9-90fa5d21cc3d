/* eslint-disable max-len */
const { mysql } = require('./vars');
const { Sequelize } = require('sequelize');

const sequelize = new Sequelize(mysql.db, mysql.user, mysql.password, {
  host: mysql.host,
  dialect: 'mysql',
  operatorsAliases: false,
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
});

const db = {};
db.Sequelize = Sequelize;
db.sequelize = sequelize;

// db.connect = async () => {
//   try {
//     await sequelize.authenticate();
//     console.log('MySQL connection has been established successfully.');
//   } catch (error) {
//     console.error('Unable to connect to the database:', error);
//   }
// };

// db.connect();

// q100 DS tài khoản
db.user = require('../api/module/user/models/user.model').schema(sequelize, Sequelize);

// q150 DS quyền của từng nhóm quyền
db.userPermissionGroupList = require('../api/module/user/models/user.permission.group.list.model').schema(sequelize, Sequelize);

// q175 DS quyền cá nhân
db.userPermission = require('../api/module/user/models/user.permission.model').schema(sequelize, Sequelize);

// q200 DS nhóm quyền của từng nhân viên / giảngviên
db.userPermissionList = require('../api/module/user/models/user.permission.list.model').schema(sequelize, Sequelize);

// q300 "DS giao diện quyền Interfaces"
db.userPermissionInterface = require('../api/module/user/models/user.permission.interface.model').schema(sequelize, Sequelize);

// q350 "DS nhóm quyền Groups"
db.userPermissionGroup = require('../api/module/user/models/user.permission.group.model').schema(sequelize, Sequelize);

// q400 "DS chức năng giao diện quyền Function Interfaces"
db.userPermissionInterfaceFunction = require('../api/module/user/models/user.permission.interface.function.model').schema(sequelize, Sequelize);

// m300 Hợp đồng (contract)
db.contract = require('../api/module/contract/models/contract.model').schema(sequelize, Sequelize);

// m310 Chi tiết chức vụ trong hợp đồng
// db.contractTitle = require('../api/module/contract/models/contract.title.model').schema(sequelize, Sequelize);

// m200 Quyết định bổ nhiệm (Decision)
db.decision = require('../api/module/contract/models/decision.model')(sequelize, Sequelize);

// m100 DS nhân viên / giảng viên
db.employee = require('../api/module/employee/models/employee.model').schema(sequelize, Sequelize);

// m310
db.employeePosition = require('../api/module/employee/models/employee.position.model').schema(sequelize, Sequelize);

// z200
db.employeeFile = require('../api/module/employee/models/employee.file.model').schema(sequelize, Sequelize);

// m500 Nghỉ phép (Leave of adsence)
db.leave = require('../api/module/employee/models/leave.model').schema(sequelize, Sequelize);

// m550 Tăng ca (Overtime)
db.overtime = require('../api/module/employee/models/overtime.model').schema(sequelize, Sequelize);

// m470 chấm công
db.timekeeping = require('../api/module/employee/models/timekeeping.model').schema(sequelize, Sequelize);

// n450 Phòng ban / Khoa (Department / School)
db.department = require('../api/module/department/models/department.model').schema(sequelize, Sequelize);

// n400 Chức vụ (Job Position)
db.position = require('../api/module/department/models/position.model').schema(sequelize, Sequelize);

// t500 DS Lịch công tác
db.workSchedule = require('../api/module/work/models/work.schedule.model').schema(sequelize, Sequelize);

// r300 Phương tiện di chuyển (Transportation)
db.car = require('../api/module/car/models/car.model').schema(sequelize, Sequelize);

// r350 Lịch trình di chuyển (Transport schedule)
db.carSchedule = require('../api/module/car/models/car.schedule.model').schema(sequelize, Sequelize);

// t600 DS Công việc được giao
db.assignment = require('../api/module/assignment/models/assignment.model').schema(sequelize, Sequelize);

// t650 DS Chi tiết công việc cá nhân
db.assignmentContent = require('../api/module/assignment/models/assignment.content.model').schema(sequelize, Sequelize);

// t700 DS Thông báo
db.notification = require('../api/module/notification/models/notification.model').schema(sequelize, Sequelize);

// m450 Ngày công mỗi tháng
db.workday = require('../api/module/employee/models/workday.model').schema(sequelize, Sequelize);

db.incomeAgreement = require('../api/module/contract/models/income.agreement.model').schema(sequelize, Sequelize); // m350

// v950 văn bằng
db.certificate = require('../api/module/student/models/certificate.model').schema(sequelize, Sequelize); // v950

// n500 ngành
db.major = require('../api/module/student/models/major.model').schema(sequelize, Sequelize); // n500

// v400 điểm thành phần
db.componentScores = require('../api/module/student/models/component.score.model').schema(sequelize, Sequelize); // v400

// v450 Thành phần điểm
db.gradeComponents = require('../api/module/student/models/grade.components.model').schema(sequelize, Sequelize); // v450


db.incomeAgreement.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee' });
db.incomeAgreement.belongsTo(db.contract, { foreignKey: 'fm300', as: 'contract' });
// db.employee.hasMany(db.incomeAgreement);

db.student = require('../api/module/student/models/student.model').schema(sequelize, Sequelize); // n100
db.class = require('../api/module/student/models/class.model').schema(sequelize, Sequelize); // b200
db.enroll = require('../api/module/student/models/enroll.model').schema(sequelize, Sequelize); // b300
db.attendance = require('../api/module/student/models/attendance.model').schema(sequelize, Sequelize); // b320
db.absent = require('../api/module/student/models/absent.model').schema(sequelize, Sequelize); // b350

db.enroll.belongsTo(db.student, { foreignKey: 'fn100', as: 'student' });
db.attendance.belongsTo(db.employee, { foreignKey: 'fm100', as: 'lecturer' });
db.attendance.belongsTo(db.class, { foreignKey: 'fb200', as: 'class' });
db.attendance.belongsTo(db.student, { foreignKey: 'fn100', as: 'student' });
db.absent.belongsTo(db.student, { foreignKey: 'fn100', as: 'student' });
db.absent.belongsTo(db.attendance, { foreignKey: 'fb320', as: 'attendance' });
db.absent.belongsTo(db.class, { foreignKey: 'fb200', as: 'class' });

// relationships

db.user.belongsTo(db.employee, { foreignKey: 'fn100', as: 'employee' });
db.employee.hasOne(db.user, { foreignKey: 'fn100' });

// db.userPermissionInterfaceFunction.belongsTo(db.userPermissionInterface, { foreignKey: 'fq300', as: 'permission_interface' });
// db.userPermissionInterface.hasMany(db.userPermissionInterfaceFunction, { foreignKey: 'fq300' });


db.contract.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee' });// ID Nhân viên / giảng viên
db.employee.hasMany(db.contract, { foreignKey: 'fm100' });
db.contract.belongsTo(db.employee, { foreignKey: 'fm100e', as: 'employee_represent' });// ID Nhân viên / giảng viên đại diện tổ chức
db.employee.hasMany(db.contract, { foreignKey: 'fm100e' });
db.contract.belongsTo(db.position, { foreignKey: 'fn400e', as: 'position_represent' });
db.position.hasMany(db.contract, { foreignKey: 'fn400e' });


// fix here

db.employee.belongsTo(db.position, { foreignKey: 'fn400' });
db.employee.belongsTo(db.department, { foreignKey: 'fn450', as: 'department' });
db.department.hasMany(db.employee);

db.employeePosition.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee' });
db.employeePosition.belongsTo(db.position, { foreignKey: 'fn400', as: 'position' });
db.employeePosition.belongsTo(db.department, { foreignKey: 'fn450', as: 'department' });
db.employeePosition.belongsTo(db.contract, { foreignKey: 'fm300', as: 'contract' });

db.decision.belongsTo(db.employee, { foreignKey: 'fm100' });
db.decision.belongsTo(db.department, { foreignKey: 'fn450' });
db.decision.belongsTo(db.position, { foreignKey: 'fn400' });

// db.contractTitle.belongsTo(db.contract, { foreignKey: 'fm300' });
// db.contractTitle.belongsTo(db.department, { foreignKey: 'fn400' });
// db.contractTitle.belongsTo(db.position, { foreignKey: 'fn450' });

db.leave.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee' });
db.employee.hasMany(db.leave);
db.leave.belongsTo(db.department, { foreignKey: 'fn450', as: 'department' });
db.department.hasMany(db.leave);

db.userPermission.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee' });
// db.employee.hasMany(db.userPermission);

db.overtime.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee' });
db.employee.hasMany(db.overtime);

db.timekeeping.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee' });

db.employeeFile.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee' });
db.employee.hasMany(db.employeeFile);

// db.overtime.belongsTo(db.employee, { foreignKey: 'fm100' });

db.userPermissionList.belongsTo(db.userPermissionGroup, { foreignKey: 'fq350' });
db.userPermissionGroup.hasMany(db.userPermissionList, { foreignKey: 'fq350' });
db.userPermissionList.belongsTo(db.employee, { foreignKey: 'fm100' });
db.employee.hasOne(db.userPermissionList, { foreignKey: 'fm100' });

db.car.belongsTo(db.employee, { foreignKey: 'fm100', as: 'driver' });

db.carSchedule.belongsTo(db.employee, { foreignKey: 'fm100', as: 'driver' });
db.carSchedule.belongsTo(db.employee, { foreignKey: 'fm100r', as: 'employee' });
db.carSchedule.belongsTo(db.car, { foreignKey: 'fr300', as: 'car' });

db.assignment.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee' });
db.assignment.belongsTo(db.department, { foreignKey: 'fn450', as: 'department' });

db.assignmentContent.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee_from' });
db.assignmentContent.belongsTo(db.employee, { foreignKey: 'fm100r', as: 'employee_to' });
db.assignmentContent.belongsTo(db.assignment, { foreignKey: 'ft600', as: 'assignment' });

db.notification.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee' });

db.workday.belongsTo(db.employee, { foreignKey: 'fm100', as: 'employee' });

db.certificate.belongsTo(db.department, { foreignKey: 'fn450', as: 'department' });
db.department.hasMany(db.certificate);
db.certificate.belongsTo(db.major, { foreignKey: 'fn500', as: 'major' });
db.major.hasMany(db.certificate);

module.exports = db;

