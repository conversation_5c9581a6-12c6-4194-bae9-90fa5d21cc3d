const fields = {
  table: 'q150', // DS quyền của từng nhóm quyền
  _id: 'pq150', // ID Quyền của từng nhóm quyền
  permission_interface: 'fq300', // ID Giao diện quyền
  permission_group: 'fq350', // ID Nhóm quyền
  permission_interface_function: 'fq400', // ID Chức năng giao diện quyền

  deleted_at: 'ql145', // Thời gian xóa
  created_at: 'ql146', // Thời gian tạo
  created_by: 'ql147', // <PERSON>ail người tạo
  updated_at: 'ql148', // Thời gian cập nhật
  updated_by: 'ql149', // Email người cập nhật
};

const schema = (sequelize, DataTypes) => {
  const userPermissionGroupListSchema = sequelize.define('UserPermissionGroupList', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });
  return userPermissionGroupListSchema;
};

module.exports = {
  schema,
};
