const mongoose = require('mongoose');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

/**
 * ForgotPassword Schema
 * @private
 */
const forgotPasswordSchema = new mongoose.Schema({
  email: {
    type: String,
    match: /^\S+@\S+\.\S+$/,
    trim: true,
    lowercase: true,
  },
  token: {
    type: String,
  },
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
});

/**
 * Methods
 */
forgotPasswordSchema.method({
  transform() {
    const transformed = {};
    const fields = ['_id', 'email', 'token'];

    fields.forEach((field) => {
      transformed[field] = this[field];
    });

    return transformed;
  },
});

/**
 * Statics
 */
forgotPasswordSchema.statics = {
  /**
   * Get ForgotPassword
   *
   * @param {ObjectId} id - The objectId of ForgotPassword.
   * @returns {Promise<Service, APIError>}
   */
  async get(id) {
    try {
      let ForgotPassword;

      if (mongoose.Types.ObjectId.isValid(id)) {
        ForgotPassword = await this.findById(id).exec();
      }
      if (ForgotPassword) {
        return ForgotPassword;
      }

      throw new APIError({
        message: 'ForgotPassword does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  async getByParams({ token, email }) {
    try {
      const forgotPassword = await this.findOne({ token, email });
      if (!forgotPassword) {
        throw new APIError({
          message: 'ForgotPassword does not exist',
          status: httpStatus.NOT_FOUND,
        });
      }
      return forgotPassword;
    } catch (error) {
      throw error;
    }
  },
};


/**
 * @typedef ForgotPassword
 */
module.exports = mongoose.model('ForgotPassword', forgotPasswordSchema);
