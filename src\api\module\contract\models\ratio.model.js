const fields = {
  table: 'm700',
  _id: 'pm700',
  name: 'mv702', // Tên
  code: 'mv702cod', // Mã hiệu
  value: 'mv703', // Gi<PERSON> trị
  start_at: 'md705', // date

  deleted_by: 'ml744', // Email người xóa
  deleted_at: 'ml745', // Thời gian xóa
  created_at: 'ml746', // Thời gian tạo
  created_by: 'ml747', // Email người tạo
  updated_at: 'ml748', // Thời gian cập nhật
  updated_by: 'ml749', // Email người cập nhật
};

const schema = (sequelize, DataTypes) => {
  const ratioSchema = sequelize.define('Ratio', {
    [fields._id]: {
      // ID
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.name]: {
      // Tên
      type: DataTypes.STRING(150),
      allowNull: false,
    },
    [fields.code]: {
      // Tên
      type: DataTypes.STRING(15),
      allowNull: false,
    },
    [fields.value]: {
      // Tên
      type: DataTypes.STRING(150),
      allowNull: false,
    },
    [fields.start_at]: {
      // Tên
      type: DataTypes.DATE(),
      allowNull: false,
    },
    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  return ratioSchema;
};

module.exports = {
  schema,
  fields,
};
