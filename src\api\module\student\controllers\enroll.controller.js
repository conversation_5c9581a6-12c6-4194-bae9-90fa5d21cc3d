// 25/11/2022
const db = require('../../../../config/mysql');
const { success: jsonSuccess } = require('../../../middlewares/success');

const Enroll = db.enroll;
const Student = db.student;

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    query.studentModel = Student;
    const enroll = await Enroll.list({ ...query });

    jsonSuccess(enroll, req, res);
  } catch (error) {
    next(error);
  }
};
