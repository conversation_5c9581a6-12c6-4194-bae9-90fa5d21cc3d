// 23/11/2021
/* eslint-disable max-len */
/* eslint-disable no-await-in-loop */
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const { ExtractJwt } = require('passport-jwt');
// const { getFile } = require('../../../../config/google');

const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');

const ArticleFolder = require('../models/article.folder.model');
const { createFolder, getFile, trashFolder } = require('../../../../config/google');

/**
 * Load folder and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const folder = await ArticleFolder.get(id);
    req.locals = { folder };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get folder
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    const token = ExtractJwt.fromAuthHeaderAsBearerToken()(req);
    if (token === null &&
      // userinfo.role !== 'admin' &&
      (req.locals.folder.status !== 'publish'
        || !req.locals.folder.is_active
        || req.locals.folder.is_deleted)) {
      throw new APIError({
        message: 'ArticleFolder does not exist',
        status: httpStatus.NOT_FOUND,
      });
    }
    const file = await getFile('1lqKip-taE9k0Pto_qd5-47vfDE5SgXBR');
    console.log('>>>>>>>>>>', file);
    jsonSuccess(req.locals.folder, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Create new folder
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user.email;
    const folder = await ArticleFolder.findOne({ title: req.body.title }).exec();
    if (folder && req.body.parent && folder.parent === req.body.parent) {
      throw new APIError({
        message: 'Folder already exists',
        status: httpStatus.BAD_REQUEST,
      });
    } else {
      let drive_folder;
      if (req.body.parent) {
        const parentFolder = await ArticleFolder.get(req.body.parent);
        if (parentFolder.drive_id) {
          drive_folder = await createFolder({ name: req.body.title, parent: parentFolder.drive_id });
        } else {
          throw new APIError({
            message: 'Parent not found (google drive)',
            status: httpStatus.BAD_REQUEST,
          });
        }
      } else {
        drive_folder = await createFolder({ name: req.body.title });
      }
      const articleFolder = new ArticleFolder({ ...req.body, drive_id: drive_folder.id });
      const saved = await articleFolder.save();

      jsonSuccess(saved.transform(), req, res);
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing folder
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user.email;
  const folder = Object.assign(req.locals.folder, req.body);

  folder.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get folder list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await ArticleFolder.count(req.query);
    const folders = await ArticleFolder.list(req.query);
    const transformed = folders.map(folder => folder.transform());
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete folder
 * @public
 */
exports.remove = async (req, res, next) => {
  try {
    const { folder } = req.locals;
    const drive_info = await trashFolder({ fileId: folder.drive_id });
    const result = await folder.remove();
    jsonSuccess({ result, drive_info }, req, res);
  } catch (error) {
    next(error);
  }
};
