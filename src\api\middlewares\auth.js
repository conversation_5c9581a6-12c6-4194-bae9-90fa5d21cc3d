const httpStatus = require('http-status');
const passport = require('passport');
const APIError = require('../utils/APIError');
const { rolesVar } = require('../../config/vars');

const roleOpts = [0, 1, 2, 3, 4, 5, 6, 7, 8];
// const ADMIN = admin_roles.map(element => parseInt(element, 10));
const ADMIN = rolesVar.admin;

const LOGGED_USER = '_loggedUser';
const HR_DEPT = rolesVar.hr_dept;

const handleJWT = (req, res, next, roles) => async (err, user, info) => {
  const error = err || info;
  const logIn = Promise.promisify(req.logIn);
  const apiError = new APIError({
    message: error ? error.message : 'Unauthorized',
    status: httpStatus.UNAUTHORIZED,
    stack: error ? error.stack : undefined,
  });
  try {
    if (error || !user) throw error;
    await logIn(user, { session: false });
  } catch (e) {
    return next(apiError);
  }
  if (roles === LOGGED_USER) {
    if (user.role !== 2 && req.params.userId !== user._id.toString()) {
      apiError.status = httpStatus.FORBIDDEN;
      apiError.message = 'Forbidden';
      return next(apiError);
    }
  } else if (Array.isArray(roles) && !roles.includes(user.role)) {
    apiError.status = httpStatus.FORBIDDEN;
    apiError.message = 'Forbidden';
    return next(apiError);
  } else if (!Array.isArray(roles) && roles !== user.role) {
    apiError.status = httpStatus.FORBIDDEN;
    apiError.message = 'Forbidden';
    return next(apiError);
  } else if (err || !user) {
    return next(apiError);
  }
  req.user = user;
  return next();
};

exports.ADMIN = ADMIN;
exports.LOGGED_USER = LOGGED_USER;
exports.HR_DEPT = HR_DEPT;

exports.authorize = (roles = roleOpts) => (req, res, next) => {
  console.error('HEADERS', req.headers);
  console.error('BODY', req.body);
  passport.authenticate(
    'jwt', { session: false },
    handleJWT(req, res, next, roles),
  )(req, res, next);
};

// exports.oAuth = service =>
//   passport.authenticate(service, { session: false });
