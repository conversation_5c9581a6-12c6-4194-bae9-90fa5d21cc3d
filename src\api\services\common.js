const fs = require('fs');

module.exports = {
  async move(oldPath, newPath) {
    return new Promise((resolve, reject) => {
      function copy() {
        const readStream = fs.createReadStream(oldPath);
        const writeStream = fs.createWriteStream(newPath);

        readStream.on('error', reject);
        writeStream.on('error', reject);

        readStream.on('close', () => {
          fs.unlink(oldPath, resolve);
        });

        readStream.pipe(writeStream);
      }

      fs.rename(oldPath, newPath, (err) => {
        if (err) {
          if (err.code === 'EXDEV') {
            copy();
          } else {
            reject(err);
          }
          return;
        }
        resolve();
      });
    });
  },


};
