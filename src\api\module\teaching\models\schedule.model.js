// const fields = {
//   table: 't100',
//   _id: 'pt100',
//   id_year: 'fh050', // ID Năm học
//   id_semester: 'fh025', // ID <PERSON><PERSON><PERSON> k<PERSON>
//   id_class: 'fb200', // ID Lớp học
//   id_course: 'fb400', // ID Khóa học
//   id_faculty: 'fn450', // ID Khoa
//   is_approve: 'tn101', // Trạng thái xét duyệt
//   approve_at: 'td101', // Ngày xét duyệt
//   is_display: 'tn101p', // Trạng thái hiển thị (giao diện sinh viên)
//   publish_at: 'td101p', // Ngày công bố
//   takes_place: 'tv108', // Thời gian diễn ra
//   day_of_week: 'tn109', // Thứ
//   is_finish: 'tn110', // <PERSON><PERSON><PERSON> d<PERSON>u đ<PERSON> nhập điểm

//   deleted_by: 'tl144', // Email người xóa
//   deleted_at: 'tl145', // Thời gian xóa
//   created_by: 'tl147', // Email người tạo
//   updated_by: 'tl149', // Email người cập nhật
//   updated_at: 'tl148',
//   created_at: 'tl146',
// };

// const schema = (sequelize, DataTypes) => {
//   const scheduleSchema = sequelize.define('Schedule', {

//   });
//   return scheduleSchema;
// };

// module.exports = {
//   schema,
// };
