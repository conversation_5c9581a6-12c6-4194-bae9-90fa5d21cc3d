const Joi = require('joi');

module.exports = {
  // GET /v1/work/report
  listWorkReports: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      calendar: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      employee: Joi.number(),
    }),
  },

  // POST /v1/work/report
  createWorkReport: {
    body: Joi.object({
      calendar: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
      work_this_week: Joi.string().allow('').optional(),
      work_mow: Joi.string().allow('').optional(),
      work_next_week: Joi.string().allow('').optional(),
      problem: Joi.string().allow('').optional(),
      suggestion: Joi.string().allow('').optional(),
    }),
  },

  // PATCH /v1/work/report/:id
  updateWorkReport: {
    body: Joi.object({
      calendar: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      work_this_week: Joi.string().allow('').optional(),
      work_mow: Joi.string().allow('').optional(),
      work_next_week: Joi.string().allow('').optional(),
      problem: Joi.string().allow('').optional(),
      suggestion: Joi.string().allow('').optional(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/work/report/:id
  deleteWorkReport: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

};
