// 12/11/2021
const moment = require('moment');
const { success: jsonSuccess } = require('../../../middlewares/success');
const db = require('../../../../config/mysql');
const Article = require('../../article/models/article.model');


const AssignmentContent = db.assignmentContent;
const Employee = db.employee;
const Assignment = db.assignment;
const Department = db.department;
const Leave = db.leave;
const Car = db.car;
const CarSchedule = db.carSchedule;


exports.dashboard = async (req, res, next) => {
  try {
    const { user } = req;
    const assignmentContents = await AssignmentContent.list({
      employeeModel: Employee,
      assignmentModel: Assignment,
      employee: user._id,
      status: 0,
    });

    const articles = await Article.list({ perPage: 3 });
    const count = await Article.count({ perPage: 3 });

    const currentDate = moment();
    const month = moment(currentDate, 'M').format('MM');
    const employee = await Employee.birthday(month);

    const leave = await Leave.list({
      employeeModel: Employee,
      departmentModel: Department,
      employee: user._id,
      perPage: 3,
    });

    const carSchedules = await CarSchedule.list({
      employeeModel: Employee,
      carModel: Car,
      employee: user._id,
      perPage: 3,
    });

    jsonSuccess({
      assignmentContents,
      articles: {
        total: count,
        data: articles,
      },
      birthday: {
        total: employee.length,
        data: employee,
      },
      leave,
      carSchedules,
    }, req, res);
  } catch (error) {
    next(error);
  }
};
