/* eslint-disable no-await-in-loop */
const db = require('../../../../config/mysql');
const _ = require('lodash');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');
const APIError = require('../../../utils/APIError');
const httpStatus = require('http-status');
const XLSX = require('xlsx');
const path = require('path');

const Contract = db.contract;
const Employee = db.employee;
const Position = db.position;
const Department = db.department;
const { fields } = require('../models/contract.model');
const { contractTemplate } = require('../templates/contract.template');
const { genPdf } = require('../../../../config/html_to_pdf');

/**
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const contract = await Contract.get({ id, employeeModel: Employee, positionModel: Position });
    req.locals = { contract };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get contract
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    const { user } = req;
    const { contract } = req.locals;

    if (![1, 2].includes(user.role) && contract.dataValues.employee.dataValues._id !== user._id) {
      throw new APIError({
        message: 'Contract does not exist',
        status: httpStatus.NOT_FOUND,
      });
    }
    jsonSuccess(contract, req, res);
  } catch (error) {
    next(error);
  }
};

// Create and Save a new Contract
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const { user } = req;
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });
    const contract = Contract.build({
      ...dbData,
      [fields.created_by]: user.email,
    });
    const savedContract = await contract.save();
    jsonSuccess(savedContract, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { user } = req;
    if (![1, 2].includes(user.role)) {
      req.query.employee = user._id;
    }
    const { query } = req;
    const contracts = await Contract.list({ ...query, employeeModel: Employee });
    jsonSuccess(contracts, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await Contract.remove({
      id: req.params.id,
      userId: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await Contract.patch({
      id: req.params.id,
      data: {
        ...req.body,
        updated_by: req.user.email,
      },
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.exportPdf = async (req, res, next) => {
  try {
    const { contract } = req.locals;
    let position_represent = '';
    if (contract.position_represent) {
      position_represent = contract.position_represent.dataValues;
    }
    const pdf = await genPdf(await contractTemplate({
      employee_represent: contract.employee_represent.dataValues,
      employee: contract.employee.dataValues,
      position_represent,
      start_at: contract.dataValues.start_at,
      end_at: contract.dataValues.end_at,
      gross: contract.dataValues.gross,
      gross_text: contract.dataValues.gross_text,
      payment_method: contract.dataValues.payment_method,
      contract_at: contract.dataValues.contract_at,
      term: contract.dataValues.term,
      create_at: contract.dataValues.create_at,
    }));
    jsonSuccess({ pdf }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.importExcel = async (req, res, next) => {
  try {
    const fileName = path.join(__dirname, 'test.xlsx');
    const workbook = XLSX.readFile(fileName);
    const sheet_name_list = workbook.SheetNames;
    const xlData = XLSX.utils.sheet_to_json(workbook.Sheets[sheet_name_list[0]], { raw: false });
    let count = 0;
    for (let i = 0; i < xlData.length; i += 1) {
      const item = xlData[i];
      const employee_name = item.ho_va_ten_a.split(' ');
      const lastSpace = item.ho_va_ten_a.lastIndexOf(' ');
      const employee_first_name = employee_name[employee_name.length - 1];
      const employee_last_name = item.ho_va_ten_a.slice(0, lastSpace);
      let employee_represent_first_name = null;
      let employee_represent_last_name = null;
      if (item.ho_va_ten_b && item.ho_va_ten_b.length > 0) {
        const employee_represent_name = item.ho_va_ten_b.split(' ');
        employee_represent_first_name = employee_represent_name[2];
        employee_represent_last_name = `${employee_represent_name[0]} ${employee_represent_name[1]}`;
      }


      let employee = await Employee.list({
        first_name: employee_first_name,
        last_name: employee_last_name,
        departmentModel: Department,
      });

      let employee_represent = await Employee.list({
        first_name: employee_represent_first_name,
        last_name: employee_represent_last_name,
        departmentModel: Department,
      });
      if (employee_represent.total > 0) {
        employee_represent = employee_represent.data[0].dataValues._id;
      } else {
        employee_represent = null;
      }
      if (employee.total > 0) {
        employee = employee.data[0].dataValues._id;
        let term = item.loai_hop_dong;
        switch (term) {
          case 'Vô thời hạn': {
            term = 0;
            break;
          }
          case 'Có thời hạn': {
            term = 1;
            break;
          }
          case 'Có thời hạn lần 2': {
            term = 2;
            break;
          }
          case 'Có thời hạn lần 3': {
            term = 3;
            break;
          }
          default:
            term = 1;
            break;
        }
        const data = {
          employee,
          employee_represent,
          work_address: item.dia_diem_lam_viec || '',
          other_address: item.dia_diem_lam_viec_khac || '',
          work_time: item.thoi_gian_lam_viec || '',
          requirement: item.trang_thiet_bi_lam_viec || '',
          vehicle: item.phuong_tien_di_lai || '',
          gross: parseInt(item.tong_thu_nhap_hang_thang, 10) || 0,
          payment_method: item.hinh_thuc_tra_luong || '',
          contract_id: item.so_hd || '',
          term,
          contract_at: item.hop_dong_tao_ngay || null,
          signed_at: item.hop_dong_ky_ngay || null,
          start_at: item.ngay_bat_dau || null,
          end_at: item.ngay_ket_thuc || null,
        };

        const dbData = {};
        _.forEach(data, (value, key) => {
          dbData[fields[key]] = value;
        });

        const contract = Contract.build({
          ...dbData,
          [fields.created_by]: req.user.email,
        });
        await contract.save();
        console.log('saved', i + 1, item.stt);
      } else {
        count += 1;
        console.log('<<<<<<<', item.stt, count, employee_last_name, employee_first_name, item.ho_va_ten_a);
      }
    }
    jsonSuccess({ xxxxxxxx: fileName }, req, res);
  } catch (error) {
    next(error);
  }
};
