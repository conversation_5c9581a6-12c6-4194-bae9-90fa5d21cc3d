const { Op } = require('sequelize');
const _ = require('lodash');
const employeeFields = (require('./employee.model')).fields;

const fields = {
  table: 'm550',
  _id: 'pm550', // ID
  employee: 'fm100', // ID Nhân viên / giảng viên
  reason: 'mv552', // Lý do
  overtime_at: 'md553', // Ngày tăng ca
  is_approved: 'mn556', // trạng thái chấp nhận
  type: 'mn554', // Loại (0: <PERSON><PERSON><PERSON> thường, 1: Ng<PERSON><PERSON> nghỉ, 2: <PERSON><PERSON><PERSON> lễ)
  duration: 'mn555', // Số giờ tăng ca

  deleted_by: 'ml544', // Email người xóa
  deleted_at: 'ml545', // Thời gian xóa
  created_by: 'ml547', // Email người tạo
  updated_by: 'ml549', // <PERSON>ail ngườ<PERSON> cậ<PERSON> nhật
  updated_at: 'ml548',
  created_at: 'ml546',
};
const typeOpts = [[0, 1, 2]];

const schema = (sequelize, DataTypes) => {
  const overtimeSchema = sequelize.define('Overtime', {
    [fields._id]: {
      // ID
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.employee]: {
      // ID Nhân viên / giảng viên
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.reason]: {
      // Lý do
      type: DataTypes.TEXT,
      allowNull: false,
    },
    [fields.overtime_at]: {
      // Ngày tăng ca
      type: DataTypes.DATE,
      allowNull: false,
    },
    [fields.type]: {
      // Loại
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.duration]: {
      // Số giờ tăng ca
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.is_approved]: {
      // trạng thái chấp nhận
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Overtime = overtimeSchema;

  overtimeSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    employee = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await Overtime.countItem({
      [fields.deleted_at]: null,
      [fields.employee]: employee,
    });
    const overtime = await Overtime.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.reason, 'reason'],
        [fields.overtime_at, 'overtime_at'],
        [fields.is_approved, 'is_approved'],
        [fields.type, 'type'],
        [fields.duration, 'duration'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.employee]: employee,
      },
      include: {
        model: employeeModel,
        as: 'employee',
        attributes: [
          [employeeFields._id, '_id'],
          [employeeFields.first_name, 'first_name'],
          [employeeFields.last_name, 'last_name'],
        ],
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: overtime };
  };

  overtimeSchema.countItem = async (query) => {
    const count = await Overtime.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  overtimeSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Overtime.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  return overtimeSchema;
};

module.exports = {
  schema,
  fields,
  typeOpts,
  opts: {
    typeOpts,
  },
};
