const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/attendance.controller');
const { authorize } = require('../../../../middlewares/auth');
const {
  listAttendances,
  createAttendance,
  updateAttendance,
} = require('../../validations/attendance.validation');

const router = express.Router();

/**
 * Load post when API with id route parameter is hit
 */
// router.param('id', controller.load);


router
  .route('/')
  /**
   * @api {get} v1/post-category List Categories
   * @apiDescription Get list
   * @apiVersion 1.0.0
   * @apiName ListPostCategories
   * @apiGroup Post Category
   *
   * @apiParam  {Number{1-}}         [page=1]     List page
   * @apiParam  {Number{1-100}}      [perPage=1]  Per page
   * @apiParam  {String}             [title]      Title
   *
   * @apiSuccess {Object[]} categories List of post categories.
   */
  // .get(validate(listArticleFields), controller.list)
  .get(authorize(), validate(listAttendances), controller.list)
  /**
   * @api {post} v1/post-category Create Category
   * @apiDescription Create a new category
   * @apiVersion 1.0.0
   * @apiName CreatePost
   * @apiGroup Post Category
   * @apiPermission admin
   *
   * @apiHeader {String} Athorization  User's access token
   *
   * @apiParam  {String}     title      title
   * @apiParam  {String}     info       info
   * @apiParam  {Number}      order      order
   *
   * @apiSuccess (Created 201) {String}  id         id
   * @apiSuccess (Created 201) {String}  title      title
   * @apiSuccess (Created 201) {String}  info       info
   * @apiSuccess (Created 201) {Number}  order      order
   * @apiSuccess (Created 201) {Date}    createdAt  Timestamp
   *
   * @apiError (Bad Request 400)   ValidationError  Some parameters may contain invalid values
   * @apiError (Unauthorized 401)  Unauthorized     Only authenticated users can create the data
   * @apiError (Forbidden 403)     Forbidden        Only admins can create the data
   */
  .post(authorize(), validate(createAttendance), controller.create);

router
  .route('/report')
  .get(authorize(), controller.report);

router
  .route('/report-no-attendance')
  .get(authorize(), controller.reportNoAttendance);

router
  .route('/report-preview')
  .get(authorize(), controller.reportPreview);

router
  .route('/:id')
  .get(authorize(), controller.get)
  .patch(authorize(), validate(updateAttendance), controller.update)
  .delete(authorize(), controller.remove);


module.exports = router;
