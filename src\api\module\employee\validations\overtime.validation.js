const Joi = require('joi');
const { opts } = require('../models/overtime.model');

module.exports = {

  // GET /v1/employee/overtime
  listOvertimes: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
      employee: Joi.number().integer(),
    }),
  },

  // POST /v1/employee/overtime
  createOvertime: {
    body: Joi.object({
      employee: Joi.number().integer().required(),
      reason: Joi.string().required(),
      overtime_at: Joi.date().required(),
      type: Joi.number().integer(),
      duration: Joi.number().required(),
    }),
  },

  // PATCH /v1/employee/overtime/:id
  updateOvertime: {
    body: Joi.object({
      employee: Joi.number().integer(),
      reason: Joi.string(),
      overtime_at: Joi.date(),
      type: Joi.number().integer(),
      duration: Joi.number(),
      is_approved: Joi.number().valid(...opts.typeOpts[0]),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/employee/overtime/:id
  deleteOvertime: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
