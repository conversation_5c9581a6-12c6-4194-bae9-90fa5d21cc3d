// 27/11/2023
const db = require('../../../../config/mysql');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');
const multer = require('multer');
const fs = require('fs');
const { uploadDir } = require('../../../../config/vars');
const APIError = require('../../../utils/APIError');
const httpStatus = require('http-status');
const {
  createEmployeeFile,
} = require('../validations/employee.file.validation');

const Employee = db.employee;
const EmployeeFile = db.employeeFile;

exports.load = async (req, res, next, id) => {
  try {
    const file = await EmployeeFile.get({ id, employeeModel: Employee });
    req.locals = { file };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.get = async (req, res, next) => {
  try {
    const { file } = req.locals;
    jsonSuccess(file, req, res);
  } catch (error) {
    next(error);
  }
};

exports.create = async (req, res, next) => {
  const moveFile = (sourcePath, destinationPath) => {
    fs.rename(sourcePath, destinationPath, (err) => {
      if (err) {
        console.error('Lỗi khi di chuyển tệp tin:', err);
      } else {
        console.log('Đã di chuyển tệp tin thành công!');
      }
    });
  };
  const dir = uploadDir.employee_file;
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
  }
  const fileStorage = multer.diskStorage({
    destination: (rq, file, cb) => {
      cb(null, uploadDir.employee_file);
    },
    filename: (rq, file, cb) => {
      const datetimestamp = Date.now();
      cb(null, `${file.fieldname}-${datetimestamp}.${file.originalname.split('.')[file.originalname.split('.').length - 1]}`);
    },
  });
  const fileFilter = (rq, file, cb) => {
    if (
      file.mimetype === 'image/png' ||
      file.mimetype === 'image/jpg' ||
      file.mimetype === 'image/jpeg' ||
      file.mimetype === 'application/pdf'
    ) {
      cb(null, true);
    } else {
      cb(new Error('File format should be PNG, JPG, JPEG, PDF'), false); // if validation failed then generate error
    }
  };
  const upload = multer({
    storage: fileStorage,
    fileFilter,
  }).single('file');
  upload(req, res, async (err) => {
    const { body, file, user } = req;

    const validateError = createEmployeeFile.validate(body).error;
    try {
      if (err) {
        throw new APIError({
          message: err.message,
          status: httpStatus.BAD_REQUEST,
        });
      }
      if (validateError) {
        // Trả về lỗi nếu dữ liệu không hợp lệ
        throw new APIError({
          message: validateError.details[0].message,
          status: httpStatus.BAD_REQUEST,
        });
      }

      // if (err) {
      //   res.json({
      //     error_code: 1,
      //     err_desc: err.message,
      //   });
      // } else {
      const employeeDir = `${uploadDir.employee_file}/${body.employee}`;
      if (!fs.existsSync(employeeDir)) {
        fs.mkdirSync(employeeDir);
      }
      const directory = `${employeeDir}/${file.filename}`;
      moveFile(`${file.destination}/${file.filename}`, directory);
      const data = {
        name: body.name,
        directory: directory.slice(directory.indexOf('employee_file')),
        created_by: user.email,
        employee: parseInt(body.employee, 10),
      };
      const employeeFile = await EmployeeFile.create(data);
      jsonSuccess(employeeFile, req, res);
      // }
    } catch (error) {
      next(error);
    }
  });
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    // query.employee = req.user._id;
    const file = await EmployeeFile.list({
      ...query,
      employeeModel: Employee,
    });

    jsonSuccess(file, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await EmployeeFile.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    await EmployeeFile.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};
