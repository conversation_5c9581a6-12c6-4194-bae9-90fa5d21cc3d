/* eslint-disable no-restricted-syntax */
const ArticleDepartment = require('../models/article.department.model');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

/**
 * Load article department and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const articleDepartment = await ArticleDepartment.get(id);
    req.locals = { articleDepartment };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get article department
 * @public
 */
exports.get = (req, res) => {
  jsonSuccess(req.locals.articleDepartment.transform(), req, res);
};

/**
 * Create new article department
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user._id;
    const category = await ArticleDepartment.findOne({ title: req.body.title }).exec();
    if (category) {
      throw new APIError({
        message: 'ArticleDepartment already exists',
        status: httpStatus.BAD_REQUEST,
      });
    } else {
      const articleDepartment = new ArticleDepartment(req.body);
      const saved = await articleDepartment.save();
      jsonSuccess(saved.transform(), req, res);
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing article department
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const articleDepartment = Object.assign(req.locals.articleDepartment, req.body);

  articleDepartment.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get article department list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await ArticleDepartment.count(req.query);
    const types = await ArticleDepartment.list(req.query);
    const transformed = types.map(type => type.transform());

    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete article department
 * @public
 */
exports.remove = (req, res, next) => {
  const { articleDepartment } = req.locals;

  articleDepartment.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};
