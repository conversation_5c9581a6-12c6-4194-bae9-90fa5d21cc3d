const Joi = require('joi');
const { opts } = require('../models/leave.model');

module.exports = {

  // GET /v1/employee/leave
  listLeaves: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string().valid(...['asc', 'desc']),
      is_approved: Joi.number().valid(...opts.approvedOpts[0]).messages({
        'any.only': '0: <PERSON><PERSON><PERSON>, 1: <PERSON><PERSON>, 2: <PERSON><PERSON><PERSON> sự tự làm',
      }),
      employee: Joi.number(),
    }),
  },

  // POST /v1/employee/leave
  createLeave: {
    body: Joi.object({
      reason: Joi.string().required(),
      start_at: Joi.date().required(),
      end_at: Joi.date().required(),
    }),
  },

  // PATCH /v1/employee/leave/:id
  updateLeave: {
    body: Joi.object({
      reason: Joi.string(),
      start_at: Joi.date(),
      end_at: Joi.date(),
      is_approved: Joi.number().valid(...opts.approvedOpts[0]).messages({
        'any.only': '0: Chưa duyệt, 1: Đã duyệt, 2: Nhân sự tự làm',
      }),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/employee/leave/:id
  deleteLeave: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // POST /v1/employee/leave/manage
  createLeaveManage: {
    body: Joi.object({
      reason: Joi.string().required(),
      start_at: Joi.date().required(),
      end_at: Joi.date().required(),
      employees: Joi.array().items(Joi.number()),
      is_all: Joi.boolean(),
      is_approved: Joi.number().valid(...opts.approvedOpts[0]).messages({
        'any.only': '0: Chưa duyệt, 1: Đã duyệt, 2: Nhân sự tự làm',
      }),
    }),
  },

};
