// 13/01/2021
const _ = require('lodash');
const moment = require('moment');
const { Op } = require('sequelize');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const departmentFields = (require('../../department/models/department.model')).fields;
const positionFields = require('../../department/models/position.model').fields;


const employeeIsForeignOpts = [[0, 1]]; // 1 là người ngoài nước
const employeeIsLecturerOpts = [[null, 0, 1]]; // (0: Employee, 1: Lecturer)
const employeeWorkStatusOpts = [[0, 1]]; // (0: Working, 1: Leaving)
const employeeTitleOpts = [[0, 1, 2, 3, 4, 5, 6, 7, 8]];// (0: Không, 1: <PERSON><PERSON><PERSON><PERSON>, 2: <PERSON><PERSON> gi<PERSON><PERSON>)
const isSocialInsuranceOpts = [[0, 1]];// (0: <PERSON><PERSON> tham gia, 1: Không còn/muốn tham gia)
const contractTypeOpts = [[0, 1, 2, 3]];// (0: Có tham gia, 1: Không còn/muốn tham gia)
const defaultSort = 'pm100';
const fields = {
  table: 'm100',
  _id: 'pm100', // ID Nhân viên / giảng viên
  timekeeping_code: 'mv101', // Mã nhân viên
  department: 'fn450', // ID Khoa / phòng ban x
  is_foreign: 'mn101p', // Đánh dấu người nước ngoài
  is_lecturer: 'mn101', // Phân biệt Nhân viên và Giảng viên Type (0: Employee, 1: Lecturer)
  last_name: 'mv102', // Họ x
  first_name: 'mv103', // Tên x
  dob: 'md104', // Ngày sinh x
  sex: 'mv105', // Giới tính x
  phone: 'mv106', // Số điện thoại x
  email: 'mv107', // Email (use to login) x
  email_personal: 'mv133', // Email cá nhân
  identification_number: 'mv108', // Số chứng minh nhân dân x
  identification_date: 'md123', // Ngày cấp CMND x
  identification_place: 'mv124', // Nơi cấp CMND x
  work_start_at: 'md109', // Ngày bắt đầu làm việc x
  work_end_at: 'md109e', // Ngày nghỉ việc
  address: 'mv110', // Địa chỉ thường trú x
  address_current: 'mv122', // Địa chỉ hiện tại (địa chỉ liên lạc) x
  nationality: 'mv111', // Quốc tịch x
  race: 'mv112', // Dân tộc x
  pob: 'mv113', // Nơi sinh
  marital_status: 'mv114', // Tình trạng hôn nhân
  work_status: 'mn115', // Trạng thái làm việc
  experience: 'mv116', // Tóm tắt nghề nghiệp
  education: 'mv117', // Tóm tắt trình độ
  avatar: 'mv121', // Địa chỉ avartar
  leave_balance_days: 'mn126', // Tổng số ngày phép có trong năm (ngày phép cơ sở + thâm niên)
  bank_account: 'mv139', // Số tài khoản ngân hàng
  bank_name: 'mv143', // Tên ngân hàng
  bank_branch: 'mv144', // Chi nhánh
  title: 'mn132', // Chuc danh khoa hoc x
  social_number: 'mv129', // Số bhxh
  tax_number: 'mv134', // Mã số thuế
  dependent_number: 'mn125', // Số người phụ thuộc
  academic_level: 'mn135', // Trình độ học vấn cao nhất
  school: 'mv137', // Nơi đào tạo
  major: 'mv138', // Chuyên ngành đào tạo
  is_social_insurance: 'mn145', // tham gia bảo hiểm xã hội
  contract_type: 'mn118', // Loại hợp đồng
  is_special: 'mn144', // Tài khoản đặc biệt
  is_both_lecturer_employee: 'mn120', // Is both employee and lecturer (1: Yes)
  deleted_by: 'ml144', // Email người xóa
  deleted_at: 'ml145', // Thời gian xóa
  created_by: 'ml147', // Email người tạo
  updated_by: 'ml149', // Email người cập nhật
  updated_at: 'ml148',
  created_at: 'ml146',
};
const schema = (sequelize, DataTypes) => {
  const employeeSchema = sequelize.define('Employee', {
    // Model attributes are defined here
    [fields._id]: {
      // ID Nhân viên / giảng viên
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.timekeeping_code]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.is_foreign]: {
      // Đánh dấu người nước ngoài
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: employeeIsForeignOpts,
      },
    },
    [fields.is_lecturer]: {
      // Phân biệt Nhân viên và Giảng viên
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: employeeIsLecturerOpts,
      },
    },
    [fields.first_name]: {
      // Tên
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.last_name]: {
      // Họ
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.dob]: {
      // Ngày sinh
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.sex]: {
      // Giới tính
      type: DataTypes.STRING(1),
      defaultValue: 'M',
    },
    [fields.phone]: {
      // Số điện thoại
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.email]: {
      // Email (use to login)
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.identification_number]: {
      // Số chứng minh nhân dân
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.work_start_at]: {
      // Ngày bắt đầu làm việc
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.work_end_at]: {
      // Ngày nghỉ việc
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.address]: {
      // Địa chỉ thường trú
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.nationality]: {
      // Quốc tịch
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.race]: {
      // Dân tộc
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.pob]: {
      // Nơi sinh
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.marital_status]: {
      // Tình trạng hôn nhân
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.work_status]: {
      // Trạng thái làm việc
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: employeeWorkStatusOpts,
      },
    },
    [fields.experience]: {
      // Tóm tắt nghề nghiệp
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.education]: {
      // Tóm tắt trình độ
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.leave_balance_days]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.is_social_insurance]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: isSocialInsuranceOpts,
      },
    },
    [fields.contract_type]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: contractTypeOpts,
      },
    },
    [fields.dependent_number]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.is_both_lecturer_employee]: {
      // Đánh dấu vừa là nhân viên vừa là giảng viên
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.avatar]: {
      // Địa chỉ avartar
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.identification_date]: {
      // Ngày cấp CMND
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.identification_place]: {
      // Nơi cấp CMND
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.address_current]: {
      // Địa chỉ hiện tại
      type: DataTypes.STRING(512),
      defaultValue: null,
    },

    [fields.is_special]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    // md126: {
    //   // Ngày thử việc
    //   type: DataTypes.DATEONLY,
    //   defaultValue: null,
    // },
    // mv127: {
    //   // Mức lương thử việc
    //   type: DataTypes.DOUBLE,
    //   defaultValue: 0,
    // },
    // mv128: {
    //   // Mức lương cơ sở
    //   type: DataTypes.DOUBLE,
    //   defaultValue: 0,
    // },
    [fields.social_number]: {
      // Số bhxh
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    mv130: {
      // Số bhyt
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    mv131: {
      // Nơi đăng ký khám chửa bệnh
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.email_personal]: {
      // Email cá nhân
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.tax_number]: {
      // Mã số thuế
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    // mv135: {
    //   // Số hộ chiếu
    //   type: DataTypes.STRING(32),
    //   defaultValue: null,
    // },
    mv136: {
      // Số thị thực
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.school]: {
      // Nơi đào tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.major]: {
      // Chuyên ngành đào tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.bank_account]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.bank_name]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.bank_branch]: {
      // Chi nhánh
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    // md145: {
    //   // Ngày hộ chiếu hết hiệu lực
    //   type: DataTypes.DATEONLY,
    //   defaultValue: null,
    // },
    // md146: {
    //   // Ngày thị thực bắt đầu có hiệu lực
    //   type: DataTypes.DATEONLY,
    //   defaultValue: null,
    // },
    // md146e: {
    //   // Ngày thị thực kết thúc hiệu lực
    //   type: DataTypes.DATEONLY,
    //   defaultValue: null,
    // },
    // [fields.temporary_residence_end]: {
    //   // Ngày thẻ tạm trú hết hiệu lực
    //   type: DataTypes.DATEONLY,
    //   defaultValue: null,
    // },
    // [fields.work_permit_start]: {
    //   // Ngày giấy phép lao động bắt đầu có hiệu lực
    //   type: DataTypes.DATEONLY,
    //   defaultValue: null,
    // },
    // [fields.work_permit_end]: {
    //   // Ngày giấy phép lao động kết thúc hiệu lực
    //   type: DataTypes.DATEONLY,
    //   defaultValue: null,
    // },
    [fields.title]: {
      // Chuc danh khoa hoc
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        isIn: employeeTitleOpts,
      },
    },
    [fields.academic_level]: {
      // Trình độ học vấn cao nhất
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Employee = employeeSchema;

  employeeSchema.findById = async (id) => {
    const employee = await Employee.findOne({
      attributes: [
        [fields._id, '_id'],
        [fields.first_name, 'first_name'],
        [fields.last_name, 'last_name'],
        [fields.email, 'email'],
        [fields.department, 'department'],
      ],
      where: {
        [fields._id]: id,
      },
    });
    return employee;
  };

  employeeSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by,
    order_way = 'desc',
    deleted_at = null,
    is_lecturer = { [Op.not]: null },
    is_both_lecturer_employee = { [Op.not]: null },
    work_status = {
      [Op.or]: [
        { [Op.not]: null },
        { [Op.is]: null },
      ],
    },
    sex = { [Op.not]: null },
    dob,
    first_name = { [Op.not]: null },
    last_name = { [Op.not]: null },
    name,
    email,
    departmentModel,
    dept = { [Op.not]: null },
    work_start_at = {
      [Op.or]: [
        { [Op.not]: null },
        { [Op.is]: null },
      ],
    },
    timekeeping_code = {
      [Op.or]: [
        { [Op.not]: null },
        { [Op.is]: null },
      ],
    },
    is_special = 0,
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    if (deleted_at === 'not_null') {
      deleted_at = { [Op.not]: null };
    }
    if (work_start_at === 'not_null') {
      work_start_at = { [Op.not]: null };
    }
    if (is_lecturer === 'all') {
      is_lecturer = { [Op.not]: null };
    }
    if (work_status === 'all') {
      work_status = { [Op.not]: null };
    }
    if (sex === 'all') {
      sex = { [Op.not]: null };
    }
    if (!dob || dob === 'all') {
      dob = { [Op.not]: null };
    } else if (dob) {
      dob = { [Op.gte]: dob };
    }
    if (!order_by || order_by === 'default') {
      order_by = defaultSort;
    } else {
      order_by = fields[order_by];
    }
    if (order_way === 'default') {
      order_way = 'desc';
    }
    if (!email || email === 'all') {
      email = { [Op.not]: null };
    } else {
      email = { [Op.substring]: email };
    }
    // if (!last_name || last_name === 'all') {
    //   last_name = { [Op.not]: null };
    // } else {
    //   last_name = { [Op.substring]: last_name };
    // }
    // if (!first_name || first_name === 'all') {
    //   first_name = { [Op.not]: null };
    // } else {
    //   first_name = { [Op.substring]: first_name };
    if (!name || name === 'all') {
      name = { [Op.not]: null };
    } else {
      name = { [Op.in]: name.split(' ') };
    }
    // }
    const where = {
      deleted_at,
      is_lecturer,
      is_both_lecturer_employee,
      work_status,
      sex,
      dob,
      first_name,
      last_name,
      name,
      email,
      departmentModel,
      dept,
      work_start_at,
      is_special,
      timekeeping_code,
    };
    const count = await Employee.countItem({
      [fields.deleted_at]: deleted_at,
      [fields.is_lecturer]: is_lecturer,
      [fields.work_status]: work_status,
      [fields.sex]: sex,
      // [fields.dob]: dob,
      [fields.email]: email,
      [fields.first_name]: first_name,
      [fields.last_name]: last_name,
      [fields.department]: dept,
      [fields.work_start_at]: work_start_at,
      [fields.is_special]: is_special,
      [fields.is_both_lecturer_employee]: is_both_lecturer_employee,
      [Op.or]: {
        [fields.first_name]: name,
        [fields.last_name]: name,
      },
    });
    const employees = await Employee.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.timekeeping_code, 'timekeeping_code'],
        [fields.is_foreign, 'is_foreign'],
        [fields.is_lecturer, 'is_lecturer'],
        [fields.last_name, 'last_name'],
        [fields.first_name, 'first_name'],
        [fields.dob, 'dob'],
        [fields.sex, 'sex'],
        [fields.email, 'email'],
        [fields.email_personal, 'email_personal'],
        [fields.work_status, 'work_status'],
        [fields.work_start_at, 'work_start_at'],
        [fields.work_end_at, 'work_end_at'],
        [fields.phone, 'phone'],
        [fields.email, 'email'],
        [fields.avatar, 'avatar'],
        [fields.leave_balance_days, 'leave_balance_days'],
        [fields.bank_account, 'bank_account'],
        [fields.bank_name, 'bank_name'],
        [fields.is_social_insurance, 'is_social_insurance'],
        [fields.contract_type, 'contract_type'],
        [fields.is_special, 'is_special'],
        [fields.is_both_lecturer_employee, 'is_both_lecturer_employee'],
      ],
      where: {
        [fields.deleted_at]: where.deleted_at,
        [fields.is_lecturer]: where.is_lecturer,
        [fields.is_both_lecturer_employee]: where.is_both_lecturer_employee,
        [fields.work_status]: where.work_status,
        [fields.sex]: where.sex,
        // [fields.dob]: dob,

        [fields.email]: where.email,
        [fields.first_name]: where.first_name,
        [fields.last_name]: where.last_name,
        [fields.department]: where.dept,
        [fields.work_start_at]: where.work_start_at,
        [fields.is_special]: where.is_special,
        [fields.timekeeping_code]: where.timekeeping_code,

        [Op.or]: {
          [fields.first_name]: name,
          [fields.last_name]: name,
        },
      },
      include: [
        {
          model: departmentModel,
          as: 'department',
          attributes: [
            [departmentFields._id, '_id'],
            [departmentFields.name, 'name'],
            [departmentFields.name_vn, 'name_vn'],
            [departmentFields.code, 'code'],
            [departmentFields.type, 'type'],
          ],
        },
      ],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: employees };
  };

  employeeSchema.countItem = async (query) => {
    const count = await Employee.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  employeeSchema.get = async ({ id, departmentModel }) => {
    try {
      const employee = await Employee.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.is_foreign, 'is_foreign'],
          [fields.is_lecturer, 'is_lecturer'],
          [fields.last_name, 'last_name'],
          [fields.first_name, 'first_name'],
          [fields.dob, 'dob'],
          [fields.sex, 'sex'],
          [fields.phone, 'phone'],
          [fields.email, 'email'],
          [fields.identification_number, 'identification_number'],
          [fields.identification_date, 'identification_date'],
          [fields.identification_place, 'identification_place'],
          [fields.work_start_at, 'work_start_at'],
          [fields.work_end_at, 'work_end_at'],
          [fields.address, 'address'],
          [fields.address_current, 'address_current'],
          [fields.nationality, 'nationality'],
          [fields.race, 'race'],
          [fields.pob, 'pob'],
          [fields.marital_status, 'marital_status'],
          [fields.work_status, 'work_status'],
          [fields.experience, 'experience'],
          // [fields.education, 'education'],
          [fields.avatar, 'avatar'],
          [fields.leave_balance_days, 'leave_balance_days'],
          [fields.bank_account, 'bank_account'],
          [fields.bank_name, 'bank_name'],
          [fields.bank_branch, 'bank_branch'],
          [fields.email_personal, 'email_personal'],
          [fields.is_both_lecturer_employee, 'is_both_lecturer_employee'],
          [fields.identification_date, 'identification_date'],
          [fields.identification_place, 'identification_place'],
          [fields.address_current, 'address_current'],
          [fields.social_number, 'social_number'],
          ['mv130', 'mv130'],
          ['mv131', 'mv131'],
          [fields.tax_number, 'tax_number'],
          ['mv136', 'mv136'],
          [fields.school, 'school'],
          [fields.major, 'major'],
          [fields.academic_level, 'academic_level'],
          [fields.title, 'title'],
          [fields.dependent_number, 'dependent_number'],
          [fields.is_social_insurance, 'is_social_insurance'],
          [fields.contract_type, 'contract_type'],


          [fields.deleted_by, 'deleted_by'],
          [fields.deleted_at, 'deleted_at'],
          [fields.created_at, 'created_at'],
          [fields.created_by, 'created_by'],
          [fields.updated_at, 'updated_at'],
          [fields.updated_by, 'updated_by'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
        include: [
          {
            model: departmentModel,
            as: 'department',
            attributes: [
              [departmentFields._id, '_id'],
              [departmentFields.name, 'name'],
              [departmentFields.name_vn, 'name_vn'],
              [departmentFields.code, 'code'],
              [departmentFields.type, 'type'],
            ],
          },
        ],
      });
      if (employee) {
        return employee;
      }
      throw new APIError({
        message: 'Employee does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  employeeSchema.profile = async ({
    id, departmentModel, positionModel, employeePositionModel, employeePositionFields,
  }) => {
    try {
      const employee = await Employee.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.is_foreign, 'is_foreign'],
          [fields.is_lecturer, 'is_lecturer'],
          [fields.last_name, 'last_name'],
          [fields.first_name, 'first_name'],
          [fields.dob, 'dob'],
          [fields.sex, 'sex'],
          [fields.phone, 'phone'],
          [fields.email, 'email'],
          [fields.identification_number, 'identification_number'],
          [fields.work_start_at, 'work_start_at'],
          [fields.work_end_at, 'work_end_at'],
          [fields.address, 'address'],
          [fields.nationality, 'nationality'],
          [fields.race, 'race'],
          [fields.pob, 'pob'],
          [fields.marital_status, 'marital_status'],
          [fields.work_status, 'work_status'],
          [fields.experience, 'experience'],
          [fields.education, 'education'],
          [fields.avatar, 'avatar'],
          // [fields.temporary_residence_end, 'temporary_residence_end'],
          // [fields.work_permit_start, 'work_permit_start'],
          [fields.is_social_insurance, 'is_social_insurance'],
          [fields.leave_balance_days, 'leave_balance_days'],
          [fields.bank_account, 'bank_account'],
          [fields.bank_name, 'bank_name'],
          [fields.deleted_by, 'deleted_by'],
          [fields.deleted_at, 'deleted_at'],
          [fields.created_at, 'created_at'],
          [fields.created_by, 'created_by'],
          [fields.updated_at, 'updated_at'],
          [fields.updated_by, 'updated_by'],
        ],
        where: {
          [fields._id]: id,
          // [fields.deleted_at]: null,
        },
        include: [
          {
            model: departmentModel,
            as: 'department',
            attributes: [
              [departmentFields._id, '_id'],
              [departmentFields.name, 'name'],
              [departmentFields.name_vn, 'name_vn'],
              [departmentFields.code, 'code'],
              [departmentFields.type, 'type'],
            ],
          },
        ],
      });
      const employeePosition = await employeePositionModel.findAll({
        attributes: [
          [employeePositionFields._id, '_id'],
          [employeePositionFields.created_by, 'created_by'],
        ],
        where: {
          [employeePositionFields.deleted_at]: null,
          [employeePositionFields.employee]: id,
        },
        include: [{
          model: positionModel,
          as: 'position',
          attributes: [
            [positionFields._id, '_id'],
            [positionFields.name, 'name'],
          ],
        },
        {
          model: departmentModel,
          as: 'department',
          attributes: [
            [departmentFields._id, '_id'],
            [departmentFields.name, 'name'],
            [departmentFields.name_vn, 'name_vn'],
            [departmentFields.code, 'code'],
            [departmentFields.type, 'type'],
          ],
        }],
      });
      if (employee) {
        return { employee, employeePosition };
      }
      throw new APIError({
        message: 'Employee does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  employeeSchema.birthday = async ({ month, departmentModel }) => {
    try {
      const employees = await Employee.findAll({
        attributes: [
          [fields._id, '_id'],
          [fields.last_name, 'last_name'],
          [fields.first_name, 'first_name'],
          [fields.dob, 'dob'],
          [fields.sex, 'sex'],
          [fields.phone, 'phone'],
          [fields.email, 'email'],
          [fields.avatar, 'avatar'],
        ],
        where: {
          [fields.dob]: { [Op.substring]: `-${month}-` },
          [fields.deleted_at]: null,
          [fields.work_status]: 0,
        },
        include: [
          {
            model: departmentModel,
            as: 'department',
            attributes: [
              [departmentFields._id, '_id'],
              [departmentFields.name, 'name'],
              [departmentFields.name_vn, 'name_vn'],
              [departmentFields.code, 'code'],
              [departmentFields.type, 'type'],
            ],
          },
        ],
        order: [
          [sequelize.fn('DAY', sequelize.col(fields.dob)), 'ASC'],
        ],
      });
      return employees;
    } catch (error) {
      throw error;
    }
  };

  employeeSchema.birthdayToday = async ({ date }) => {
    try {
      const employees = await Employee.findAll({
        attributes: [
          [fields._id, '_id'],
          [fields.last_name, 'last_name'],
          [fields.first_name, 'first_name'],
          [fields.dob, 'dob'],
          [fields.sex, 'sex'],
          [fields.phone, 'phone'],
          [fields.email, 'email'],
          [fields.avatar, 'avatar'],
        ],
        where: {
          [fields.dob]: { [Op.endsWith]: `-${date}` },
          [fields.deleted_at]: null,
          [fields.work_status]: 0,
        },
        order: [
          [sequelize.fn('DAY', sequelize.col(fields.dob)), 'ASC'],
        ],
      });
      return employees;
    } catch (error) {
      throw error;
    }
  };

  employeeSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Employee.update({
        ...dbData,
        [fields.updated_at]: moment().format(),
      }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  employeeSchema.remove = async ({ id, email }) => {
    try {
      const result = await Employee.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  // thêm ngày phép
  employeeSchema.addDays = async ({
    employee = { [Op.not]: null }, // id nhân viên cần thêm
    number = 1, // số ngày cộng thêm
  }) => {
    try {
      const updated = await Employee.increment(`${fields.leave_balance_days}`, {
        by: number,
        where: {
          [fields._id]: employee,
          [fields.deleted_at]: null,
          [fields.work_status]: 0,
        },
      });
      return updated;
    } catch (error) {
      throw error;
    }
  };

  // reset ngày phép
  employeeSchema.resetDay = async () => {
    try {
      const updated = await Employee.update({
        [fields.leave_balance_days]: 0,
      }, {
        where: {
          [fields._id]: { [Op.not]: null },
        },
      });
      return updated;
    } catch (error) {
      throw error;
    }
  };

  employeeSchema.tmp = async ({
    page = 1,
    perPage = -1,
    order_by = defaultSort,
    order_way = 'desc',
    deleted_at = null,
    // is_lecturer = 1,
    work_status = 0,
    is_special = 0,
    departmentModel,
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const where = {
      [fields.deleted_at]: deleted_at,
      // [fields.is_lecturer]: is_lecturer,
      [fields.work_status]: work_status,
      [fields.is_special]: is_special,
    };
    const employees = await Employee.findAll({
      attributes: [
        [fields.last_name, 'last_name'],
        [fields.first_name, 'first_name'],
        [fields.email, 'email'],
      ],
      where: {
        ...where,
      },
      include: [
        {
          model: departmentModel,
          as: 'department',
          attributes: [
            [departmentFields.name_vn, 'name_vn'],
          ],
        },
      ],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { employees };
  };

  return employeeSchema;
};


module.exports = {
  schema,
  fields,
  opts: {
    employeeIsForeignOpts,
    employeeIsLecturerOpts,
    employeeWorkStatusOpts,
    employeeTitleOpts,
    isSocialInsuranceOpts,
    contractTypeOpts,
  },
};
