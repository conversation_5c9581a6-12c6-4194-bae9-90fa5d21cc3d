const html_to_pdf = require('html-pdf-node');
const fs = require('fs');
const path = require('path');
const { uploadDir } = require('../config/vars');

const footer = `<footer style="
padding: 10px;
text-align: center;
color: #fff;
background-color: #24793b;
width:100%;
height:auto;
font-size:8px;
margin:-15px;
-webkit-print-color-adjust: exact;
">
Tan Tao University Avenue Tan Duc E. City, Duc Hoa District, Long An Province<br />
Đại lộ Đại học Tân Tạo, Tân Đức E.City, Huyện Đức Hòa, Tỉnh Long An<br />
Tel: (+84-0272) 376 9216  -  Email: <EMAIL><br />
</footer>`;
const options = {
  format: 'A4',
  printBackground: true,
  footerTemplate: footer,
  displayHeaderFooter: true,
  args: ['--disable-setuid-sandbox', '--no-sandbox'],
};
const dir = path.join(process.cwd(), uploadDir.pdf);
if (!fs.existsSync(dir)) {
  fs.mkdirSync(dir);
}

exports.genPdf = async (htmlTemplate) => {
  const file = { content: htmlTemplate };
  const now = Date.now();
  const fileName = path.join(uploadDir.pdf, `HD-${now}.pdf`);

  await html_to_pdf.generatePdf(file, options).then((pdfBuffer) => {
    fs.writeFileSync(fileName, pdfBuffer);
    console.log('PDF Buffer:-', pdfBuffer);
  });
  return `pdf/HD-${now}.pdf`;
};
