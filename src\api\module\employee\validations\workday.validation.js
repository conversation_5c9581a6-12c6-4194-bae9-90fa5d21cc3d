// 5/1/2022
const Joi = require('joi');

module.exports = {

  // GET /v1/employee/workday
  listWorkdays: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
      employee: Joi.number().integer(),
      start_date: Joi.date(),
    }),
  },

  // POST /v1/employee/workday
  createWorkday: {
    body: Joi.object({
      employee: Joi.number().integer().required(),
      start_date: Joi.date().required(),
      month_workday: Joi.number().required(),
    }),
  },

  // PATCH /v1/employee/workday/:id
  updateWorkday: {
    body: Joi.object({
      total_workday: Joi.number().integer(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/employee/workday/:id
  deleteWorkday: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // POST /v1/employee/workday/auto-calculate
  autoCalculate: {
    body: Joi.object({
      now: Joi.date(),
    }),
  },

};
