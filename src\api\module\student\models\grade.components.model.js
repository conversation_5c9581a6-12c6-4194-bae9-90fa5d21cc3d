// 25/11/2024

const _ = require('lodash');
const moment = require('moment');
const httpStatus = require('http-status');
const { Op } = require('sequelize');
const APIError = require('../../../utils/APIError');

const fields = {
  table: 'v450',
  _id: 'pv450',
  year: 'fh050', // Năm học
  semester: 'fh025', // Họ<PERSON>ỳ
  class: 'fb200', // lớp
  course: 'fh750', // Môn học
  name_eng: 'vv452', // Tên cột điểm (tiếng Anh)
  name_vie: 'vv452_vn', // Tên cột điểm (tiếng Việt)
  weight: 'vn453', // Trọng số (theo phần trăm)
  description: 'vl438', // mô tả
  note: 'vl439', // ghi chú

  deleted_by: 'vl444',
  deleted_at: 'vl445',
  updated_by: 'vl449',
  updated_at: 'vl448',
  created_by: 'vl447',
  created_at: 'vl446',
};

const schema = (sequelize, DataTypes) => {
  const gradeComponentSchema = sequelize.define(
    'GradeComponent',
    {
      [fields._id]: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      [fields.year]: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      [fields.semester]: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      [fields.class]: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      [fields.course]: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      [fields.name_eng]: {
        type: DataTypes.STRING(150),
        allowNull: false,
      },
      [fields.name_vie]: {
        type: DataTypes.STRING(300),
        defaultValue: null,
      },
      [fields.weight]: {
        type: DataTypes.DOUBLE,
        defaultValue: 0,
      },
      [fields.description]: {
        type: DataTypes.TEXT,
        defaultValue: null,
      },
      [fields.note]: {
        type: DataTypes.STRING(512),
        defaultValue: null,
      },

      [fields.deleted_by]: {
        type: DataTypes.STRING(128),
        defaultValue: null,
      },
      [fields.deleted_at]: {
        type: DataTypes.DATEONLY,
        defaultValue: null,
      },
      [fields.created_by]: {
        type: DataTypes.STRING(128),
        defaultValue: null,
      },
      [fields.updated_by]: {
        type: DataTypes.STRING(128),
        defaultValue: null,
      },
    },
    {
      tableName: fields.table,
      createdAt: fields.created_at,
      updatedAt: fields.updated_at,
    },
  );

  const GradeComponent = gradeComponentSchema;

  gradeComponentSchema.get = async ({ id }) => {
    try {
      const gradeComponent = await GradeComponent.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.year, 'year'],
          [fields.semester, 'semester'],
          [fields.class, 'class'],
          [fields.course, 'course'],
          [fields.name_eng, 'name_eng'],
          [fields.name_vie, 'name_vie'],
          [fields.weight, 'weight'],
          [fields.description, 'description'],
          [fields.note, 'note'],
          [fields.created_by, 'created_by'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      if (gradeComponent) {
        return gradeComponent;
      }
      throw new APIError({
        message: 'GradeComponent does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  gradeComponentSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    semester = { [Op.not]: null },
    _class = { [Op.not]: null },
    year = { [Op.not]: null },
    course = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await GradeComponent.countItem({
      [fields.deleted_at]: null,
      [fields.semester]: semester,
      [fields.class]: _class,
      [fields.year]: year,
      [fields.course]: course,
    });
    const gradeComponents = await GradeComponent.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.year, 'year'],
        [fields.semester, 'semester'],
        [fields.class, 'class'],
        [fields.course, 'course'],
        [fields.name_eng, 'name_eng'],
        [fields.name_vie, 'name_vie'],
        [fields.weight, 'weight'],
        [fields.description, 'description'],
        [fields.note, 'note'],
        [fields.created_by, 'created_by'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.semester]: semester,
        [fields.class]: _class,
        [fields.year]: year,
        [fields.course]: course,
      },
      ...pagination,
      order: [[order_by, order_way]],
    });
    return { total: count, data: gradeComponents };
  };

  gradeComponentSchema.countItem = async (query) => {
    const count = await GradeComponent.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  gradeComponentSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await GradeComponent.update(
        { ...dbData },
        {
          where: {
            [fields._id]: id,
          },
        },
      );
    } catch (error) {
      throw error;
    }
  };

  gradeComponentSchema.remove = async ({ id, email }) => {
    try {
      const result = await GradeComponent.update(
        {
          [fields.deleted_at]: moment().format(),
          [fields.deleted_by]: email,
        },
        {
          where: {
            [fields._id]: id,
            [fields.deleted_at]: null,
          },
        },
      );
      return result;
    } catch (error) {
      throw error;
    }
  };

  return gradeComponentSchema;
};

module.exports = {
  schema,
  fields,
};
