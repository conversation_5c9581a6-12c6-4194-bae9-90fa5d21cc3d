/* eslint-disable max-len */
// const mongoose = require('mongoose');
// const httpStatus = require('http-status');
// const { omitBy, isNil } = require('lodash');
// const APIError = require('../../../utils/APIError');

// const demandOpts = ['', 'replacement', 'urgency', 'plan'];
// /**
//  * recruitment_request Schema
//  * @private
//  */
// const recruitmentRequestSchema = new mongoose.Schema({
//   department: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'Department',
//   },
//   position: {
//     type: String,
//     trim: true,
//   },
//   year: {
//     type: Number,
//   },
//   demand: {
//     type: String,
//     enum: demandOpts,
//     default: '',
//   },
//   workforces: [{
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'Workforce',
//   }],
//   is_active: {
//     type: Boolean,
//     default: true,
//   },
//   created_by: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'User',
//   },
//   updated_by: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'User',
//   },
// }, {
//   timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
// });

// /**
//  * Methods
//  */
// recruitmentRequestSchema.method({
//   transform() {
//     const transformed = {};
//     const fields = [
//       '_id',
//       'department',
//       'demand',
//       'position',
//       'is_plan',
//       'year',
//       'is_replacement',
//       'is_urgency',
//       'workforces',
//       'is_active',
//       'created_at',
//       'created_by',
//     ];
//     fields.forEach((field) => {
//       transformed[field] = this[field];
//     });

//     return transformed;
//   },
// });

// /**
//  * Statics
//  */
// recruitmentRequestSchema.statics = {
//   demandOpts,
//   /**
//    * Get recruitment_request
//    *
//    * @param {ObjectId} id - The objectId of recruitment_request.
//    * @returns {Promise<RecruitmentRequest, APIError>}
//    */
//   async get(id) {
//     try {
//       let recruitment_request;

//       if (mongoose.Types.ObjectId.isValid(id)) {
//         recruitment_request = await this.findById(id)
//           .populate('department', '_id name')
//           .populate({
//             path: 'workforces',
//             select: '_id quantity position report salary_range sex deadline reason job_description age education note',
//             populate: {
//               path: 'job_description',
//               select: '_id department position',
//               populate: {
//                 path: 'department',
//                 select: '_id name',
//               },
//             },
//           })
//           .populate('created_by', '_id name email')
//           .exec();
//       }
//       if (recruitment_request) {
//         return recruitment_request;
//       }

//       throw new APIError({
//         message: 'RecruitmentRequest does not exist',
//         status: httpStatus.NOT_FOUND,
//       });
//     } catch (error) {
//       throw error;
//     }
//   },

//   /**
//    * List recruitment_request in descending order of 'createdAt' timestamp.
//    *
//    * @param {number} skip - Number of recruitment_request to be skipped.
//    * @param {number} limit - Limit number of recruitment_request to be returned.
//    * @returns {Promise<User[]>}
//    */
//   list({
//     page = 1,
//     perPage = 30,
//     sort,
//     year,
//     // department,
//     is_plan,
//     is_replacement,
//     is_urgency,
//     requester,
//     is_active = true,
//   }) {
//     page = parseInt(page, 10);
//     perPage = parseInt(perPage, 10);
//     const options = omitBy({
//       // department: new RegExp(department, 'i'),
//       year,
//       is_plan,
//       is_replacement,
//       is_urgency,
//       requester,
//       is_active,
//     }, isNil);
//     const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
//     const result = this.find(options)
//       .populate('department', '_id name')
//       .populate('created_by', '_id name email')
//       .populate({
//         path: 'workforces',
//         select: '_id quantity position report salary_range sex deadline reason job_description',
//         populate: {
//           path: 'job_description',
//           select: '_id department position',
//           populate: {
//             path: 'department',
//             select: '_id name',
//           },
//         },
//       })
//       .sort(sortOpts);
//     if (perPage > -1) {
//       result.skip(perPage * (page - 1)).limit(perPage);
//     }
//     return result.exec();
//   },

//   /**
//    * Count recruitment_request.
//    * @returns {Promise<Number>}
//    */
//   async count({
//     year,
//     // department,
//     // location,
//     is_active,
//   }) {
//     const options = omitBy({
//       // location: new RegExp(location, 'i'),
//       // department: new RegExp(department, 'i'),
//       year,
//       is_active,
//     }, isNil);
//     return this.find(options).count();
//   },
// };

// /**
//  * @typedef RecruitmentRequest
//  */
// module.exports = mongoose.model('RecruitmentRequest', recruitmentRequestSchema);
