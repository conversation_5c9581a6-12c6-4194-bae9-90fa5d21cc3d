// 28/11/2022
const { Op } = require('sequelize');
const studentFields = (require('./student.model')).fields;

const fields = {
  table: 'b300',
  _id: 'pb300',
  class: 'fb200',
  student: 'fn100',

  deleted_by: 'bl344',
  deleted_at: 'bl345',
  created_by: 'bl347',
  updated_by: 'bl349',
  updated_at: 'bl348',
  created_at: 'bl346',
};
const schema = (sequelize, DataTypes) => {
  const enrollSchema = sequelize.define('Enroll', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.name]: {
      type: DataTypes.STRING(150),
      allowNull: false,
    },
    [fields.name_vn]: {
      type: DataTypes.STRING(300),
      defaultValue: null,
    },
    [fields.code]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Enroll = enrollSchema;

  enrollSchema.list = async ({
    page = 1,
    perPage = 30,
    // order_by = fields._id,
    order_way = 'asc',
    _class,
    studentModel,
    student = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await Enroll.countItem({
      [fields.class]: _class,
      [fields.student]: student,
      [fields.deleted_at]: null,
    });
    const enroll = await Enroll.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.class, 'class'],
      ],
      where: {
        [fields.class]: _class,
        [fields.student]: student,
        [fields.deleted_at]: null,
      },
      include: [
        {
          model: studentModel,
          as: 'student',
          attributes: [
            [studentFields._id, '_id'],
            [studentFields.first_name, 'first_name'],
            [studentFields.last_name, 'last_name'],
            [studentFields.student_id, 'student_id'],
            [studentFields.email, 'email'],
            [studentFields.avatar, 'avatar'],
          ],
        },
      ],
      ...pagination,
      order: [
        [{ model: studentModel, as: 'student' }, studentFields.first_name, order_way],
      ],
    });
    return { total: count, data: enroll };
  };

  enrollSchema.countItem = async (query) => {
    const count = await Enroll.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  return enrollSchema;
};

module.exports = {
  schema,
  fields,
};
