const mongoose = require('mongoose');
const httpStatus = require('http-status');
const Slug = require('slug');
const { omitBy, isNil } = require('lodash');
const APIError = require('../../../utils/APIError');

/**
 * ArticleNumber Schema
 * @private
 */
const articleNumber = new mongoose.Schema({
  number: {
    type: String,
    default: '',
    trim: true,
  },
  type: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ArticleType',
  },
  created_by: {
    type: String,
    default: '',
  },
  updated_by: {
    type: String,
    default: '',
  },
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
});

/**
 * Add your
 * - pre-save hooks
 * - validations
 * - virtuals
 */
articleNumber.pre('save', async function save(next) {
  try {
    /*
    slug
    */
    if (this.isModified('title')) {
      this.slug = Slug(this.title).toLowerCase();
    }
    next();
  } catch (error) {
    next(error);
  }
});

/**
 * Methods
 */
articleNumber.method({
  transform() {
    const transformed = {};
    const fields = [
      '_id',
      'number',
      'type',
      'created_at',
      'created_by',
    ];

    fields.forEach((field) => {
      transformed[field] = this[field];
    });

    return transformed;
  },
});

/**
 * Statics
 */
articleNumber.statics = {
  /**
   * Get ArticleNumber
   *
   * @param {ObjectId} id - The objectId of ArticleNumber.
   * @returns {Promise<Post, APIError>}
   */
  async get(id) {
    try {
      let ArticleNumber;

      if (mongoose.Types.ObjectId.isValid(id)) {
        ArticleNumber = await this.findById(id)
          .exec();
      }
      if (ArticleNumber) {
        return ArticleNumber;
      }

      throw new APIError({
        message: 'ArticleNumber does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * List categories in descending order of 'createdAt' timestamp.
   *
   * @param {number} skip - Number of categories to be skipped.
   * @param {number} limit - Limit number of categories to be returned.
   * @returns {Promise<User[]>}
   */
  list({
    page = 1, perPage = 30, sort, type, number,
  }) {
    perPage = parseInt(perPage, 10);
    page = parseInt(page, 10);
    const options = omitBy({ type, number }, isNil);
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options)
      .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  },

  /**
   * Count radios.
   * @returns {Promise<Number>}
   */
  async count({
    type, number,
  }) {
    const options = omitBy({ type, number }, isNil);
    return this.find(options).count();
  },
};

/**
 * @typedef ArticleNumber
 */
module.exports = mongoose.model('ArticleNumber', articleNumber);
