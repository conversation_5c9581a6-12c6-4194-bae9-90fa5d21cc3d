const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/paycheck.controller');
const { authorize } = require('../../../../middlewares/auth');
const {
  list,
} = require('../../validations/paycheck.validation');

const router = express.Router();

router
  .route('/')
  /**
   * @api {get} v1/article/article List Posts
   * @apiDescription Get list
   * @apiVersion 1.0.0
   * @apiName ListPosts
   * @apiGroup Post
   * @apiPermission admin
   *
   * @apiHeader {String} Athorization  User's access token
   *
   * @apiParam  {Number{1-}}         [page=1]     List page
   * @apiParam  {Number{1-100}}      [perPage=1]  Per page
   * @apiParam  {String}             [title]      Title
   * @apiParam  {String}             [category]   category
   * @apiParam  {String}             [status]     status
   * @apiParam  {String}             [sort]       sort ex:{"id": -1}
   *
   * @apiSuccess {Number} total Total posts.
   * @apiSuccess {Object[]} docs List of posts.
   */
  .get(authorize(), validate(list), controller.list);

module.exports = router;
