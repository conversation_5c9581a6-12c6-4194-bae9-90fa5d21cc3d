const Joi = require('joi');

module.exports = {

  // GET /v1/article/field
  listAttendancePermissions: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      title: Joi.string(),
      sort: Joi.string(),
    }),
  },

  // POST /v1/article/field
  createAttendancePermission: {
    body: Joi.object({
      class: Joi.number(),
      student: Joi.string().email().required(),
    }),
  },

  // PATCH /v1/article/field/:id
  updateAttendancePermission: {
    body: Joi.object({
      title: Joi.string(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/article/field/:id
  deleteAttendancePermission: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },
};
