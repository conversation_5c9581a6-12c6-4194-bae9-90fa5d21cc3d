// 28/1/2022
const Jo<PERSON> = require('joi');

module.exports = {

  // GET /v1/employee/position
  listEmployeePositions: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
      employee: Joi.number().integer(),
      contract: Joi.number().integer(),
      department: Joi.number().integer(),
      position: Joi.number().integer(),
    }),
  },

  // POST /v1/employee/position
  createEmployeePosition: {
    body: Joi.object({
      employee: Joi.number().integer().required(),
      contract: Joi.number().integer(),
      department: Joi.number().integer().required(),
      position: Joi.number().integer().required(),
    }),
  },

  // PATCH /v1/employee/position/:id
  updateEmployeePosition: {
    body: Joi.object({
      employee: Joi.number().integer(),
      contract: Joi.number().integer(),
      department: Joi.number().integer(),
      position: Joi.number().integer(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/employee/position/:id
  deleteEmployeePosition: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
