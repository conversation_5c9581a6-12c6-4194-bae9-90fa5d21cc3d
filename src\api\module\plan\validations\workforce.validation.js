const Joi = require('joi');
const Workforce = require('../models/workforce.model');

module.exports = {

  // GET /v1/plan/workforce
  listWorkforces: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      deadline: Joi.date(),
      sort: Joi.string(),
    }),
  },

  // POST /v1/plan/workforce
  createWorkforce: {
    body: Joi.object({
      position: Joi.string().required(),
      quantity: Joi.number(),
      report: Joi.string(),
      salary_range: Joi.number(),
      sex: Joi.string().valid(...Workforce.sexOpts),
      deadline: Joi.date(),
      reason: Joi.string().allow(''),
      job_description: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      age: Joi.string().allow(''),
      education: Joi.string().allow(''),
      note: Joi.string().allow(''),
    }),
  },

  // PATCH /v1/plan/workforce/:id
  updateWorkforce: {
    body: Joi.object({
      position: Joi.string(),
      quantity: Joi.number(),
      report: Joi.string(),
      salary_range: Joi.number(),
      sex: Joi.string().valid(...Workforce.sexOpts),
      deadline: Joi.date(),
      reason: Joi.string(),
      job_description: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      age: Joi.string().allow(''),
      education: Joi.string().allow(''),
      note: Joi.string().allow(''),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1/plan/workforce/:id
  deleteWorkforce: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

};
