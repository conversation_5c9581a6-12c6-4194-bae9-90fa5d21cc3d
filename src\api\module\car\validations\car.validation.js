const Joi = require('joi');
// const { opts } = require('../models/overtime.model');

module.exports = {

  // GET /v1/car/car
  listCars: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
      borrowed_at: Joi.date(),
      returned_at: Joi.date().greater(Joi.ref('borrowed_at')),
    }),
  },

  // POST /v1/car/car
  createCar: {
    body: Joi.object({
      driver: Joi.number().integer().required(),
      name: Joi.string().required(),
      license_plate: Joi.string().required(),
      seat: Joi.number().integer().required(),
      code: Joi.string(),
      description: Joi.string(),
    }),
  },

  // PATCH /v1/car/car/:id
  updateCar: {
    body: Joi.object({
      driver: Joi.number().integer(),
      name: Joi.string(),
      license_plate: Joi.string(),
      seat: Joi.number().integer(),
      code: Joi.string(),
      description: Joi.string(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/car/car/:id
  deleteCar: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
