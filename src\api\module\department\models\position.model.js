// 17/12/2021
const _ = require('lodash');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

const fields = {
  table: 'n400',
  _id: 'pn400',
  code: 'nv401', // <PERSON>ã hiệu (gốc, hiển thị cho người dùng)
  is_approved: 'nn401', // Trạng thái xét duyệt
  approved_at: 'nd401', // Ngày xét duyệt
  name: 'nv402', // Tên
  description: 'nl438', // <PERSON><PERSON> tả
  name_vn: 'nv402_vn', // Tên tiếng V<PERSON>

  deleted_by: 'nl444', // Email người xóa
  deleted_at: 'nl445', // Thời gian xóa
  created_by: 'nl447', // Email người tạo
  updated_by: 'nl449', // Email ngườ<PERSON> cập nhật
  updated_at: 'nl448',
  created_at: 'nl446',
};

const schema = (sequelize, DataTypes) => {
  const positionSchema = sequelize.define('Position', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.is_approved]: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    [fields.approved_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.code]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.name]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },

    [fields.name_vn]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Position = positionSchema;

  positionSchema.countItem = async (query) => {
    const count = await Position.count({
      where: { ...query },
    });
    return count;
  };

  positionSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }

    const count = await Position.countItem({
      [fields.deleted_at]: null,
      [fields.is_approved]: 1,
    });

    const position = await Position.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.code, 'code'],
        [fields.approved_at, 'approved_at'],
        [fields.name, 'name'],
        [fields.name_vn, 'name_vn'],
        [fields.is_approved, 'is_approved'],
        [fields.created_by, 'created_by'],
        [fields.created_at, 'created_at'],
      ],
      where: {
        [fields.deleted_at]: null,
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });

    return { total: count, data: position };
  };

  positionSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Position.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  positionSchema.remove = async ({ id, employee }) => {
    try {
      const leave = await Position.destroy({
        where: {
          [fields._id]: id,
          [fields.is_approved]: 0,
          [fields.employee]: employee,
        },
      });
      return leave;
    } catch (error) {
      throw error;
    }
  };

  positionSchema.get = async (id) => {
    try {
      const notification = await Position.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.code, 'code'],
          [fields.approved_at, 'approved_at'],
          [fields.name, 'name'],
          [fields.name_vn, 'name_vn'],
          [fields.is_approved, 'is_approved'],
          [fields.created_by, 'created_by'],
          [fields.created_at, 'created_at'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      if (notification) {
        return notification;
      }
      throw new APIError({
        message: 'Notification does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  return positionSchema;
};

module.exports = {
  schema,
  fields,
};
