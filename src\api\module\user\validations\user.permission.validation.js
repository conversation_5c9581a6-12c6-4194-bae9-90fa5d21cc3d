const Joi = require('joi');

module.exports = {

  // GET /v1/user/permission
  listPermissions: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      employee: Joi.number(),
    }),
  },

  // POST /v1/user/permission
  createPermission: {
    body: Joi.object({
      permission_ui_function_name: Joi.string().required(),
      permission_ui_function_code: Joi.string().required(),
      employee: Joi.number().required(),
    }),
  },

  // PATCH /v1/user/permission/:id
  updatePermission: {
    body: Joi.object({
      employee: Joi.number().required(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/user/permission/:id
  deletePermission: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // GET /v1/user/permission/allow-post-document
  listPermissionsArticle: {
    query: Joi.object({
      employee: Joi.number().required(),
    }),
  },
};

