/* eslint-disable no-tabs */

const translate = require('translate-google');
const moment = require('moment');

// bỏ dấu
const removeAccents = (str) => {
  let newStr = str;
  const AccentsMap = [
    'aàảãáạăằẳẵắặâầẩẫấậ',
    'AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬ',
    'dđ', 'DĐ',
    'eèẻẽéẹêềểễếệ',
    'EÈẺẼÉẸÊỀỂỄẾỆ',
    'iìỉĩíị',
    'IÌỈĨÍỊ',
    'oòỏõóọôồổỗốộơờởỡớợ',
    'OÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢ',
    'uùủũúụưừửữứự',
    'UÙỦŨÚỤƯỪỬỮỨỰ',
    'yỳỷỹýỵ',
    'YỲỶỸÝỴ',
  ];
  for (let i = 0; i < AccentsMap.length; i += 1) {
    const item = AccentsMap[i];
    const re = new RegExp(`[${item.substr(1)}]`, 'g');
    const char = AccentsMap[i][0];
    newStr = newStr.replace(re, char);
  }
  return newStr;
};

const trans = async (text) => {
  if (text) {
    const newText = await translate(text, { to: 'en' });
    return newText;
  }
  return '';
};

const fetchData = ({ data, field, lang }) => {
  if (data === null || data === '') {
    return '';
  }
  switch (field) {
    case 'sex': {
      if (lang === 'vi') {
        if (data === 'F') return 'Bà';
        return 'Ông';
      }
      if (data === 'F') return 'Ms.';
      return 'Mr.';
    }

    case 'text': {
      if (lang === 'en') {
        return removeAccents(data);
      }

      return data;
    }

    case 'phone': {
      return `${data.slice(0, 3)} ${data.slice(3, 6)} ${data.slice(6)}`;
    }

    case 'date': {
      return moment(data).format('DD/MM/YYYY');
    }

    case 'fullDateEn': {
      return moment(data).format('LL');
    }

    case 'fullDateVi': {
      return moment(data).format('[ngày] DD [tháng] MM [năm] YYYY');
    }

    case 'money': {
      return data.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }

    case 'term': {
      const term = ['Vô thời hạn', 'Có thời hạn', 'Có thời hạn lần 2', 'Có thời hạn lần 3'];
      return term[data];
    }

    default:
      return '';
  }
};

exports.contractTemplate = async ({
  employee_represent,
  employee,
  position_represent,
  start_at,
  end_at,
  gross,
  gross_text,
  payment_method,
  contract_at,
  term,
  create_at,
}) => `<!DOCTYPE html>
<html lang="vi">

<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width,initial-scale=1" />
	<meta name="description" content="" />
	<style>
		html {
			font-family: sans-serif;
			-ms-text-size-adjust: 100%;
			-webkit-text-size-adjust: 100%;
			-webkit-box-sizing: border-box;
			-moz-box-sizing: border-box;
			box-sizing: border-box;
		}
		*, *:before, *:after {
			-webkit-box-sizing: inherit;
			-moz-box-sizing: inherit;
			box-sizing: inherit;
		}

		body {
			margin: 0;
			font-family: 'Times New Roman', Times, serif;
			min-height: 100vh;
		}

		article,
		aside,
		details,
		figcaption,
		figure,
		footer,
		header,
		hgroup,
		main,
		nav,
		section,
		summary {
			display: block;
		}

		audio,
		canvas,
		progress,
		video {
			display: inline-block;
			vertical-align: baseline;
		}

		audio:not([controls]) {
			display: none;
			height: 0;
		}

		[hidden],
		template {
			display: none;
		}

		a {
			background: transparent;
		}

		a:active,
		a:hover {
			outline: 0;
		}

		abbr[title] {
			border-bottom: 1px dotted;
		}

		b,
		strong {
			font-weight: bold;
		}

		dfn {
			font-style: italic;
		}

		h1 {
			font-size: 2em;
			margin: 0.67em 0;
		}

		mark {
			background: #ff0;
			color: #000;
		}

		small {
			font-size: 80%;
		}

		sub,
		sup {
			font-size: 75%;
			line-height: 0;
			position: relative;
			vertical-align: baseline;
		}

		sup {
			top: -0.5em;
		}

		sub {
			bottom: -0.25em;
		}

		img {
			border: 0;
		}

		svg:not(:root) {
			overflow: hidden;
		}

		figure {
			margin: 1em 40px;
		}

		hr {
			-moz-box-sizing: content-box;
			box-sizing: content-box;
			height: 0;
		}

		pre {
			overflow: auto;
		}

		code,
		kbd,
		pre,
		samp {
			font-family: monospace, monospace;
			font-size: 1em;
		}

		button,
		input,
		optgroup,
		select,
		textarea {
			color: inherit;
			font: inherit;
			margin: 0;
		}

		button {
			overflow: visible;
		}

		button,
		select {
			text-transform: none;
		}

		button,
		html input[type="button"],
		input[type="reset"],
		input[type="submit"] {
			-webkit-appearance: button;
			cursor: pointer;
		}

		button[disabled],
		html input[disabled] {
			cursor: default;
		}

		button::-moz-focus-inner,
		input::-moz-focus-inner {
			border: 0;
			padding: 0;
		}

		input {
			line-height: normal;
		}

		input[type="checkbox"],
		input[type="radio"] {
			box-sizing: border-box;
			padding: 0;
		}

		input[type="number"]::-webkit-inner-spin-button,
		input[type="number"]::-webkit-outer-spin-button {
			height: auto;
		}

		input[type="search"] {
			-webkit-appearance: textfield;
			-moz-box-sizing: content-box;
			-webkit-box-sizing: content-box;
			box-sizing: content-box;
		}

		input[type="search"]::-webkit-search-cancel-button,
		input[type="search"]::-webkit-search-decoration {
			-webkit-appearance: none;
		}

		fieldset {
			border: 1px solid #c0c0c0;
			margin: 0 2px;
			padding: 0.35em 0.625em 0.75em;
		}

		legend {
			border: 0;
			padding: 0;
		}

		textarea {
			overflow: auto;
		}

		optgroup {
			font-weight: bold;
		}

		table {
			border-collapse: collapse;
			border-spacing: 0;
		}

		td,
		th {
			padding: 0;
		}
		/* tandatx style */
		.container{
			max-width: 794px;
			margin: auto;
		}
		header{
			padding: 20px 40px;
			background-color: #24793b;
			font-family: Arial, Helvetica, sans-serif
		}
		header .container{
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
		header .brand-logo-text{
			width: 50%;
			text-align: left;
		}
		header .brand-logo-text h1{
			font-size: 1.5rem;
			color: #fff;
		}
		header .brand-logo-text h1 span{
			display: block;
			font-size: 1.15rem;
			font-weight: 400;
		}
		header .brand-logo-img{
			width: 50%;
		}
		header .brand-logo-img a{
			color: #fff;
			display: block;
			text-align: center;
			text-decoration: none;
			max-width: 150px;
			margin-left: auto;
		}
		header .brand-logo-img a span{
			display: block;
		}
		header .brand-logo-img img{
			max-width: 70px;
		}
		.contract-main .container{
			padding: 20px 60px;
		}
		.contract-heading{
			text-align: center;
		}
		.contract-heading p{
			margin-bottom: 0;
		}
		.contract-heading h2{
			margin-top: 10px;
		}
		.contract-heading h2 i{
			display: block;
		}
		.contract-sub-heading{
			text-align: right;
		}
		.d-gird{
			display: grid;
			grid-template-columns: 50% 50%;
		}
		.d-gird p{
			margin: 0;
			margin-bottom: 1em;
      font-size:90%;
		}
		.mb-3{
			margin-bottom: 30px;
		}
		.mb-0{
			margin-bottom: 0px!important;
		}
		.contract-body ul{
			list-style-type: none;
			padding: 0;
			margin: .5em 0;
		}
		.contract-body ul li{
			padding: 5px 0;
			display: block;
			padding-left: 40px;
			position: relative;
		}
		.contract-body ul li .num{
			position: absolute;
    		left: 0;
		}
		.contract-sign{
			display: grid;
			grid-template-columns: 35% 65%;
			text-align: center;
			padding: 20px 0;
			padding-bottom: 150px;
		}
		footer{
			padding: 20px 40px;
			text-align: center;
			color: #fff;
			background-color: #24793b;
		}

	</style>
</head>

<body>
	<header style="
			padding: 20px 40px;
			background-color: #24793b;
			font-family: Arial, Helvetica, sans-serif
		">
		<div class="container">
			<div class="brand-logo-text">
				<h1 style="color: #fff;">
					TAN TAO UNIVERSITY
					<span>TRƯỜNG ĐẠI HỌC TÂN TẠO</span>
				</h1>
			</div>
			<div class="brand-logo-img">
				<a href="https://ttu.edu.vn/" target="_blank">
					<img src="https://tuyensinh.ttu.edu.vn/logo/logo.svg" />
					<span>Website: ttu.edu.vn</span>
				</a>
			</div>
		</div>
	</header>

	<section class="contract-main">
		<div class="container contract-heading">
			<p>No: 322/HĐLĐ-TTU.19</p>
			<h2>Labor Contract <i>Hợp Đồng Lao Động</i></h2>
			<div class="contract-sub-heading">
				Long An, ${fetchData({ data: create_at, field: 'fullDateEn' })} <br />
				<i>Long An, ${fetchData({ data: create_at, field: 'fullDateVi' })}</i>
			</div>
		</div>
		<div class="container contract-body">
			<p>
				<b>We are, TAN TAO UNIVERSITY (TTU)</b><br />
				<i>Chúng tôi, một bên là Trường ĐẠI HỌC TÂN TẠO (TTU)</i>
			</p>
			<p>
				<b>Address: Tan Duc E.City, Duc Hoa Ha, Duc Hoa District, Long An Province</b><br />
				<i>Địa Chỉ: Khu đô thị E.City Tân Đức, Xã Đức Hòa Hạ, Huyện Đức Hòa, Tỉnh Long An</i>
			</p>
			<div class="d-gird mb-3">
				<p>
					<b>Representative, ${fetchData({ data: employee_represent.sex, field: 'sex' })} ${fetchData({ data: `${employee_represent.last_name} ${employee_represent.first_name}`, lang: 'en', field: 'text' })}</b><br />
					<i>Đại diện: ${fetchData({ data: employee_represent.sex, lang: 'vi', field: 'sex' })} ${fetchData({ data: `${employee_represent.last_name} ${employee_represent.first_name}`, field: 'text' })}</i>
				</p>
				<p>
					<b>Nationality: ${await trans(employee_represent.nationality)}</b><br />
					<i>Quốc tịch: ${employee_represent.nationality || ''}</i>
				</p>
				<p>
					<b>Position: ${position_represent.name || ''}</b><br />
					<i>Chức vụ: ${position_represent.name_vn || ''}</i>
				</p>
				<p>
					<b>Telephone: ${fetchData({ data: employee_represent.phone, field: 'phone' })}</b><br />
					<i>Điện Thoại: ${fetchData({ data: employee_represent.phone, field: 'phone' })}</i>
				</p>
			</div>
			<div class="d-gird">
				<p>
					<b>And from other side,	 ${fetchData({ data: employee.sex, field: 'sex' })} ${fetchData({ data: `${employee.last_name} ${employee.first_name}`, lang: 'en', field: 'text' })}</b><br />
					<i>Và một bên là ${fetchData({ data: employee.sex, lang: 'vi', field: 'sex' })} ${fetchData({ data: `${employee.last_name} ${employee.first_name}`, field: 'text' })}</i>
				</p>
				<p>
					<b>Nationality: ${await trans(employee.nationality)}</b><br />
					<i>Quốc tịch: ${employee.nationality || ''}</i>
				</p>
				<p>
					<b>Date of birth:	${fetchData({ data: employee.dob, field: 'date' })}</b><br />
					<i>Sinh ngày: ${fetchData({ data: employee.dob, field: 'date' })}</i>
				</p>
				<p>
					<b>Place of birth: ${await trans(employee.pod)}</b><br />
					<i>Tại: ${employee.pod || ''}</i>
				</p>
				<p>
					<b>Identity Card No.: ${employee.identification_number || ''}</b><br />
					<i>CMND số:  ${employee.identification_number || ''}</i>
				</p>
				<p>
					<b>Date of issue:${fetchData({ data: employee.identification_date, field: 'date' })}</b><br />
					<i>Cấp ngày: ${fetchData({ data: employee.identification_date, field: 'date' })}</i>
				</p>
				<p class="mb-0">
					<b>Place of issue: ${await trans(employee.identification_place)}</b><br />
					<i>Tại: ${employee.identification_place || ''}</i>
				</p>
				<p class="mb-0">
					<b>Qualification: ${await trans(employee.education)}</b><br />
					<i>Trình độ chuyên môn: ${employee.education || ''}</i>
				</p>
			</div>

			<p>
				<b>Permanent Address: ${await trans(employee.address)}</b><br />
				<i>Địa chỉ thường trú: ${employee.address}</i>
			</p>
			<p>
				<b>Telephone: ${fetchData({ data: employee.phone, field: 'phone' })}</b><br />
				<i>Điện thoại: ${fetchData({ data: employee.phone, field: 'phone' })}</i>
			</p>
			<p>
				<b>Agree to sign this Labor Contract and commit to implement the following provisions:</b><br />
				<i>Thỏa thuận ký hợp đồng lao động và cam kết thực hiện đúng các điều khoản sau đây:</i>
			</p>
			<p class="mb-0">
				<b><u>Article 1:</u> Terms and work contract:</b><br />
				<b><i><u>Điều 1:</u> Thời hạn và Công việc hợp đồng</i></b>
			</p>
			<ul>
				<li>
					<span class="num">1.1</span>Type of contract: ${await trans(fetchData({ data: term, field: 'term' }))}<br/>
					<span class="num"></span><i>Loại hợp đồng: ${fetchData({ data: term, field: 'term' })}</i>
				</li>
        <br/><br/><br/><br/><br/>
				<li>
					<span class="num">1.2</span> Effective date: From date: ${fetchData({ data: start_at, field: 'date' })} to date: ${fetchData({ data: end_at, field: 'date' })}<br/>
					<span class="num"></span> <i>Thời gian hiệu lực: Từ ngày ${fetchData({ data: start_at, field: 'date' })} đến ngày ${fetchData({ data: end_at, field: 'date' })}</i>
				</li>
				<li>
					<span class="num">1.3</span> Main working location: Tan Tao University<br/>
					<span class="num"></span> <i>Địa điểm làm việc chính: Trường Đại Học Tân Tạo</i>
				</li>
				<li>
					<span class="num">1.4</span> Other working locations (outside of TTU): To be informed and agreed upon.<br/>
					<span class="num"></span> <i>Địa điểm làm việc khác (ngoài Trường Đại học Tân Tạo): Sẽ thoả thuận với với Người lao động làm việc ngoài địa điểm chính do nhu cầu công việc. </i>
				</li>
				<li>
					<span class="num">1.5</span> Professional title: Full time faculty member <br/>
					<span class="num"></span> <i>Chức danh chuyên môn: Giảng viên cơ hữu</i>
				</li>
				<li>
					<span class="num">1.6</span> The work to be done: As stated in the Faculty Handbook<br/>
					<span class="num"></span> <i>Công việc phải làm: như mô tả trong Sổ tay Giảng viên.</i>
				</li>
			</ul>
			<p class="mb-0">
				<b><u>Article 2:</u> Work policies</b><br />
				<b><i><u>Điều 2:</u> Chế độ lao động</i></b>
			</p>
			<ul>
				<li>
					<span class="num">2.1</span>Working time: As stated by the current regulations of the Ministry of Education and Training and the assignment of the School of Medicine.<br/>
					<span class="num"></span><i>Thời gian lao động: Theo qui định hiện hành của Bộ GDĐT và theo phân công của Khoa Y.</i>
				</li>
				<li>
					<span class="num">2.2</span>Facilities and equipment offered: dependent on job requirements under the provisions of Vietnamese Labor Laws<br/>
					<span class="num"></span><i>Trang thiết bị làm việc: Theo nhu cầu công việc và theo qui định của Luật Lao Động</i>
				</li>
			</ul>
			<p class="mb-0">
				<b><u>Article 3:</u> Duties and Rights of employee</b><br />
				<b><i><u>Điều 3:</u> Nghĩa vụ và quyền lợi của người lao động </i></b>
			</p>
			<ul>
				<li>
					<span class="num">3.1</span><b>Rights</b><br/>
					<span class="num"></span><b><i>Quyền lợi</i></b>
				</li>
				<li>
					<span class="num">3.1.1</span>Means of transport for work: Not provided<br/>
					<span class="num"></span><i>Phương tiện đi lại: Tự túc</i>
				</li>
				<li>
					<span class="num">3.1.2</span>Gross salary will be ${fetchData({ data: gross, field: 'money' })} VND per month including contribution of social insurance, health insurance, unemployment insurance and other taxes that might pertain.<br/>
					<span class="num"></span><i>Tổng thu nhập hàng tháng bằng ${fetchData({ data: gross, field: 'money' })} VND (${gross_text || ''}) đã bao gồm BHYT, BHXH, BHTN và các khoản thuế khác nếu có.</i>
				</li>
				<li>
					<span class="num">3.1.3</span>Payment method: ${await trans(payment_method)}<br/>
					<span class="num"></span><i>Phương thức thanh toán: ${payment_method || ''}</i>
				</li>
				<li>
					<span class="num">3.2</span><b>Duties</b><br/>
					<span class="num"></span><b><i>Nghĩa vụ</i></b>
				</li>
				<li>
					<span class="num">3.2.1</span>You must perform the tasks stated in the Faculty Handbook well.<br/>
					<span class="num"></span><i>Anh/Chị phải thực hiện đầy đủ với hiệu quả tốt những công việc đã được nêu trong Sổ tay Giảng viên.</i>
				</li>
				<li>
					<span class="num">3.2.2</span>You must strictly comply with labor regulations, the Collective Labor Agreement, and the current Charter of TTU.<br/>
					<span class="num"></span><i>Anh/Chị phải nghiêm túc chấp hành nội quy lao động, Thỏa ước Lao động Tập thể, quy chế, chính sách hiện hành của Trường.</i>
				</li>
				<li>
					<span class="num">3.2.3</span>You agree to comply with all confidentiality regulations: policies, rules and requirements during your full time duties under contract. You shall not disclose the above mentioned information to any individual, collectives including family members and relatives. Violation of the confidentiality commitment will lead to legal action including the obligation to reimburse for material damage in accordance to Vietnamese Law.<br/><br/><br/><br/><br/><br/><br/>
					<span class="num"></span><i>Trong thời gian này, Anh/Chị tuyệt đối giữ những bí mật về thông tin, công nghệ, chính sách và các nội quy của Trường. Giữ bí mật được hiểu là Anh/Chị không được tiết lộ thông tin trên cho bất kỳ cá nhân, tập thể nào, kể cả gia đình và người thân của Anh/Chị. Trong trường hợp có bằng chứng về sự rò rỉ thông tin từ Anh/Chi, Anh/Chị sẽ chịu hoàn toàn trách nhiệm với TTU, bao gồm cả nghĩa vụ bồi hoàn thiệt hại về vật chất theo quy định của pháp luật Việt Nam.</i>
				</li>
				<li>
					<span class="num">3.2.4</span>In order to act as an adjunct professor or lecturer at colleges, universities, or academic institutions outside of the TTU institution, You must formally request an approval from the TTU Provost, and ensure that all duties and responsibilities at Tan Tao University are completely upheld and performed satisfactorily. Failure to commit to this policy will result in disciplinary actions in accordance to TTU regulations.<br/>
					<span class="num"></span><i>Trong thời gian làm giảng viên cơ hữu của TTU, nếu Anh/Chị nhận làm giảng viên thỉnh giảng tại bất kỳ cơ sở đào tạo nào khác thì phải được sự đồng ý bằng văn bản của Hiệu trưởng TTU, đồng thời phải đảm bảo thực hiện tốt mọi nhiệm vụ mà TTU phân công. Nếu Anh/Chị không báo cáo và TTU phát hiện thì tùy theo tình hình, TTU sẽ có hình thức xử lý phù hợp.</i>
				</li>
				<li>
					<span class="num">3.2.5</span>During your employment at TTU, full time employment as a professor or lecturer in colleges, universities, or academic institutions outside of the TTU institution is strictly prohibited. Failure to commit to this policy will result in monetary fines that amounts to double the total income received during the violation period and legal action in accordance to Vietnamese Laws.<br/>
					<span class="num"></span><i>Trong thời gian thực hiện Hợp đồng này, Anh/Chị không được phép làm giảng viên cơ hữu ở bất kỳ cơ sở đào tạo nào khác. Nếu bị phát hiện vi phạm thì Anh/Chị phải chịu phạt vi phạm và bồi  thường cho TTU gấp 2 lần tổng thu nhập trong thời gian vi phạm và bị xử lý theo quy định pháp luật.</i>
				</li>
				<li>
					<span class="num">3.2.6</span>Scientific research tasks must be completed in accordance with regulations that govern full time faculty members. Each month, 90% of your income will be paid while the remaining 10% will be withheld and distributed at the end of the year upon completion of all scientific research assigned that academic year. If research is not completed within the academic year, the remaining 10% will not be released and deducted from your salary. If you do not complete this task in two (2) consecutive years, TTU has the right to review the contract. <br/>
					<span class="num"></span><i>Anh/Chị phải hoàn thành nhiệm vụ nghiên cứu khoa học theo qui định về chế độ làm việc đối với giảng viên cơ hữu. Mỗi tháng, nhà trường sẽ thanh toán 90% thu nhập, 10% còn lại sẽ được thanh toán vào cuối năm, khi Anh/Chị hoàn thành nhiệm vụ nghiên cứu khoa học trong năm học đó theo quy định. Trường hợp không hoàn thành nhiệm vụ nghiên cứu khoa học trong năm thì không được nhận lại 10% thu nhập nêu trên. Nếu không hoàn thành nhiệm vụ này trong hai (2) năm liên tiếp nhà trường có quyền xem xét lại hợp đồng.</i>
				</li>
			</ul>
			<p class="mb-0">
				<b><u>Article 4:</u> Duties and Rights of employer</b><br />
				<b><i><u>Điều 4:</u> Nghĩa Vụ và Quyền Hạn của người sử dụng lao động</i></b>
			</p>
			<ul>
				<li>
					<span class="num">4.1</span><b>Duties</b><br/>
					<span class="num"></span><b><i>Nghĩa vụ</i></b>
				</li>
				<li>
					<span class="num">4.1.1</span>Ensure employment and sufficient implementation of provisions as specified in Labor Contract.<br/>
					<span class="num"></span><i>Đảm bảo việc làm và thực hiện đầy đủ những điều đã cam kết trong hợp đồng lao động.</i>
				</li>
				<li>
					<span class="num">4.1.1</span>Disburse salary payment in accordance with Labor Contract provisions.<br/>
					<span class="num"></span><i>Trả lương đúng thời hạn và đúng theo điều khoản Hợp đồng lao động.</i>
				</li>
				<li>
					<span class="num">4.1.1</span>Enable and support employee to quickly integrate and develop capacity.<br/>
					<span class="num"></span><i>Tạo điều kiện và hỗ trợ cho người lao động sớm hội nhập và phát huy năng lực.</i>
				</li>
				<li>
					<span class="num">4.1.1</span>Provide protection equipment and ensure a good and safe working environment according to policy and labor safety code.<br/>
					<span class="num"></span><i>Hỗ trợ các trang thiết bị cho người lao động và đảm bảo môi trường làm việc tốt và an toàn.</i>
				</li>
				<li>
					<span class="num">4.2</span><b>Rights</b><br/>
					<span class="num"></span><b><i>Quyền hạn</i></b>
				</li>
        <br/><br/><br/><br/><br/><br/>
				<li>
					<span class="num">4.2.1</span>Contract termination without prior notification when discovering employee violating the terms of the TTU’s regulations;<br/>
					<span class="num"></span><i>Đơn phương chấm dứt Hợp đồng lao động khi phát hiện người lao động vi phạm các điều khoản trong qui chế Trường.</i>
				</li>
				<li>
					<span class="num">4.2.1</span>Manage and arrange tasks according to TTU’s requirements; supervise and check employee’s work performance as stated in the Faculty Handbook.<br/>
					<span class="num"></span><i>Điều hành, bố trí công việc theo nhu cầu của Trường, giám sát, kiểm tra người lao động hoàn thành khối lượng công việc theo như mô tả trong Sổ tay Giảng viên.</i>
				</li>
				<li>
					<span class="num">4.2.1</span>Delay, suspend, transfer positions, make lay-off and discipline decisions if employee infringes regulations, policies, procedures and provisions.<br/>
					<span class="num"></span><i>Tạm hoãn, đình chỉ, thuyên chuyển vị trí, quyết định kỷ luật và chấm dứt hợp đồng nếu người lao động vi phạm nội quy, chính sách, quy trình, chế độ và vi phạm các điều đã thỏa thuận, cam kết.</i>
				</li>
				<li>
					<span class="num">4.2.1</span>Prosecute employee in the case of purchasing, selling or disclosing confidential information or purposefully causing damage or loss to TTU.<br/>
					<span class="num"></span><i>Truy tố pháp luật trong trường hợp người lao động mua bán, làm rò rỉ thông tin hoặc cố ý gây hậu quả nghiêm trọng cho Trường.</i>
				</li>
				<li>
					<span class="num">4.2.1</span>To appoint employee to hold concurrent positions, to work in other fields or/and work in other places different from the place stated in Article 1 subject to the development demand and necessity of TTU.<br/>
					<span class="num"></span><i>Được phép điều động làm kiêm nhiệm hoặc/và sang các lĩnh vực khác hoặc/và cả địa điểm làm việc khác với nơi trong Điều 1, tùy theo nhu cầu phát triển và sự cần thiết của Trường.</i>
				</li>
			</ul>
			<p class="mb-0">
				<b><u>Article 5:</u> Implementing provisions</b><br />
				<b><i><u>Điều 5:</u> Điều Khoản Thi Hành</i></b>
			</p>
			<ul>
				<li>
					<span class="num">5.1</span>This Labor Contract enclosed with annexes is an indivisible part accompanying Job Description in the Faculty Handbook, Probationary Contract, and Hiring Decision.<br/>
					<span class="num"></span><i>Hợp đồng Lao động này bao gồm các phụ lục đi kèm là bộ phận không tách rời cùng với  Mô tả Công việc trong Sổ tay Giảng viên, Hợp đồng Thử việc và Quyết định tuyển dụng.</i>
				</li>
				<li>
					<span class="num">5.2</span>The two parties shall agree and commit to implement provisions stated in this contract.<br/>
					<span class="num"></span><i>Hai bên đã đồng ý và cam kết thực hiện những điều đã nêu trong hợp đồng.</i>
				</li>
				<li>
					<span class="num">5.3</span>In case of contract dispute, the two sides shall base on the final decision of state authority as prescribed by law.<br/>
					<span class="num"></span><i>Trong trường hợp có tranh chấp phát sinh từ hợp đồng, hai bên sẽ căn cứ vào phán quyết cuối cùng của cơ quan nhà nước có thẩm quyền theo qui định của pháp luật.</i>
				</li>
				<li>
					<span class="num">5.4</span>This contract is made into 02 (two) original copies, bilingual languages: English and Vietnamese, having equal validity. Each party will keep 01 (one) copy.<br/>
					<span class="num"></span><i>Hợp đồng này được làm thành 02 (hai) bản gốc song ngữ, tiếng Anh và tiếng Việt, có giá trị ngang nhau. Mỗi bên giữ 01 (một) bản.</i>
				</li>
				<li>
					<span class="num">5.1</span>This contract is signed at Tan Tao University on  ${fetchData({ data: contract_at, field: 'date' })}.<br/>
					<span class="num"></span><i>Hợp đồng ký tại Trường Đại Học Tân Tạo ngày ${fetchData({ data: contract_at, field: 'date' })}.</i>
				</li>
			</ul>

			<div class="contract-sign">
				<div class="employee">
					<b>Employee</b><br />
					(Sign/Full name)<br />
					<b>Người Lao Động</b><br />
					(Ký tên/ Ghi rõ họ tên)
				</div>
				<div class="employer">
					<b>Employer</b><br />
					(Sign/TTU Seal and Full name - on behalf of the University)<br />
					<b>Người Sử Dụng Lao Động</b><br />
					(Ký tên/ Đóng dấu và ghi rõ họ tên người đại diện)
				</div>
			</div>

		</div>
	</section>
</body>

</html>`;
