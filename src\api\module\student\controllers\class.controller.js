/* eslint-disable no-await-in-loop */
// 30/11/2022
const db = require('../../../../config/mysql');
const { success: jsonSuccess } = require('../../../middlewares/success');
const AttendancePermission = require('../models/attendance.permission.model');
const { registrarEmails, secretaryEmails } = require('../../../../config/vars');

const Class = db.class;

exports.list = async (req, res, next) => {
  try {
    const { query, user } = req;
    if (user.role === 0) { // user là sv
      let total = 0;
      const data = [];
      const attendancePermission = await AttendancePermission.list({ student: user.email });
      for (let i = 0; i < attendancePermission.length; i += 1) {
        const _class = await Class.get(attendancePermission[i].class);
        if (_class) {
          total += 1;
          data.push(_class);
        }
      }
      jsonSuccess({ total, data }, req, res);
    } else if (registrarEmails.includes(user.email) || secretaryEmails.includes(user.email)) {
      const _class = await Class.list({ ...query });
      jsonSuccess(_class, req, res);
    } else {
      if (!query.lecturer) {
        query.lecturer = req.user._id;
      }
      const _class = await Class.list({ ...query });
      jsonSuccess(_class, req, res);
    }
  } catch (error) {
    next(error);
  }
};
