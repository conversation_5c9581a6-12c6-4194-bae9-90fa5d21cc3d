/* eslint-disable no-await-in-loop */
// 12/9/2022
// @dnine
const QRCode = require('qrcode');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');

const Device = require('../models/device.model');

exports.load = async (req, res, next, id) => {
  try {
    const device = await Device.get(id);
    req.locals = { device };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;

    const devices = await Device.list({ ...query });
    const count = await Device.count(req.query);
    const transformed = devices.map(type => type.transform());
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.get = async (req, res, next) => {
  try {
    const { device } = req.locals;
    jsonSuccess(device, req, res);
  } catch (error) {
    next(error);
  }
};

exports.getByTag = async (req, res, next) => {
  try {
    const { tagId } = req.params;
    const device = await Device.getByTag(tagId);
    jsonSuccess(device, req, res);
  } catch (error) {
    next(error);
  }
};

exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user.email;
    req.body.tag_id = `random-${Math.floor(Math.random() * 999999)}`;
    const device = new Device(req.body);
    const saved = await device.save();
    jsonSuccess(saved.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = (req, res, next) => {
  const { device } = req.locals;

  device.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};

exports.update = (req, res, next) => {
  req.body.updated_by = req.user.email;

  const device = Object.assign(req.locals.device, req.body);

  device.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

exports.faultyReport = (req, res, next) => {
  req.body.updated_by = req.user.email;

  const device = Object.assign(req.locals.device, req.body);

  device.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

exports.qrGenerate = async (req, res, next) => {
  try {
    const qrGen = async (text) => {
      try {
        const options = {
          width: 440,
        };

        const url = await QRCode.toDataURL(text, options);
        return url;
      } catch (error) {
        return error.message;
      }
    };
    const devices = await Device.list({ perPage: -1 });
    for (let i = 0; i < devices.length; i += 1) {
      const item = devices[i];
      const qr = await qrGen(item._id.toString());
      if (qr) {
        await Device.findByIdAndUpdate(item._id, { avatar: qr });
      }
    }
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

