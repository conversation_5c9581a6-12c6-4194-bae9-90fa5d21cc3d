// 23/2/2024
const db = require('../../../../config/mysql');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');

const Certificate = db.certificate;
const Department = db.department;
const Major = db.major;

exports.load = async (req, res, next, id) => {
  try {
    const certificate = await Certificate.get(id);
    req.locals = { certificate };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.get = (req, res) => {
  jsonSuccess(req.locals.certificate.transform(), req, res);
};

// exports.create = async (req, res, next) => {
//   try {
//     const { user } = req;
//     if (!registrarEmails.includes(user.email)) {
//       throw new APIError({
//         message: 'Don\'t have permission',
//         status: httpStatus.BAD_REQUEST,
//       });
//     }
//     req.body.created_by = user.email;
//     const calendar = new Certificate(req.body);
//     const saved = await calendar.save();
//     jsonSuccess(saved.transform(), req, res);
//   } catch (error) {
//     next(error);
//   }
// };

// exports.update = (req, res, next) => {
//   const { user } = req;
//   if (!registrarEmails.includes(user.email)) {
//     throw new APIError({
//       message: 'Don\'t have permission',
//       status: httpStatus.BAD_REQUEST,
//     });
//   }
//   req.body.updated_by = user.email;
//   const calendar = Object.assign(req.locals.calendar, req.body);

//   calendar.save()
//     .then(saved => jsonSuccess(saved.transform(), req, res))
//     .catch(e => next(e));
// };

exports.list = async (req, res, next) => {
  try {
    const certificates = await Certificate.list({
      ...req.query,
      departmentModel: Department,
      majorModel: Major,
    });

    jsonSuccess(certificates, req, res);
  } catch (error) {
    next(error);
  }
};

// exports.remove = async (req, res, next) => {
//   try {
//     const { user } = req;
//     if (!registrarEmails.includes(user.email)) {
//       throw new APIError({
//         message: 'Don\'t have permission',
//         status: httpStatus.BAD_REQUEST,
//       });
//     }
//     const { calendar } = req.locals;
//     await calendar.remove();
//     jsonSuccess({}, req, res);
//   } catch (error) {
//     next(error);
//   }
// };

