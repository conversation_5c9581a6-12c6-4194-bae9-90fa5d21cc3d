// 16/9/2022
// @dnine
const Joi = require('joi');

module.exports = {

  listDeviceNotebooks: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      sort: Joi.string(),
      start_at: Joi.date(),
      is_borrow: Joi.boolean(),
    }),
  },

  createDeviceNotebook: {
    body: Joi.object({
      device: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      name: Joi.string().required(),
      code: Joi.string(),
      phone: Joi.string(),
      department: Joi.string().allow('').optional(),
      devices: Joi.array().items(Joi.string()),
    }),
  },

  updateDeviceNotebook: {
    body: Joi.object({
      is_borrow: Joi.boolean(),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  updateManyDeviceNotebook: {
    body: Joi.object({
      is_borrow: Joi.boolean(),
      devices: Joi.array().items(Joi.string().min(1).required()),
    }),

  },
};
