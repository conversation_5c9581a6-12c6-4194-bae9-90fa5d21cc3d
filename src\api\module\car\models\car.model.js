// 2/11/2021
// const { Op } = require('sequelize');
const _ = require('lodash');
const moment = require('moment');
const employeeFields = (require('../../employee/models/employee.model')).fields;
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

const fields = {
  table: 'r300',
  _id: 'pr300',
  driver: 'fm100', // ID Tài xế (= ID Nhân viên)
  name: 'rv302', // Tên
  seat: 'rn305', // Số ghế
  license_plate: 'rv303', // Biển số xe
  code: 'rv301', // <PERSON>ã hiệu (gốc, hiển thị cho người dùng)
  is_approve: 'rn301', // Trạng thái xét duyệt
  approved_at: 'rd301', // <PERSON><PERSON>y xét duyệt
  description: 'rl338', // <PERSON><PERSON>ả

  deleted_by: 'rl344', // <PERSON>ail <PERSON>
  deleted_at: 'rl345', // Thời gian x<PERSON>a
  created_by: 'rl347', // <PERSON>ail người tạo
  updated_by: 'rl349', // Email người cập nhật
  updated_at: 'rl348',
  created_at: 'rl346',
};

const schema = (sequelize, DataTypes) => {
  const carSchema = sequelize.define('Car', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.name]: {
      type: DataTypes.STRING(150),
      defaultValue: null,
    },
    [fields.seat]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.license_plate]: {
      type: DataTypes.STRING(280),
      defaultValue: null,
    },
    [fields.code]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.is_approve]: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    [fields.approved_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.description]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },

    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Car = carSchema;

  carSchema.get = async ({ id, employeeModel }) => {
    try {
      const car = await Car.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.name, 'name'],
          [fields.seat, 'seat'],
          [fields.license_plate, 'license_plate'],
          [fields.code, 'code'],
          [fields.description, 'description'],
          [fields.created_by, 'created_by'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
        include: [
          {
            model: employeeModel,
            as: 'driver',
            attributes: [
              [employeeFields._id, '_id'],
              [employeeFields.last_name, 'last_name'],
              [employeeFields.first_name, 'first_name'],
              [employeeFields.phone, 'phone'],
              [employeeFields.email, 'email'],
              [employeeFields.avatar, 'avatar'],
            ],
          },
        ],
      });
      if (car) {
        return car;
      }
      throw new APIError({
        message: 'Car does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  carSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await Car.countItem({
      [fields.deleted_at]: null,
    });
    const cars = await Car.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.name, 'name'],
        [fields.seat, 'seat'],
        [fields.license_plate, 'license_plate'],
        [fields.code, 'code'],
        [fields.is_approve, 'is_approve'],
        [fields.approved_at, 'approved_at'],
        [fields.description, 'description'],
      ],
      where: {
        [fields.deleted_at]: null,
      },
      include: {
        model: employeeModel,
        as: 'driver',
        attributes: [
          [employeeFields._id, '_id'],
          [employeeFields.first_name, 'first_name'],
          [employeeFields.last_name, 'last_name'],
        ],
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: cars };
  };

  carSchema.countItem = async (query) => {
    const count = await Car.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  carSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Car.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  carSchema.remove = async ({ id, email }) => {
    try {
      const result = await Car.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  return carSchema;
};

module.exports = {
  schema,
  fields,
};
