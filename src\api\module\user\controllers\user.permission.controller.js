// 25/10/2021
const _ = require('lodash');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const { Op } = require('sequelize');
const db = require('../../../../config/mysql');
const { fields } = require('../models/user.permission.model');
const { success: jsonSuccess } = require('../../../middlewares/success');
const ArticleFolder = require('../../article/models/article.folder.model');

const UserPermission = db.userPermission;
const Employee = db.employee;
// const FUNCTION_CODE = 'FUNC_LEAVE_ALLOW_';
// const FUNCTION_NAME = 'Quản lý nghỉ phép: ';
const FUNCTION_CODE = 'FUNC_DEPARTMENT_HEAD_';
const FUNCTION_NAME = 'Trưởng bộ phận: ';


exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const dbData = {};
    _.forEach(data, (value, key) => {
      if (key === 'permission_ui_function_code') {
        dbData[fields[key]] = `${FUNCTION_CODE}${value}`;
      } else if (key === 'permission_ui_function_name') {
        dbData[fields[key]] = `${FUNCTION_NAME}${value}`;
      } else {
        dbData[fields[key]] = value;
      }
    });

    const userPermission = UserPermission.build({
      ...dbData,
    });
    const saved = await userPermission.save();
    jsonSuccess({ saved }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;

    const userPermission = await UserPermission.list({
      ...query,
      permission_ui_function_code: { [Op.startsWith]: FUNCTION_CODE },
      employeeModel: Employee,
    });

    jsonSuccess(userPermission, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await UserPermission.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const result = await UserPermission.remove({
      id: req.params.id,
      employee: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.createPermissionArticle = async (req, res, next) => {
  try {
    const data = req.body;
    const folder = await ArticleFolder.get(req.body.folder);
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });
    dbData[fields.permission_ui_function_name] = `Đăng công văn / biểu mẫu (${folder.title})`;
    dbData[fields.permission_ui_function_code] = `FUNC_POST_DOCUMENT_${req.body.folder}`;
    const userPermission = UserPermission.build({
      ...dbData,
    });
    const saved = await userPermission.save();
    jsonSuccess({ saved }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.checkPermissionArticle = async (req, res, next) => {
  try {
    const { query } = req;

    const userPermission = await UserPermission.list({
      ...query,
      permission_ui_function_code: { [Op.startsWith]: 'FUNC_POST_DOCUMENT' },
      employeeModel: Employee,
    });
    if (userPermission.data.length > 0) {
      jsonSuccess(userPermission.data[0], req, res);
    } else {
      throw new APIError({
        message: 'Permission does not exist',
        status: httpStatus.NOT_FOUND,
      });
    }
  } catch (error) {
    next(error);
  }
};
