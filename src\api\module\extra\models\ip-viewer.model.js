// const mongoose = require('mongoose');
// const { omitBy, isNil } = require('lodash');
// const httpStatus = require('http-status');
// const APIError = require('../../../utils/APIError');

// /**
//  * IpViewer Schema
//  * @private
//  */
// const ipViewerSchema = new mongoose.Schema({
//   article: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'Article',
//     required: true,
//   },
//   ip: {
//     type: String,
//     default: '',
//   },
// }, {
//   timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
// });

// /**
//  * Methods
//  */
// ipViewerSchema.method({
//   transform() {
//     const transformed = {};
//     const fields = [
//       '_id',
//       'article',
//       'ip',
//       'created_at',
//     ];

//     fields.forEach((field) => {
//       transformed[field] = this[field];
//     });

//     return transformed;
//   },
// });

// /**
//  * Statics
//  */
// ipViewerSchema.statics = {
//   /**
//    * Get IpViewer
//    *
//    * @param {ObjectId} id - The objectId of IpViewer.
//    * @returns {Promise<IpViewer, APIError>}
//    */
//   async get(id) {
//     try {
//       const IpViewer = await this.findById(id).exec();
//       if (IpViewer) {
//         return IpViewer;
//       }

//       throw new APIError({
//         message: 'IpViewer does not exist',
//         status: httpStatus.NOT_FOUND,
//       });
//     } catch (error) {
//       throw error;
//     }
//   },

//   /**
//    * List competitions in descending order of 'created_at' timestamp.
//    * @param {number} skip - Number of competitions to be skipped.
//    * @param {number} limit - Limit number of competitions to be returned.
//    * @returns {Promise<IpViewer[]>}
//    */
//   async list({
//     page = 1,
//     perPage = 30,
//     sort,
//     ip,
//     article,
//   }) {
//     try {
//       const options = omitBy({
//         ip,
//         article,
//       }, isNil);
//       const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };

//       const result = this.find(options)
//         .sort(sortOpts);
//       if (perPage > -1) {
//         result.skip(perPage * (page - 1)).limit(perPage);
//       }
//       return result.exec();
//     } catch (error) {
//       throw error;
//     }
//   },

//   /**
//    * Count competitions.
//    * @returns {Promise<Number>}
//    */
//   async count({
//     ip,
//     article,
//   }) {
//     const options = omitBy({
//       ip,
//       article,
//     }, isNil);
//     return this.find(options).count();
//   },
// };

// /**
//  * @typedef IpViewer
//  */
// module.exports = mongoose.model('IpViewer', ipViewerSchema);
