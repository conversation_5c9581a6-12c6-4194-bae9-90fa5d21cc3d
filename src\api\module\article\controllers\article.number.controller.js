/* eslint-disable no-restricted-syntax */
const ArticleNumber = require('../models/article.number.model');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

/**
 * Load article number and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const articleNumber = await ArticleNumber.get(id);
    req.locals = { articleNumber };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get article number
 * @public
 */
exports.get = (req, res) => {
  jsonSuccess(req.locals.articleNumber.transform(), req, res);
};

/**
 * Create new article number
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user._id;
    const category = await ArticleNumber.findOne({ title: req.body.title }).exec();
    if (category) {
      throw new APIError({
        message: 'ArticleNumber already exists',
        status: httpStatus.BAD_REQUEST,
      });
    } else {
      const articleNumber = new ArticleNumber(req.body);
      const saved = await articleNumber.save();
      jsonSuccess(saved.transform(), req, res);
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing article number
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const articleNumber = Object.assign(req.locals.articleNumber, req.body);

  articleNumber.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get article number list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await ArticleNumber.count(req.query);
    const types = await ArticleNumber.list(req.query);
    const transformed = types.map(type => type.transform());

    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete article number
 * @public
 */
exports.remove = (req, res, next) => {
  const { articleNumber } = req.locals;

  articleNumber.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};
