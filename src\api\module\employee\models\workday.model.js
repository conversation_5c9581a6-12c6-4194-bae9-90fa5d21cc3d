// 5/1/2022
// ngày công mỗi tháng
const { Op } = require('sequelize');
const _ = require('lodash');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const employeeFields = (require('./employee.model')).fields;


const fields = {
  table: 'm450',
  _id: 'pm450', // ID
  employee: 'fm100', // ID Nhân viên / giảng viên
  start_date: 'md452', // Ng<PERSON>y tháng năm chấm công
  month_workday: 'mn453', // Tổng ngày công
  total_workday: 'mn454', // Ngày công đã làm

  deleted_by: 'ml444',
  deleted_at: 'ml445',
  created_by: 'ml447',
  updated_by: 'ml449',
  updated_at: 'ml448',
  created_at: 'ml446',
};

const schema = (sequelize, DataTypes) => {
  const workdaySchema = sequelize.define('Workday', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.start_date]: {
      type: DataTypes.DATE,
      defaultValue: null,
    },
    [fields.month_workday]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    [fields.total_workday]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },

    [fields.deleted_by]: {
      // Email người xóa
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      // Thời gian xóa
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      // Email người tạo
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      // Email người cập nhật
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Workday = workdaySchema;

  workdaySchema.create = async (data) => {
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });
    const workday = Workday.build({ ...dbData });
    const saved = await workday.save();
    return saved;
  };

  workdaySchema.findOrCreate = async ({ data, where }) => {
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });
    const dbWhere = {};
    _.forEach(where, (value, key) => {
      dbWhere[fields[key]] = value;
    });
    const workday = await Workday.findOne({
      where: {
        ...dbWhere,
        [fields.deleted_at]: null,
      },
    });
    if (!workday) {
      const newWorkday = Workday.build({ ...dbData });
      const saved = await newWorkday.save();
      return saved;
    }
    return 'exist';
  };

  workdaySchema.countItem = async (query) => {
    const count = await Workday.count({
      where: { ...query },
    });
    return count;
  };

  workdaySchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    employeeModel,
    employee = { [Op.not]: null },
    start_date = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await Workday.countItem({
      [fields.deleted_at]: null,
      [fields.employee]: employee,
      [fields.start_date]: start_date,
    });
    const workday = await Workday.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.start_date, 'start_date'],
        [fields.month_workday, 'month_workday'],
        [fields.total_workday, 'total_workday'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.employee]: employee,
        [fields.start_date]: start_date,
      },
      include: {
        model: employeeModel,
        as: 'employee',
        attributes: [
          [employeeFields._id, '_id'],
          [employeeFields.first_name, 'first_name'],
          [employeeFields.last_name, 'last_name'],
          [employeeFields.email, 'email'],
        ],
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: workday };
  };

  workdaySchema.get = async ({ id, employeeModel }) => {
    try {
      const workday = await Workday.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.start_date, 'start_date'],
          [fields.month_workday, 'month_workday'],
          [fields.total_workday, 'total_workday'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
        include: {
          model: employeeModel,
          as: 'employee',
          attributes: [
            [employeeFields._id, '_id'],
            [employeeFields.first_name, 'first_name'],
            [employeeFields.last_name, 'last_name'],
            [employeeFields.email, 'email'],
          ],
        },
      });
      if (workday) {
        return workday;
      }
      throw new APIError({
        message: 'Workday does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  workdaySchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Workday.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  return workdaySchema;
};


module.exports = {
  schema,
  fields,
};
