// 25/11/2022
const _ = require('lodash');
const httpStatus = require('http-status');
const db = require('../../../../config/mysql');
const { fields } = require('../models/student.model');
const { success: jsonSuccess } = require('../../../middlewares/success');
const APIError = require('../../../utils/APIError');

const Student = db.student;
const Enroll = db.enroll;

// Create and Save a new Student
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const dbData = {};
    _.forEach(data, (value, key) => {
      dbData[fields[key]] = value;
    });

    const student = Student.build({
      ...dbData,
    });
    const saved = await student.save();
    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    const student = await Enroll.list({ ...query });

    jsonSuccess(student, req, res);
  } catch (error) {
    next(error);
  }
};

exports.get = async (req, res, next) => {
  try {
    const { id } = req.params;
    const student = await Student.findById(id);
    if (!student) {
      throw new APIError({
        message: 'Student does not exist',
        status: httpStatus.NOT_FOUND,
      });
    }

    jsonSuccess(student, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    await Student.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};
