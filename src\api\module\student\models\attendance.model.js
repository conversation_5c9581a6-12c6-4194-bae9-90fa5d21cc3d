// 1/12/2022
const _ = require('lodash');
const { Op } = require('sequelize');
const lecturerFields = require('../../employee/models/employee.model').fields;
const classFields = require('./class.model').fields;
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const moment = require('moment');

const fields = {
  table: 'b320',
  _id: 'pb320',
  year: 'fh050',
  semester: 'fh025',
  lecturer: 'fm100',
  student: 'fn100',
  class: 'fb200',
  code: 'bv322',
  note: 'bl339',
  date: 'bd323',
  content: 'bv324',

  deleted_by: 'bl344',
  deleted_at: 'bl345',
  created_by: 'bl347',
  updated_by: 'bl349',
  updated_at: 'bl348',
  created_at: 'bl346',
};

const schema = (sequelize, DataTypes) => {
  const attendanceSchema = sequelize.define('Attendance', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.code]: {
      type: DataTypes.STRING(32),
      allowNull: false,
    },
    [fields.date]: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    [fields.note]: {
      type: DataTypes.STRING(512),
      defaultValue: null,
    },
    [fields.content]: {
      type: DataTypes.TEXT,
      defaultValue: null,
    },
    [fields.year]: {
      type: DataTypes.INTEGER,
      defaultValue: null,
    },
    [fields.semester]: {
      type: DataTypes.INTEGER,
      defaultValue: null,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Attendance = attendanceSchema;

  attendanceSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    lecturer = { [Op.not]: null },
    student = { [Op.not]: null },
    year = { [Op.not]: null },
    _class = { [Op.not]: null },
    semester = { [Op.not]: null },
    lecturerModel,
    classModel,
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const where = {
      [fields.deleted_at]: null,
      [fields.lecturer]: lecturer,
      [fields.year]: year,
      [fields.semester]: semester,
      [fields.student]: student,
      [fields.class]: _class,
    };
    const count = await Attendance.countItem({
      ...where,
    });
    const attendances = await Attendance.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.year, 'year'],
        [fields.semester, 'semester'],
        [fields.code, 'code'],
        [fields.student, 'student'],
        [fields.date, 'date'],
        [fields.content, 'content'],
        [fields.created_by, 'created_by'],
        [fields.created_at, 'created_at'],
      ],
      where: {
        ...where,
      },
      include: [
        {
          model: lecturerModel,
          as: 'lecturer',
          attributes: [
            [lecturerFields._id, '_id'],
            [lecturerFields.first_name, 'first_name'],
            [lecturerFields.last_name, 'last_name'],
            [lecturerFields.email, 'email'],
          ],
        },
        {
          model: classModel,
          as: 'class',
          attributes: [
            [classFields._id, '_id'],
            [classFields.name, 'name'],
            [classFields.name_vn, 'name_vn'],
          ],
        },
      ],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: attendances };
  };

  attendanceSchema.listForStudent = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    year = { [Op.not]: null },
    _class = [],
    semester = { [Op.not]: null },
    lecturerModel,
    classModel,
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const where = {
      [fields.deleted_at]: null,
      [fields.year]: year,
      [fields.semester]: semester,
      [fields.class]: { [Op.in]: _class },
    };
    const count = await Attendance.countItem({
      ...where,
    });
    const classes = await Attendance.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.year, 'year'],
        [fields.semester, 'semester'],
        [fields.code, 'code'],
        [fields.student, 'student'],
        [fields.date, 'date'],
        [fields.content, 'content'],
        [fields.created_by, 'created_by'],
        [fields.created_at, 'created_at'],
      ],
      where: {
        ...where,
      },
      include: [
        {
          model: lecturerModel,
          as: 'lecturer',
          attributes: [
            [lecturerFields._id, '_id'],
            [lecturerFields.first_name, 'first_name'],
            [lecturerFields.last_name, 'last_name'],
            [lecturerFields.email, 'email'],
          ],
        },
        {
          model: classModel,
          as: 'class',
          attributes: [
            [classFields._id, '_id'],
            [classFields.name, 'name'],
            [classFields.name_vn, 'name_vn'],
          ],
        },
      ],
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: classes };
  };

  attendanceSchema.report = async ({
    order_by = fields._id,
    order_way = 'desc',
    year = { [Op.not]: null },
    semester = { [Op.not]: null },
    lecturer,
  }) => {
    const report = await Attendance.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.year, 'year'],
        [fields.semester, 'semester'],
        [fields.date, 'date'],
        [fields.content, 'content'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.lecturer]: lecturer,
        [fields.year]: year,
        [fields.semester]: semester,
      },
      order: [
        [order_by, order_way],
      ],
    });

    return report;
  };

  attendanceSchema.countItem = async (query) => {
    const count = await Attendance.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  attendanceSchema.remove = async ({ id, email }) => {
    try {
      const result = await Attendance.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  attendanceSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await Attendance.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  attendanceSchema.get = async ({
    id,
    lecturerModel,
    classModel,
  }) => {
    try {
      const attendance = await Attendance.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.year, 'year'],
          [fields.semester, 'semester'],
          [fields.content, 'content'],
          [fields.date, 'date'],
          [fields.note, 'note'],
          [fields.code, 'code'],
          [fields.created_by, 'created_by'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
        include: [
          {
            model: lecturerModel,
            as: 'lecturer',
            attributes: [
              [lecturerFields._id, '_id'],
              [lecturerFields.first_name, 'first_name'],
              [lecturerFields.last_name, 'last_name'],
              [lecturerFields.email, 'email'],
            ],
          },
          {
            model: classModel,
            as: 'class',
            attributes: [
              [classFields._id, '_id'],
              [classFields.name, 'name'],
              [classFields.name_vn, 'name_vn'],
            ],
          },
        ],
      });
      if (attendance) {
        return attendance;
      }
      throw new APIError({
        message: 'Attendance does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  return attendanceSchema;
};

module.exports = {
  schema,
  fields,
};
