const Joi = require('joi');

module.exports = {

  // GET /v1article/number
  listArticleNumbers: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      type: Joi.string().Joi.regex(/^[a-fA-F0-9]{24}$/),
      sort: Joi.string(),
    }),
  },

  // POST /v1article/number
  createArticleNumber: {
    body: Joi.object({
      number: Joi.string().required(),
      type: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // PATCH /v1article/number/:id
  updateArticleNumber: {
    body: Joi.object({
      number: Joi.string(),
      type: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
    }),
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },

  // DELETE /v1article/number/:id
  deleteArticleNumber: {
    params: Joi.object({
      id: Joi.string().regex(/^[a-fA-F0-9]{24}$/).required(),
    }),
  },
};
