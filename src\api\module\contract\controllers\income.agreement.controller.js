const db = require('../../../../config/mysql');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { handler: errorHandler } = require('../../../middlewares/error');
const { fields } = require('../models/income.agreement.model');

const IncomeAgreement = db.incomeAgreement;
const Employee = db.employee;
const Contract = db.contract;

exports.load = async (req, res, next, id) => {
  try {
    const incomeAgreement = await IncomeAgreement.get({
      id,
      employeeModel: Employee,
      contractModel: Contract,
    });
    req.locals = { incomeAgreement };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

// Create and Save a new IncomeAgreement
exports.create = async (req, res, next) => {
  try {
    const data = req.body;
    const { user } = req;
    const incomeAgreement = IncomeAgreement.build({
      [fields.contract]: data.contract,
      [fields.employee]: data.employee,
      [fields.position_represent]: data.position_represent,
      [fields.income_agreement_id]: data.income_agreement_id,
      [fields.income_agreement_at]: data.income_agreement_at,
      [fields.start_at]: data.start_at,
      [fields.income_primary]: data.income_primary,
      [fields.income_other]: data.income_other,
      [fields.allowance_responsibility]: data.allowance_responsibility,
      [fields.allowance_gas]: data.allowance_gas,
      [fields.allowance_concurrently]: data.allowance_concurrently,
      [fields.allowance_seniority]: data.allowance_seniority,
      [fields.allowance_lunch]: data.allowance_lunch,
      [fields.signed_at]: data.signed_at,

      [fields.created_by]: user.email,
    });
    const saved = await incomeAgreement.save();
    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const { query } = req;
    const incomeAgreements = await IncomeAgreement.list({
      ...query,
      employeeModel: Employee,
      contractModel: Contract,
    });
    jsonSuccess(incomeAgreements, req, res);
  } catch (error) {
    next(error);
  }
};

exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.incomeAgreement, req, res);
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const { user } = req;
    await IncomeAgreement.patch({
      id: req.params.id,
      data: {
        ...req.body,
        updated_by: user.email,
      },
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};
