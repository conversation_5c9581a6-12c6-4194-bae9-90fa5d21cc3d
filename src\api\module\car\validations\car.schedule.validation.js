// 2/11/2021
const Joi = require('joi');
// const { opts } = require('../models/overtime.model');

module.exports = {

  // GET /v1/car/schedule
  listCarSchedules: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      order_by: Joi.string(),
      order_way: Joi.string(),
    }),
  },

  // POST /v1/car/schedule
  createCarSchedule: {
    body: Joi.object({
      employee: Joi.number().integer().required(),
      car: Joi.number().integer().required(),
      borrowed_at: Joi.date().required(),
      returned_at: Joi.date().greater(Joi.ref('borrowed_at')).required(),
      borrow_time: Joi.string().required(),
      return_time: Joi.string().required(),
      reason: Joi.string().required(),
      start_at: Joi.string().allow(''),
      end_at: Joi.string().allow(''),
      description: Joi.string().allow(''),
    }),
  },

  // PATCH /v1/car/schedule/:id
  updateCarSchedule: {
    body: Joi.object({
      employee: Joi.number().integer(),
      car: Joi.number().integer(),
      borrowed_at: Joi.date(),
      returned_at: Joi.date(),
      borrow_time: Joi.string(),
      return_time: Joi.string(),
      start_at: Joi.string().allow(''),
      end_at: Joi.string().allow(''),
      reason: Joi.string(),
      description: Joi.string().allow(''),
      is_approved: Joi.number(),
    }),
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

  // DELETE /v1/car/schedule/:id
  deleteCarSchedule: {
    params: Joi.object({
      id: Joi.number().required(),
    }),
  },

};
