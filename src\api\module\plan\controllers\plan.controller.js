
const { handler: error<PERSON><PERSON><PERSON> } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const Plan = require('../models/plan.model');

/**
 * Load plan and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const plan = await Plan.get(id);
    req.locals = { plan };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get plan
 * @public
 */
exports.get = async (req, res, next) => {
  try {
    jsonSuccess(req.locals.plan.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Create new plan
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user._id;
    const plan = new Plan(req.body);
    const saved = await plan.save();

    jsonSuccess(saved.transform(), req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing plan
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const plan = Object.assign(req.locals.plan, req.body);

  plan.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get plan list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await Plan.count(req.query);
    const plans = await Plan.list(req.query);
    const transformed = plans.map(plan => plan.transform());
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete plan
 * @public
 */
exports.remove = (req, res, next) => {
  const { plan } = req.locals;

  plan.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};

