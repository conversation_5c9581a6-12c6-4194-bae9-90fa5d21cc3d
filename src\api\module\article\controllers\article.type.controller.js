/* eslint-disable no-restricted-syntax */
const ArticleType = require('../models/article.type.model');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

/**
 * Load article type and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const articleType = await ArticleType.get(id);
    req.locals = { articleType };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get article type
 * @public
 */
exports.get = (req, res) => {
  jsonSuccess(req.locals.articleType.transform(), req, res);
};

/**
 * Create new article type
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    req.body.created_by = req.user._id;
    const category = await ArticleType.findOne({ title: req.body.title }).exec();
    if (category) {
      throw new APIError({
        message: 'ArticleType already exists',
        status: httpStatus.BAD_REQUEST,
      });
    } else {
      const articleType = new ArticleType(req.body);
      const saved = await articleType.save();
      jsonSuccess(saved.transform(), req, res);
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing article type
 * @public
 */
exports.update = (req, res, next) => {
  req.body.updated_by = req.user._id;
  const articleType = Object.assign(req.locals.articleType, req.body);

  articleType.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

/**
 * Get article type list
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const count = await ArticleType.count(req.query);
    const types = await ArticleType.list(req.query);
    const transformed = types.map(type => type.transform());

    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete article type
 * @public
 */
exports.remove = (req, res, next) => {
  const { articleType } = req.locals;

  articleType.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};
