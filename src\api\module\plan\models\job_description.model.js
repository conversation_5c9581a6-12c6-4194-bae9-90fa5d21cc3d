/* eslint-disable max-len */
// const mongoose = require('mongoose');
// const httpStatus = require('http-status');
// const { omitBy, isNil } = require('lodash');
// const APIError = require('../../../utils/APIError');

// const sexOpts = ['other', 'male', 'female'];

// /**
//  * jobDescription Schema
//  * @private
//  */
// const jobDescriptionSchema = new mongoose.Schema({
//   position: {
//     type: String,
//     trim: true,
//     required: true,
//   },
//   job_code: {
//     type: String,
//     trim: true,
//   },
//   department: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'Department',
//   },
//   supervisor: {
//     type: String,
//     trim: true,
//   },
//   address: {
//     type: String,
//     trim: true,
//   },
//   description_items: [{
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'JobDescriptionItem',
//   }],
//   purpose: {
//     type: String,
//     trim: true,
//   },
//   sex: {
//     type: String,
//     enum: sexOpts,
//   },
//   health: {
//     type: String,
//     trim: true,
//   },
//   age: {
//     type: String,
//     trim: true,
//   },
//   appearance: {
//     type: String,
//     trim: true,
//   },
//   education: {
//     type: String,
//     trim: true,
//   },
//   profession: {
//     type: String,
//     trim: true,
//   },
//   language: {
//     type: String,
//     trim: true,
//   },
//   computer_skill: {
//     type: String,
//     trim: true,
//   },
//   experience: {
//     type: String,
//     trim: true,
//   },
//   competence: {
//     type: String,
//     trim: true,
//   },
//   skill: {
//     type: String,
//     trim: true,
//   },
//   other_requirement: {
//     type: String,
//     trim: true,
//   },
//   outside: {
//     type: String,
//     trim: true,
//   },
//   inside: {
//     type: String,
//     trim: true,
//   },
//   condition: {
//     type: String,
//     trim: true,
//   },
//   is_active: {
//     type: Boolean,
//     default: true,
//   },
//   created_by: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'User',
//   },
//   updated_by: {
//     type: mongoose.Schema.Types.ObjectId,
//     ref: 'User',
//   },
// }, {
//   timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
// });

// /**
//  * Methods
//  */
// jobDescriptionSchema.method({
//   transform() {
//     const transformed = {};
//     const fields = [
//       '_id',
//       'position',
//       'job_code',
//       'address',
//       'department',
//       'created_by',
//       'created_at',
//       'is_active',
//     ];
//     fields.forEach((field) => {
//       transformed[field] = this[field];
//     });

//     return transformed;
//   },
// });

// /**
//  * Statics
//  */
// jobDescriptionSchema.statics = {
//   sexOpts,
//   /**
//    * Get jobDescription
//    *
//    * @param {ObjectId} id - The objectId of jobDescription.
//    * @returns {Promise<JobDescription, APIError>}
//    */
//   async get(id) {
//     try {
//       let jobDescription;

//       if (mongoose.Types.ObjectId.isValid(id)) {
//         jobDescription = await this.findById(id, '_id position department description_items job_code supervisor address purpose sex health age appearance education profession language computer_skill experience competence skill other_requirement outside inside condition')
//           .populate('department', '_id name')
//           .populate('description_items', '_id job_description job_output')
//           .exec();
//       }
//       if (jobDescription) {
//         return jobDescription;
//       }

//       throw new APIError({
//         message: 'JobDescription does not exist',
//         status: httpStatus.NOT_FOUND,
//       });
//     } catch (error) {
//       throw error;
//     }
//   },

//   /**
//    * List jobDescription in descending order of 'createdAt' timestamp.
//    *
//    * @param {number} skip - Number of jobDescription to be skipped.
//    * @param {number} limit - Limit number of jobDescription to be returned.
//    * @returns {Promise<User[]>}
//    */
//   list({
//     page = 1,
//     perPage = 30,
//     sort,
//     is_active = true,
//     department,
//   }) {
//     page = parseInt(page, 10);
//     perPage = parseInt(perPage, 10);
//     const options = omitBy({
//       is_active,
//       department,
//     }, isNil);
//     const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
//     const result = this.find(options)
//       .populate('department', '_id name')
//       .populate('created_by', '_id name')
//       .sort(sortOpts);
//     if (perPage > -1) {
//       result.skip(perPage * (page - 1)).limit(perPage);
//     }
//     return result.exec();
//   },

//   /**
//    * Count jobDescription.
//    * @returns {Promise<Number>}
//    */
//   async count({
//     is_active = true,
//     department,
//   }) {
//     const options = omitBy({
//       is_active,
//       department,
//     }, isNil);
//     return this.find(options).count();
//   },
// };

// /**
//  * @typedef JobDescription
//  */
// module.exports = mongoose.model('JobDescription', jobDescriptionSchema);
