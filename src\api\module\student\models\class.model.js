// 28/11/2022
const { Op } = require('sequelize');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');

const fields = {
  table: 'b200',
  _id: 'pb200', // ID
  year: 'fh050',
  lecturer: 'fm100',
  name: 'bv202',
  name_vn: 'bv202_vn',
  code: 'bv201',
  school: 'fn450',
  semester: 'fh025',
  is_finish: 'bn220', // Đ<PERSON>h dấu đã nhập điểm

  deleted_by: 'bl244',
  deleted_at: 'bl245',
  created_by: 'bl247',
  updated_by: 'bl249',
  updated_at: 'bl248',
  created_at: 'bl246',
};

const schema = (sequelize, DataTypes) => {
  const classSchema = sequelize.define('Class', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.name]: {
      type: DataTypes.STRING(150),
      allowNull: false,
    },
    [fields.name_vn]: {
      type: DataTypes.STRING(300),
      defaultValue: null,
    },
    [fields.code]: {
      type: DataTypes.STRING(32),
      defaultValue: null,
    },
    [fields.is_finish]: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const Class = classSchema;

  classSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    lecturer = { [Op.not]: null },
    year = { [Op.not]: null },
    semester = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await Class.countItem({
      [fields.deleted_at]: null,
      [fields.lecturer]: lecturer,
      [fields.year]: year,
      [fields.semester]: semester,
    });
    const classes = await Class.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.year, 'year'],
        [fields.lecturer, 'lecturer'],
        [fields.name, 'name'],
        [fields.name_vn, 'name_vn'],
        [fields.code, 'code'],
        [fields.school, 'school'],
        [fields.semester, 'semester'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.lecturer]: lecturer,
        [fields.year]: year,
        [fields.semester]: semester,
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: classes };
  };

  classSchema.countItem = async (query) => {
    const count = await Class.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  classSchema.get = async (id) => {
    try {
      const _class = await Class.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.name, 'name'],
          [fields.name_vn, 'name_vn'],
          [fields.lecturer, 'lecturer'],
          [fields.code, 'code'],
          [fields.year, 'year'],
          [fields.semester, 'semester'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      if (_class) {
        return _class;
      }
      throw new APIError({
        message: 'Class does not exist',
        status: httpStatus.NOT_FOUND,
      });
    } catch (error) {
      throw error;
    }
  };

  return classSchema;
};

module.exports = {
  schema,
  fields,
};
