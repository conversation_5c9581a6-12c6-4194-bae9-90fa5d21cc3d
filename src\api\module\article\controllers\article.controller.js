/* eslint-disable no-await-in-loop */
const moment = require('moment');
const httpStatus = require('http-status');
const XLSX = require('xlsx');
const multer = require('multer');
const APIError = require('../../../utils/APIError');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const db = require('../../../../config/mysql');
const Article = require('../models/article.model');
const { getFile } = require('../../../../config/google');
const { rolesVar, uploadDir } = require('../../../../config/vars');

const UserPermission = db.userPermission;
const Employee = db.employee;
const Department = db.department;
const departmentFields = (require('../../department/models/department.model')).fields;
const employeeFields = (require('../../employee/models/employee.model')).fields;

exports.load = async (req, res, next, id) => {
  try {
    const article = await Article.get(id);
    req.locals = { article };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.get = async (req, res, next) => {
  try {
    const { status } = req.locals.article;
    if (!rolesVar.admin.includes(req.user.role) && status === 'private' && !req.locals.article.allow_users.includes(req.user._id)) {
      throw new APIError({
        message: 'Article does not exist',
        status: httpStatus.NOT_FOUND,
      });
    }
    if (req.locals.article.type === 'document') {
      const fileInfo = [];
      for (let i = 0; i < req.locals.article.attachment.length; i += 1) {
        const item = req.locals.article.attachment[i];
        const info = await getFile(item);
        fileInfo.push(info);
      }
      const allow_users = [];
      for (let i = 0; i < req.locals.article.allow_users.length; i += 1) {
        const item = req.locals.article.allow_users[i];
        const user = await Employee.findOne({
          attributes: [
            [employeeFields._id, '_id'],
            [employeeFields.last_name, 'last_name'],
            [employeeFields.first_name, 'first_name'],
          ],
          where: {
            [employeeFields._id]: item,
            [employeeFields.deleted_at]: null,
          },
          include: [
            {
              model: Department,
              as: 'department',
              attributes: [
                [departmentFields._id, '_id'],
                [departmentFields.name, 'name'],
                [departmentFields.name_vn, 'name_vn'],
                [departmentFields.code, 'code'],
              ],
            },
          ],
        });
        allow_users.push(user);
      }
      jsonSuccess(Object.assign(
        req.locals.article.transform(),
        { allow_users },
        { google_drive_info: fileInfo },
      ), req, res);
    } else {
      jsonSuccess(Object.assign(req.locals.article, req.locals.article.transform()), req, res);
    }
  } catch (error) {
    next(error);
  }
};

exports.create = async (req, res, next) => {
  try {
    const { user } = req;
    const { folder } = req.body;
    const { number } = req.body;
    if (number) {
      const checkExistNumber = await Article.count({ number });
      if (checkExistNumber > 0) {
        throw new APIError({
          message: 'Số hiệu văn bản đã tồn tại',
          status: httpStatus.BAD_REQUEST,
        });
      }
    }

    if (rolesVar.hr_dept.includes(user.role)) {
      req.body.created_by = req.user.email;
      if (!req.body.public_date) {
        req.body.public_date = new Date();
      }
      if (req.body.folder === '') {
        req.body.folder = null;
      }
      const article = new Article(req.body);
      const saved = await article.save();

      jsonSuccess(saved.transform(), req, res);
    } else {
      const userPermission = await UserPermission.list({
        // permission_ui_function_code: { [Op.startsWith]: 'FUNC_POST_DOCUMENT_' },
        permission_ui_function_code: `FUNC_POST_DOCUMENT_${folder}`,
        employeeModel: Employee,
        employee: user._id,
      });
      if (userPermission.total > 0) {
        req.body.created_by = req.user.email;
        if (!req.body.public_date) {
          req.body.public_date = new Date();
        }
        if (req.body.folder === '') {
          req.body.folder = null;
        }
        const article = new Article(req.body);
        const saved = await article.save();

        jsonSuccess(saved.transform(), req, res);
      } else {
        throw new APIError({
          message: 'You don\'t have permission',
          status: httpStatus.BAD_REQUEST,
        });
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.update = (req, res, next) => {
  req.body.updated_by = req.user.email;
  if (req.body.folder === '') {
    req.body.folder = null;
  }
  const article = Object.assign(req.locals.article, req.body);

  article.save()
    .then(saved => jsonSuccess(saved.transform(), req, res))
    .catch(e => next(e));
};

exports.list = async (req, res, next) => {
  try {
    // let count = await Article.count(req.query);
    const { user } = req;
    req.query.user = user._id;
    req.query.role = user.role;
    const count = await Article.count(req.query);
    const articles = await Article.list(req.query);
    // const transformed = [];
    const transformed = articles.map(article => article.transform());
    // for (let i = 0; i < articles.length; i += 1) {
    //   const article = articles[i];
    //   if (article.status === 'public') {
    //     transformed.push(article.transform());
    //   } else if ((article.allow_users && article.allow_users.includes(req.user._id))
    //   || (parseInt(article.created_by, 10) === req.user._id)) {
    //     transformed.push(article.transform());
    //   } else {
    //     count -= 1;
    //   }
    // }
    // jsonSuccess({ total: count, docs: transformed }, req, res);
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = (req, res, next) => {
  const { article } = req.locals;

  article.remove()
    .then(() => jsonSuccess({}, req, res))
    .catch(e => next(e));
};


exports.schedule = async (req, res, next) => {
  try {
    const count = await Article.count({ type: 'schedule', ...req.query });
    const articles = await Article.list({ type: 'schedule', ...req.query });
    const transformed = articles.map(article => article.transform());
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.public = async (req, res, next) => {
  try {
    const count = await Article.count({ status: 'public', ...req.query });
    const articles = await Article.list({ status: 'public', ...req.query });
    const transformed = articles.map(article => article.transform());
    jsonSuccess({ total: count, docs: transformed }, req, res);
  } catch (error) {
    next(error);
  }
};

exports.importFromExcel = async (req, res, next) => {
  try {
    const func = multer.diskStorage({
      // multers disk storage settings
      destination: (rq, file, cb) => {
        cb(null, uploadDir.tmp);
      },
      filename: (rq, file, cb) => {
        const datetimestamp = Date.now();
        cb(null, `${file.fieldname}-${datetimestamp}.${file.originalname.split('.')[file.originalname.split('.').length - 1]}`);
      },
    });

    const upload = multer({
      storage: func,
    }).single('file');

    upload(req, res, async (err) => {
      if (err) {
        res.json({
          error_code: 1,
          err_desc: err,
        });
      }

      const workbook = XLSX.readFile(req.file.path, {
        cellDates: true,
      });
      const worksheet = XLSX.utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]]);
      for (let i = 0; i < worksheet.length; i += 1) {
        const item = worksheet[i];
        if (item.STT >= 1) {
          let publicDate = null;
          if (item['NGÀY THÁNG']) {
            publicDate = moment(new Date(item['NGÀY THÁNG'])).add(1, 'hours').format('YYYY-MM-DD');
          }
          const article = new Article({
            title: item['TRÍCH YẾU NỘI DUNG'],
            number: item['SỐ, KÝ HIỆU'],
            sign: item['NGƯỜI KÝ'],
            public_date: publicDate,
            created_at: publicDate,
            document_type: req.body.document_type,
            created_by: req.user.email,
            type: 'document',
            folder: req.body.folder,
            original: 'Văn bản',
          });
          await article.save();
        }
      }
      jsonSuccess({ worksheet, document_type: req.body.document_type }, req, res);
    });
  } catch (error) {
    next(error);
  }
};
