const multer = require('multer');
const fs = require('fs');
const path = require('path');
const moment = require('moment-timezone');
const sharp = require('sharp');

const { uploadDir } = require('../../../../config/vars');
const { success: jsonSuccess } = require('../../../middlewares/success');
const common = require('../../../services/common');
const { uploadFile, publicFile } = require('../../../../config/google');
const ArticleFolder = require('../../article/models/article.folder.model');

function resize(fileSource, fileTarget, width, height) {
  return new Promise((resolve, reject) => {
    sharp(fileSource)
      .resize(width, height)
      .toFile(fileTarget, (err, result) => {
        if (err) reject(err);
        resolve(result);
      });
  });
}

/**
 * Upload avatar
 * @public
 */
exports.avatar = async (req, res, next) => {
  try {
    const func = multer.diskStorage({
      // multers disk storage settings
      destination: (rq, file, cb) => {
        cb(null, uploadDir.tmp);
      },
      filename: (rq, file, cb) => {
        const datetimestamp = Date.now();
        cb(null, `${file.fieldname}-${datetimestamp}.${file.originalname.split('.')[file.originalname.split('.').length - 1]}`);
      },
    });

    const upload = multer({
      storage: func,
    }).single('file');

    upload(req, res, async (err) => {
      if (err) {
        res.json({
          error_code: 1,
          err_desc: err,
        });
      }

      const now = moment();
      const year = now.format('YYYY');
      const month = now.format('MM');
      const day = now.format('DD');

      const type = req.body.type;
      switch (type) {
        case 'avatar': {
          const ext = path.extname(req.file.filename);
          const filename = path.basename(req.file.filename, ext);
          await common.move(`${uploadDir.tmp}/${req.file.filename}`, `${uploadDir.avatar}/${req.file.filename}`);
          await resize(`${uploadDir.avatar}/${req.file.filename}`, `${uploadDir.avatar}/${filename}-32x32${ext}`, 32, 32);
          req.query.name = req.file.filename;
          req.file.filename = `avatar/${req.file.filename}`;
          req.file.directory = 'avatar';
          break;
        }
        case 'post': {
          let dir = `${uploadDir.post}/${year}`;
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir);
          }

          dir = `${dir}/${month}`;
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir);
          }

          dir = `${dir}/${day}`;
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir);
          }
          const ext = path.extname(req.file.filename);
          const filename = path.basename(req.file.filename, ext);
          await common.move(`${uploadDir.tmp}/${req.file.filename}`, `${dir}/${req.file.filename}`);
          await resize(`${dir}/${req.file.filename}`, `${dir}/${filename}-240x150${ext}`, 240, 150);
          await resize(`${dir}/${req.file.filename}`, `${dir}/${filename}-24x15${ext}`, 24, 15);
          req.query.name = req.file.filename;
          req.file.filename = `post/${year}/${month}/${day}/${req.file.filename}`;
          req.file.directory = `post/${year}/${month}/${day}`;
          break;
        }
        case 'post_content':
        {
          let dir = `${uploadDir.post_content}/${year}`;
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir);
          }

          dir = `${dir}/${month}`;
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir);
          }

          dir = `${dir}/${day}`;
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir);
          }

          await common.move(`${uploadDir.tmp}/${req.file.filename}`, `${dir}/${req.file.filename}`);
          req.query.name = req.file.filename;
          req.file.filename = `post_content/${year}/${month}/${day}/${req.file.filename}`;
          req.file.directory = `post_content/${year}/${month}/${day}`;
          break;
        }

        default:
          break;
      }
      req.query.directory = req.file.directory;
      req.query.file = req.file.filename;
      req.query.size = req.file.size;
      req.query.mimetype = req.file.mimetype;
      req.query.type = req.body.type;
      jsonSuccess(req.file, req, res);
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Upload file to google drive
 * @public
 */
exports.uploadDocument = async (req, res, next) => {
  try {
    const storage = multer.diskStorage({
      // multers disk storage settings
      destination: (rq, file, cb) => {
        cb(null, uploadDir.tmp);
      },
      filename: (rq, file, cb) => {
        const datetimestamp = Date.now();
        const now = moment();
        const year = now.format('YYYY');
        const month = now.format('MM');
        const day = now.format('DD');
        cb(null, `[${year}-${month}-${day}][${datetimestamp}]${file.originalname}`);
      },
    });

    const upload = multer({
      storage,
    }).single('file');

    upload(req, res, async (err) => {
      if (err) {
        res.json({
          error_code: 1,
          err_desc: err,
        });
      }

      let uploadData = {
        name: req.file.filename,
        filePath: `${uploadDir.tmp}/${req.file.filename}`,
      };

      if (req.body.parent) {
        const parentFolder = await ArticleFolder.get(req.body.parent);

        uploadData = {
          ...uploadData,
          parent: parentFolder.drive_id,
        };
      }
      // upload lên drive (private)->public
      const uploaded = await uploadFile(uploadData);
      const publicStatus = await publicFile(uploaded.id);
      await fs.unlinkSync(`./${req.file.path}`);

      jsonSuccess({
        name: uploaded.name,
        drive_id: uploaded.id,
        public_status: publicStatus,
      }, req, res);
    });
  } catch (error) {
    next(error);
  }
};

exports.externalImage = async (req, res, next) => {
  try {
    // eslint-disable-next-line no-shadow
    const fileFilter = (req, file, cb) => {
      if (
        file.mimetype === 'image/png' ||
        file.mimetype === 'image/jpg' ||
        file.mimetype === 'image/jpeg'
      ) {
        cb(null, true);
      } else {
        cb(new Error('File format should be PNG,JPG,JPEG'), false); // if validation failed then generate error
      }
    };

    const func = multer.diskStorage({
      // multers disk storage settings
      destination: (rq, file, cb) => {
        cb(null, uploadDir.tmp);
      },
      filename: (rq, file, cb) => {
        const datetimestamp = Date.now();
        cb(null, `${file.fieldname}-${datetimestamp}.${file.originalname.split('.')[file.originalname.split('.').length - 1]}`);
      },
    });

    const upload = multer({
      storage: func,
      fileFilter,
    }).single('file');

    upload(req, res, async (err) => {
      if (err) {
        res.json({
          error_code: 1,
          err_desc: err.message,
        });
      }

      const now = moment();
      const year = now.format('YYYY');
      const month = now.format('MM');
      const day = now.format('DD');

      let dir = `${uploadDir.external_image}/${year}`;
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir);
      }

      dir = `${dir}/${month}`;
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir);
      }

      dir = `${dir}/${day}`;
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir);
      }
      // const ext = path.extname(req.file.filename);
      // const filename = path.basename(req.file.filename, ext);
      // await resize(`${dir}/${req.file.filename}`, `${dir}/${filename}-240x150${ext}`, 240, 150);
      if (req.file) {
        if (req.file.size > 2097152) {
          fs.unlinkSync(`${uploadDir.tmp}/${req.file.filename}`);
          res.json({
            error_code: 1,
            err_desc: 'The file is too large for the destination (less than 2mb)',
          });
        } else {
          await common.move(`${uploadDir.tmp}/${req.file.filename}`, `${dir}/${req.file.filename}`);
          req.query.name = req.file.filename;
          req.file.filename = `external_image/${year}/${month}/${day}/${req.file.filename}`;
          req.file.directory = `external_image/${year}/${month}/${day}`;
          req.query.directory = req.file.directory;
          req.query.file = req.file.filename;
          req.query.size = req.file.size;
          req.query.mimetype = req.file.mimetype;
          req.query.type = req.body.type;
          jsonSuccess(req.file, req, res);
        }
      }
    });
  } catch (error) {
    next(error);
  }
};
